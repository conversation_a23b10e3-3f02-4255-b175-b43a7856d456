{"skeleton": {"hash": "Om0hPlqZfVO5mxKlHDziC0m4dmM", "spine": "3.8.99", "x": -109.71, "y": -13.44, "width": 175.27, "height": 186.3}, "bones": [{"name": "root"}, {"name": "all", "parent": "root", "scaleX": 0.67, "scaleY": 0.67}, {"name": "xiaoniu2", "parent": "all", "x": -5, "y": 106}, {"name": "honghaier2", "parent": "xiaoniu2", "x": -4.62, "y": 1.1}, {"name": "honghaier5", "parent": "honghaier2", "length": 16.55, "rotation": -88.98, "x": -0.84, "y": -0.34}, {"name": "honghaier5b", "parent": "honghaier5", "length": 16.55, "x": 16.55}, {"name": "honghaier5c", "parent": "honghaier5b", "length": 16.55, "x": 16.55}, {"name": "hong<PERSON>er5d", "parent": "honghaier5c", "length": 16.55, "x": 16.55}, {"name": "honghaier6", "parent": "xiaoniu2", "length": 27.88, "rotation": 96.69, "x": -0.05, "y": 0.56}, {"name": "honghaier7", "parent": "honghaier6", "length": 32, "rotation": -12.59, "x": 27.88}, {"name": "honghaier0", "parent": "honghaier7", "length": 39.34, "rotation": 8.65, "x": 31.93, "y": 0.8}, {"name": "honghaier1", "parent": "honghaier7", "length": 36.71, "rotation": -141.22, "x": 26.39, "y": -15.08}, {"name": "hong<PERSON>er4", "parent": "honghaier1", "length": 30.6, "rotation": -19.01, "x": 36.71}, {"name": "honghaier8", "parent": "hong<PERSON>er4", "length": 16.94, "rotation": -16.41, "x": 30.28, "y": 0.27}, {"name": "honghaier9", "parent": "honghaier8", "length": 8.69, "rotation": -19.03, "x": 16.94}, {"name": "honghaier10", "parent": "honghaier9", "length": 6.47, "rotation": -13.97, "x": 8.69}, {"name": "honghaier12", "parent": "honghaier7", "length": 26.84, "rotation": 157.01, "x": 23.22, "y": 10.11}, {"name": "honghaier13", "parent": "honghaier12", "length": 26.44, "rotation": 5.43, "x": 26.84}, {"name": "honghaier14", "parent": "honghaier13", "length": 15.47, "rotation": 28.35, "x": 26.44}, {"name": "honghaier11", "parent": "honghaier14", "length": 127.64, "rotation": -66.07, "x": 14.91, "y": 0.05}, {"name": "honghaier16", "parent": "xiaoniu2", "length": 57.72, "rotation": -97.19, "x": -8.7, "y": 2.26}, {"name": "honghaier<PERSON>", "parent": "honghaier16", "length": 44.48, "rotation": 14.91, "x": 57.32, "y": -0.8}, {"name": "leg1", "parent": "all", "x": -15.76, "y": 7.37}, {"name": "honghaier18", "parent": "leg1", "length": 19.3, "rotation": -162.99}, {"name": "hong<PERSON><PERSON><PERSON>", "parent": "xiaoniu2", "length": 61.55, "rotation": -87.68, "x": 11.22, "y": 3.01}, {"name": "hong<PERSON>er<PERSON>", "parent": "hong<PERSON><PERSON><PERSON>", "length": 54.05, "rotation": 11.27, "x": 61.55}, {"name": "leg2", "parent": "all", "x": 21.35, "y": -4.87}, {"name": "honghaier21", "parent": "leg2", "length": 16.09, "rotation": -144.24, "y": 0.25}, {"name": "honghaier15", "parent": "honghaier2", "length": 10.81, "rotation": -106.2, "x": -7.41, "y": -9.13}, {"name": "honghaier22", "parent": "honghaier15", "length": 9.17, "rotation": -7.29, "x": 10.81}, {"name": "honghaier23", "parent": "honghaier22", "length": 7.71, "rotation": -0.96, "x": 9.17}, {"name": "bone2", "parent": "honghaier2", "length": 10.79, "rotation": -34.49, "x": 4.15, "y": -9.41}, {"name": "bone2b", "parent": "bone2", "length": 10.79, "x": 10.79}, {"name": "bone2c", "parent": "bone2b", "length": 10.79, "x": 10.79}, {"name": "xiaoniu3", "parent": "xiaoniu2", "x": 2.01, "y": 16.39}, {"name": "honghaier_0009", "parent": "xiaoniu3", "x": -5.64, "y": 121.18}, {"name": "honghaier_0000", "parent": "xiaoniu3", "x": 16.03, "y": -0.75}, {"name": "honghaier_0", "parent": "xiaoniu3", "x": -22.14, "y": -5.34}, {"name": "xiaoniu4", "parent": "xiaoniu3", "x": -39.2, "y": 108.56}, {"name": "xiaoniu5", "parent": "xiaoniu3", "x": -62.61, "y": 78.43}, {"name": "xiaoniu6", "parent": "xiaoniu3", "x": -68.27, "y": 40.82}, {"name": "xiaoniu7", "parent": "xiaoniu3", "x": 28.35, "y": 128.6}, {"name": "xiaoniu8", "parent": "xiaoniu3", "x": 65.95, "y": 123.18}, {"name": "xiaoniu9", "parent": "xiaoniu3", "x": 78.57, "y": 87.03}, {"name": "xiaoniu10", "parent": "xiaoniu3", "x": 72.23, "y": 37.05}, {"name": "honghaier_0003", "parent": "honghaier_0000", "length": 33.09, "rotation": -50.15, "x": 0.59, "y": -0.36}, {"name": "honghaier_3", "parent": "honghaier_0003", "length": 21.22, "rotation": 11.49, "x": 33.09}, {"name": "honghaier_4", "parent": "honghaier_3", "length": 21.4, "rotation": -7.59, "x": 21.22}, {"name": "honghaier_5", "parent": "honghaier_4", "length": 19.47, "rotation": -18.93, "x": 21.08, "y": 0.31}, {"name": "hong<PERSON>er_6", "parent": "honghaier_5", "length": 16.18, "rotation": -20.12, "x": 19.47}, {"name": "honghaier_7", "parent": "hong<PERSON>er_6", "length": 18.29, "rotation": -41.85, "x": 16.18}, {"name": "honghaier_8", "parent": "honghaier_7", "length": 19.76, "rotation": -24.86, "x": 18.29}, {"name": "honghaier_0011", "parent": "honghaier_0", "length": 24.25, "rotation": -120.07, "x": -0.13, "y": -0.73}, {"name": "hong<PERSON>er_11", "parent": "honghaier_0011", "length": 20.25, "rotation": -33.37, "x": 24.25}, {"name": "hong<PERSON>er_12", "parent": "hong<PERSON>er_11", "length": 18.87, "rotation": 11.78, "x": 20.25}, {"name": "hong<PERSON>er_13", "parent": "hong<PERSON>er_12", "length": 17.37, "rotation": 44.35, "x": 18.87}, {"name": "hong<PERSON>er_14", "parent": "hong<PERSON>er_13", "length": 14.85, "rotation": 37.68, "x": 17.84, "y": -0.16}, {"name": "hong<PERSON>er_15", "parent": "hong<PERSON>er_14", "length": 14.37, "rotation": 13.38, "x": 14.85}, {"name": "hong<PERSON>er_16", "parent": "hong<PERSON>er_15", "length": 15.63, "rotation": 22.94, "x": 14.37}], "slots": [{"name": "honghaier_0011", "bone": "honghaier_0011", "attachment": "honghaier_0011"}, {"name": "honghaier9", "bone": "honghaier12", "attachment": "honghaier9"}, {"name": "honghaier7", "bone": "honghaier11", "attachment": "honghaier8"}, {"name": "honghaier6", "bone": "honghaier16", "attachment": "honghaier6"}, {"name": "honghaier5", "bone": "hong<PERSON><PERSON><PERSON>", "attachment": "honghaier5"}, {"name": "hong<PERSON>er4", "bone": "honghaier5", "attachment": "hong<PERSON>er4"}, {"name": "honghaier3", "bone": "honghaier2", "attachment": "honghaier3"}, {"name": "honghaier2", "bone": "honghaier6", "attachment": "honghaier2"}, {"name": "honghaier_0003", "bone": "honghaier_0003", "attachment": "honghaier_0003"}, {"name": "honghaier1", "bone": "honghaier1", "attachment": "honghaier1"}, {"name": "honghaier0", "bone": "honghaier0", "attachment": "honghaier0"}, {"name": "honghaier_0009", "bone": "honghaier_0011", "attachment": "honghaier_0009"}, {"name": "honghaier_0000", "bone": "honghaier_0000", "attachment": "honghaier_0000"}], "ik": [{"name": "leg1", "bones": ["honghaier16", "honghaier<PERSON>"], "target": "leg1"}, {"name": "leg2", "order": 1, "bones": ["hong<PERSON><PERSON><PERSON>", "hong<PERSON>er<PERSON>"], "target": "leg2"}], "skins": [{"name": "default", "attachments": {"honghaier0": {"honghaier0": {"x": 22.48, "y": 1.12, "rotation": -92.74, "width": 42, "height": 57}}, "honghaier1": {"honghaier1": {"type": "mesh", "uvs": [0.64167, 0.11635, 0.8941, 0.17059, 0.93481, 0.29584, 1, 0.42755, 1, 0.61213, 0.95833, 0.70347, 0.93757, 0.80665, 0.90068, 0.85712, 0.85917, 0.89332, 0.78306, 0.92843, 0.62625, 0.96463, 0.41639, 0.95695, 0.34121, 0.8979, 0.37729, 0.86862, 0.42716, 0.82823, 0.47174, 0.81056, 0.45688, 0.76007, 0.38684, 0.83025, 0.32423, 0.8565, 0.23671, 0.85387, 0.20124, 0.7695, 0.23027, 0.69434, 0.38182, 0.5931, 0.36247, 0.53941, 0.21414, 0.48725, 0.18835, 0.42129, 0.27541, 0.34613, 0.14643, 0.22954, 0, 0.12063, 0, 0, 0.29424, 0, 0.51123, 0.9129, 0.58751, 0.86797, 0.54574, 0.89044, 0.62566, 0.82477, 0.66017, 0.79021, 0.6638, 0.70207, 0.66198, 0.62257, 0.58751, 0.46877, 0.51792, 0.3435, 0.29623, 0.18011, 0.11513, 0.04345], "triangles": [32, 14, 15, 33, 14, 32, 13, 14, 33, 31, 13, 33, 12, 13, 31, 11, 12, 31, 10, 32, 9, 33, 32, 10, 31, 33, 10, 11, 31, 10, 15, 16, 35, 34, 15, 35, 7, 35, 6, 34, 35, 7, 32, 15, 34, 8, 34, 7, 9, 32, 34, 8, 9, 34, 5, 36, 37, 4, 5, 37, 36, 16, 22, 35, 16, 36, 5, 35, 36, 6, 35, 5, 38, 39, 2, 3, 38, 2, 38, 24, 25, 25, 26, 38, 38, 26, 39, 23, 24, 38, 22, 23, 38, 38, 3, 4, 37, 38, 4, 22, 38, 37, 36, 22, 37, 21, 22, 16, 20, 21, 16, 16, 17, 20, 19, 20, 17, 18, 19, 17, 41, 29, 30, 28, 29, 41, 40, 30, 0, 41, 30, 40, 28, 41, 40, 27, 28, 40, 39, 40, 0, 39, 0, 1, 39, 1, 2, 26, 27, 40, 39, 26, 40], "vertices": [2, 11, 20.79, 17.55, 0.98308, 12, -20.77, 11.41, 0.01692, 3, 11, 32.2, 24.9, 0.82171, 12, -12.38, 22.08, 0.17781, 13, -47.08, 8.86, 0.00048, 3, 11, 44.12, 19.58, 0.42956, 12, 0.62, 20.92, 0.54056, 13, -34.28, 11.43, 0.02988, 3, 11, 57.24, 14.9, 0.04516, 12, 14.56, 20.77, 0.73691, 13, -20.87, 15.22, 0.21793, 3, 12, 33.02, 16.21, 0.08451, 13, -1.87, 16.07, 0.91545, 14, -23.02, 9.06, 4e-05, 2, 13, 7.62, 14.45, 0.9405, 14, -13.52, 10.62, 0.0595, 3, 13, 18.28, 13.9, 0.51796, 14, -3.26, 13.58, 0.4792, 15, -14.88, 10.29, 0.00284, 3, 13, 23.55, 12.33, 0.24865, 14, 2.23, 13.81, 0.69901, 15, -9.6, 11.84, 0.05234, 3, 13, 27.37, 10.46, 0.10313, 14, 6.45, 13.29, 0.73551, 15, -5.38, 12.35, 0.16136, 3, 13, 31.14, 6.89, 0.01593, 14, 11.18, 11.15, 0.57385, 15, -0.27, 11.42, 0.41023, 2, 14, 17.48, 5.38, 0.07403, 15, 7.23, 7.34, 0.92597, 1, 15, 12.56, -1.49, 1, 2, 14, 16.22, -10.14, 0.01995, 15, 9.75, -8.02, 0.98005, 4, 12, 51.35, -19.74, 0.0005, 13, 25.87, -13.24, 0.00036, 14, 12.77, -9.61, 0.08845, 15, 6.27, -8.34, 0.91069, 4, 12, 47.9, -16.37, 0.0145, 13, 21.61, -10.99, 0.03233, 14, 8, -8.86, 0.36581, 15, 1.46, -8.77, 0.58737, 4, 12, 46.66, -13.81, 0.05148, 13, 19.69, -8.88, 0.12861, 14, 5.5, -7.5, 0.53998, 15, -1.29, -8.05, 0.27994, 4, 12, 41.43, -13.27, 0.37079, 13, 14.53, -9.84, 0.41744, 14, 0.93, -10.09, 0.20235, 15, -5.09, -11.66, 0.00941, 3, 12, 47.63, -18.34, 0.552, 13, 21.9, -12.95, 0.35555, 14, 8.92, -10.62, 0.09245, 3, 12, 49.52, -21.97, 0.56196, 13, 24.74, -15.9, 0.35147, 14, 12.56, -12.48, 0.08657, 3, 12, 48.23, -26.06, 0.56671, 13, 24.66, -20.19, 0.34907, 14, 13.89, -16.57, 0.08422, 3, 12, 39.38, -25.67, 0.59621, 13, 16.06, -22.31, 0.32837, 14, 6.44, -21.38, 0.07542, 3, 12, 32.2, -22.43, 0.67839, 13, 8.26, -21.24, 0.26907, 14, -1.28, -22.9, 0.05254, 3, 12, 23.86, -12.72, 0.96728, 13, -2.49, -14.28, 0.02933, 14, -13.71, -19.83, 0.00339, 2, 11, 49.96, -17.59, 0.00571, 12, 18.26, -12.32, 0.99429, 2, 11, 41.51, -20.78, 0.07558, 12, 11.3, -18.09, 0.92442, 2, 11, 35.11, -18.16, 0.18151, 12, 4.4, -17.69, 0.81849, 2, 11, 30.93, -10.37, 0.64784, 12, -2.09, -11.69, 0.35216, 1, 11, 17.41, -9.16, 1, 1, 11, 4.1, -9.1, 1, 1, 11, -6.34, -2.35, 1, 1, 11, 1.49, 9.76, 1, 2, 14, 14.59, -1.83, 0.00087, 15, 6.17, -0.35, 0.99913, 4, 12, 53.76, -9.72, 2e-05, 13, 25.35, -2.95, 1e-05, 14, 8.92, -0.05, 0.41012, 15, 0.23, 0, 0.58985, 3, 12, 55.51, -12.26, 1e-05, 14, 11.82, -1.1, 0.00261, 15, 3.3, -0.32, 0.99737, 1, 14, 4.09, 0.05, 1, 2, 13, 17.19, 0.25, 0.41561, 14, 0.16, 0.32, 0.58439, 2, 13, 8.11, 0.02, 1, 14, -8.35, -2.86, 0, 2, 12, 30.09, -0.12, 0.9023, 13, -0.06, -0.43, 0.0977, 2, 12, 13.84, 0.13, 0.99949, 13, -15.73, -4.78, 0.00051, 2, 11, 37.15, -0.24, 0.29288, 12, 0.49, -0.09, 0.70712, 1, 11, 17.12, -0.23, 1, 1, 11, 0.48, -0.04, 1], "hull": 31}}, "honghaier2": {"honghaier2": {"type": "mesh", "uvs": [0.91257, 0.14462, 1, 0.31309, 1, 0.38511, 0.84989, 0.60759, 0.84989, 0.86946, 0.51345, 1, 0.18998, 0.94031, 0.07184, 0.75462, 0.05327, 0.56678, 0, 0.34408, 0, 0.14849, 0.19119, 0.05554, 0.45771, 0.01757, 0.58105, 0, 0.76405, 0, 0.47231, 0.94052, 0.46171, 0.79722, 0.43518, 0.62487, 0.46701, 0.33633, 0.48823, 0.11557], "triangles": [3, 17, 18, 3, 18, 2, 1, 2, 18, 0, 1, 18, 17, 8, 18, 0, 19, 13, 0, 13, 14, 8, 9, 18, 18, 19, 0, 9, 11, 18, 9, 10, 11, 18, 11, 19, 19, 11, 12, 19, 12, 13, 6, 15, 5, 5, 15, 4, 6, 16, 15, 15, 16, 4, 6, 7, 16, 16, 3, 4, 7, 17, 16, 3, 16, 17, 7, 8, 17], "vertices": [1, 9, 31.77, -19.11, 1, 1, 9, 21.63, -24.2, 1, 2, 8, 39.21, -27.81, 0.00028, 9, 17.11, -24.67, 0.99972, 2, 8, 26.09, -19.32, 0.21706, 9, 2.46, -19.24, 0.78294, 2, 8, 9.71, -17.4, 0.81727, 9, -13.95, -20.94, 0.18273, 2, 8, 3.34, -1.07, 0.99962, 9, -23.72, -6.39, 0.00038, 1, 8, 8.81, 13.27, 1, 2, 8, 21.06, 17.31, 0.90477, 9, -10.43, 15.4, 0.09523, 2, 8, 32.91, 16.78, 0.41449, 9, 1.25, 17.47, 0.58551, 2, 8, 47.13, 17.58, 0.03718, 9, 14.95, 21.35, 0.96282, 2, 8, 59.37, 16.14, 4e-05, 9, 27.21, 22.62, 0.99996, 1, 9, 33.94, 14.47, 1, 1, 9, 37.58, 2.53, 1, 1, 9, 39.26, -3, 1, 1, 9, 40.13, -11.38, 1, 1, 8, 7.28, 0.37, 1, 2, 8, 16.31, -0.19, 0.99893, 9, -11.26, -2.71, 0.00107, 2, 8, 27.23, -0.24, 0.83913, 9, -0.58, -0.38, 0.16087, 1, 9, 17.65, 0.03, 1, 1, 9, 31.58, 0.49, 1], "hull": 15}}, "honghaier3": {"honghaier3": {"type": "mesh", "uvs": [0.80367, 0.28794, 0.86534, 0.32008, 0.88534, 0.44865, 0.91367, 0.52722, 0.94201, 0.64151, 0.96201, 0.71651, 1, 0.80539, 1, 0.85141, 0.94074, 0.88141, 0.83874, 0.92855, 0.72793, 0.91872, 0.67922, 0.86088, 0.63313, 0.81715, 0.57388, 0.78047, 0.41851, 0.8242, 0.3698, 0.77059, 0.3145, 0.67608, 0.30001, 0.72404, 0.26315, 0.81433, 0.22497, 0.88909, 0.19468, 0.92859, 0.14728, 1, 0.0881, 1, 0, 0.97013, 0.0441, 0.88566, 0.07076, 0.82117, 0.09876, 0.77555, 0.12422, 0.72784, 0.05547, 0.79213, 0, 0.80418, 0, 0.61372, 0.01379, 0.57336, 0.03347, 0.49691, 0.05347, 0.40691, 0.07547, 0.28262, 0.10347, 0.22691, 0.10347, 0.14977, 0.15147, 0, 0.71867, 0, 0.30172, 0.3207, 0.63547, 0.30999, 0.44547, 0.50686, 0.26172, 0.50151, 0.22547, 0.58186, 0.20047, 0.68767, 0.17047, 0.77472, 0.13922, 0.84168, 0.11297, 0.90061, 0.52422, 0.56713, 0.59672, 0.6274, 0.66797, 0.67561, 0.74047, 0.72651, 0.81672, 0.78142], "triangles": [8, 9, 52, 9, 10, 52, 10, 11, 52, 7, 8, 6, 11, 51, 52, 11, 12, 51, 5, 6, 8, 8, 52, 5, 5, 52, 4, 52, 51, 4, 4, 51, 3, 12, 50, 51, 12, 13, 50, 13, 49, 50, 13, 48, 49, 50, 49, 2, 51, 50, 3, 50, 2, 3, 40, 2, 49, 14, 15, 13, 15, 48, 13, 48, 16, 41, 48, 15, 16, 49, 48, 40, 48, 41, 40, 41, 39, 40, 20, 21, 47, 23, 24, 22, 21, 22, 47, 22, 24, 47, 19, 20, 46, 24, 25, 47, 20, 47, 46, 47, 25, 46, 46, 45, 19, 19, 45, 18, 25, 26, 46, 46, 26, 45, 45, 44, 18, 18, 44, 17, 29, 30, 28, 26, 27, 45, 27, 28, 30, 45, 27, 44, 44, 27, 31, 27, 30, 31, 17, 44, 16, 44, 43, 16, 44, 31, 43, 43, 42, 16, 16, 42, 41, 31, 32, 43, 43, 32, 42, 42, 39, 41, 32, 33, 42, 42, 33, 39, 2, 0, 1, 0, 2, 40, 39, 34, 35, 39, 38, 40, 37, 39, 36, 40, 38, 0, 38, 39, 37, 36, 39, 35, 39, 33, 34], "vertices": [4, 3, 25.84, 2.77, 0.9, 31, 10.98, 22.32, 0.04127, 32, 0.19, 22.32, 0.05279, 33, -10.6, 22.32, 0.00594, 4, 3, 29.54, 0.97, 0.7, 31, 15.05, 22.93, 0.09732, 32, 4.26, 22.93, 0.1725, 33, -6.53, 22.93, 0.03018, 4, 3, 30.74, -6.23, 0.6, 31, 20.11, 17.68, 0.06926, 32, 9.32, 17.68, 0.23237, 33, -1.46, 17.68, 0.09837, 4, 3, 32.44, -10.63, 0.06785, 31, 24.01, 15.02, 0.06606, 32, 13.22, 15.02, 0.44687, 33, 2.43, 15.02, 0.41922, 4, 3, 34.14, -17.03, 0.01476, 31, 29.03, 10.7, 0.00731, 32, 18.24, 10.7, 0.18857, 33, 7.45, 10.7, 0.78936, 4, 3, 35.34, -21.23, 0.00246, 31, 32.4, 7.92, 7e-05, 32, 21.61, 7.92, 0.04359, 33, 10.82, 7.92, 0.95387, 2, 32, 26.31, 5.11, 0.00033, 33, 15.52, 5.11, 0.99967, 1, 33, 16.98, 2.99, 1, 1, 33, 15, -0.41, 1, 1, 33, 11.45, -6.05, 1, 2, 32, 16.45, -9.36, 0.07988, 33, 5.66, -9.36, 0.92012, 4, 28, 12.19, 30.39, 0.00045, 31, 22.99, -8.35, 0.00306, 32, 12.2, -8.35, 0.32633, 33, 1.42, -8.35, 0.67015, 4, 28, 10.61, 27.05, 0.0069, 31, 19.33, -7.9, 0.05003, 32, 8.54, -7.9, 0.67632, 33, -2.25, -7.9, 0.26675, 4, 28, 9.63, 23.06, 0.04368, 31, 15.23, -8.22, 0.28788, 32, 4.45, -8.22, 0.64103, 33, -6.34, -8.22, 0.02741, 3, 28, 14.58, 14.79, 0.20389, 31, 8.94, -15.52, 0.63809, 32, -1.85, -15.52, 0.15802, 3, 28, 12.51, 11.15, 0.26851, 31, 4.83, -14.7, 0.63121, 32, -5.96, -14.7, 0.10028, 4, 28, 8.35, 6.49, 0.59388, 29, -3.26, 6.12, 0.07977, 31, -0.9, -12.21, 0.31503, 32, -11.69, -12.21, 0.01132, 4, 28, 11.18, 6.4, 0.47329, 29, -0.45, 6.4, 0.43375, 31, -0.1, -14.92, 0.09161, 32, -10.89, -14.92, 0.00135, 4, 28, 16.65, 5.69, 0.03855, 29, 5.07, 6.38, 0.91394, 30, -4.21, 6.31, 0.04301, 31, 0.94, -20.34, 0.00451, 2, 29, 9.82, 5.95, 0.53576, 30, 0.55, 5.96, 0.46424, 2, 29, 12.57, 5.16, 0.17313, 30, 3.32, 5.22, 0.82687, 1, 30, 8.14, 4.29, 1, 1, 30, 9.6, 1.05, 1, 1, 30, 10.27, -4.45, 1, 2, 29, 13.97, -4.08, 0.00149, 30, 4.87, -4, 0.99851, 2, 29, 10.02, -4.05, 0.30085, 30, 0.92, -4.04, 0.69915, 3, 28, 17.31, -4.39, 0.00155, 29, 7.01, -3.53, 0.8578, 30, -2.1, -3.57, 0.14064, 2, 28, 14.32, -3.67, 0.09538, 29, 3.95, -3.19, 0.90462, 2, 28, 18.93, -6.63, 0.09834, 29, 8.89, -5.54, 0.90166, 2, 28, 20.51, -9.63, 0.09009, 29, 10.84, -8.33, 0.90991, 3, 3, -22.38, -15.47, 0.00036, 28, 10.26, -12.61, 0.51663, 29, 1.06, -12.58, 0.48301, 3, 3, -21.56, -13.21, 0.0045, 28, 7.86, -12.44, 0.65169, 29, -1.35, -12.72, 0.34381, 3, 3, -20.38, -8.93, 0.04458, 28, 3.42, -12.5, 0.82875, 29, -5.74, -13.34, 0.12667, 3, 3, -19.18, -3.89, 0.18198, 28, -1.75, -12.76, 0.79723, 29, -10.84, -14.25, 0.02079, 2, 3, -17.86, 3.07, 0.64, 28, -8.8, -13.43, 0.36, 2, 3, -16.18, 6.19, 0.78, 28, -12.27, -12.69, 0.22, 2, 3, -16.18, 10.51, 0.83, 28, -16.42, -13.89, 0.17, 1, 3, -13.3, 18.9, 1, 1, 3, 20.74, 18.9, 1, 2, 3, -4.28, 0.94, 0.79307, 28, -10.54, 0.2, 0.20693, 4, 3, 15.74, 1.54, 0.48623, 31, 3.36, 15.59, 0.34128, 32, -7.43, 15.59, 0.16888, 33, -18.22, 15.59, 0.00361, 2, 31, 0.21, 0.05, 0.99999, 32, -10.58, 0.05, 1e-05, 3, 3, -6.68, -9.19, 0.00634, 28, -0.15, 0.72, 0.96832, 31, -9.05, -5.95, 0.02534, 2, 28, 4.78, -0.11, 0.99982, 29, -5.97, -0.88, 0.00018, 4, 28, 10.89, 0.1, 0.46408, 29, 0.06, 0.11, 0.5352, 31, -6.17, -16.62, 0.00071, 32, -16.96, -16.62, 0, 2, 29, 5.25, 0.4, 0.99996, 31, -4.9, -21.66, 4e-05, 2, 29, 9.44, 0.17, 0.37477, 30, 0.26, 0.18, 0.62523, 1, 30, 3.92, 0.11, 1, 3, 3, 9.07, -12.86, 6e-05, 28, -1.02, 16.87, 0.00103, 31, 6.01, -0.06, 0.99891, 3, 28, 1.01, 21.99, 0.00111, 31, 11.51, -0.38, 0.15661, 32, 0.72, -0.38, 0.84228, 4, 28, 2.41, 26.85, 0.00014, 31, 16.56, -0.18, 0.00089, 32, 5.77, -0.18, 0.99884, 33, -5.02, -0.18, 0.00013, 2, 32, 10.97, -0.07, 0.41408, 33, 0.18, -0.07, 0.58592, 1, 33, 5.69, -0.01, 1], "hull": 39}}, "honghaier4": {"honghaier4": {"type": "mesh", "uvs": [1, 1, 0.5, 1, 0, 1, 0, 0.85714, 0, 0.71429, 0, 0.57143, 0, 0.42857, 0, 0.28571, 0, 0.14286, 0, 0, 0.5, 0, 1, 0, 1, 0.14286, 1, 0.28571, 1, 0.42857, 1, 0.57143, 1, 0.71429, 1, 0.85714, 0.5, 0.85714, 0.5, 0.71429, 0.5, 0.57143, 0.5, 0.42857, 0.5, 0.28571, 0.5, 0.14286], "triangles": [18, 3, 19, 2, 3, 18, 1, 2, 18, 18, 19, 16, 17, 18, 16, 1, 18, 17, 0, 1, 17, 20, 5, 21, 4, 5, 20, 19, 4, 20, 3, 4, 19, 15, 20, 14, 19, 20, 15, 16, 19, 15, 6, 7, 22, 21, 6, 22, 5, 6, 21, 21, 22, 13, 14, 21, 13, 20, 21, 14, 8, 9, 10, 23, 8, 10, 7, 8, 23, 22, 7, 23, 23, 10, 11, 12, 23, 11, 22, 23, 12, 13, 22, 12], "vertices": [1, 7, 20.4, 15.22, 1, 1, 7, 20.09, -2.28, 1, 2, 6, 36.33, -19.78, 0.02979, 7, 19.78, -19.78, 0.97021, 2, 6, 25.76, -19.59, 0.17296, 7, 9.21, -19.59, 0.82704, 3, 5, 31.74, -19.4, 0.02323, 6, 15.19, -19.4, 0.56988, 7, -1.36, -19.4, 0.40689, 4, 4, 37.72, -19.22, 0.00118, 5, 21.17, -19.22, 0.22983, 6, 4.62, -19.22, 0.69751, 7, -11.93, -19.22, 0.07148, 4, 4, 27.15, -19.03, 0.07855, 5, 10.6, -19.03, 0.59368, 6, -5.95, -19.03, 0.3267, 7, -22.5, -19.03, 0.00107, 4, 4, 16.58, -18.84, 0.42575, 5, 0.03, -18.84, 0.51383, 6, -16.52, -18.84, 0.06043, 7, -33.07, -18.84, 0, 4, 4, 6.01, -18.65, 0.84302, 5, -10.54, -18.65, 0.1549, 6, -27.09, -18.65, 0.00207, 7, -43.64, -18.65, 0, 3, 4, -4.56, -18.47, 0.97405, 5, -21.11, -18.47, 0.02595, 7, -54.21, -18.47, 0, 2, 4, -4.25, -0.97, 1, 7, -53.9, -0.97, 0, 2, 4, -3.94, 16.53, 0.99914, 5, -20.49, 16.53, 0.00086, 3, 4, 6.63, 16.34, 0.88568, 5, -9.92, 16.34, 0.11432, 7, -43.02, 16.34, 0, 3, 4, 17.2, 16.15, 0.41659, 5, 0.65, 16.15, 0.58071, 6, -15.9, 16.15, 0.0027, 3, 4, 27.77, 15.97, 0.04313, 5, 11.22, 15.97, 0.80691, 6, -5.33, 15.97, 0.14996, 3, 5, 21.79, 15.78, 0.36746, 6, 5.24, 15.78, 0.58495, 7, -11.31, 15.78, 0.04759, 3, 5, 32.36, 15.59, 0.0564, 6, 15.81, 15.59, 0.4749, 7, -0.74, 15.59, 0.4687, 3, 5, 42.93, 15.4, 0.00087, 6, 26.38, 15.4, 0.06355, 7, 9.83, 15.4, 0.93558, 2, 6, 26.07, -2.09, 0.00252, 7, 9.52, -2.09, 0.99748, 2, 6, 15.5, -1.91, 0.84774, 7, -1.05, -1.91, 0.15226, 1, 6, 4.93, -1.72, 1, 2, 5, 10.91, -1.53, 0.99422, 6, -5.64, -1.53, 0.00578, 4, 4, 16.89, -1.34, 0.30773, 5, 0.34, -1.34, 0.69165, 6, -16.21, -1.34, 0.00062, 7, -32.76, -1.34, 0, 3, 4, 6.32, -1.16, 0.99719, 5, -10.23, -1.16, 0.00281, 7, -43.33, -1.16, 0], "hull": 18}}, "honghaier5": {"honghaier5": {"type": "mesh", "uvs": [0.89447, 0.21045, 0.94008, 0.28134, 1, 0.37447, 1, 0.51234, 0.86546, 0.59167, 0.89959, 0.85343, 0.86392, 0.91144, 0.60386, 0.98051, 0.39961, 1, 0.21673, 0.97472, 0.16971, 0.94306, 0.46716, 0.84769, 0.39453, 0.62158, 0.16007, 0.52729, 0, 0.46291, 0, 0.37865, 0, 0.31596, 0, 0.24328, 0.1139, 0, 0.41782, 0, 0.75907, 0, 0.70977, 0.87112, 0.57327, 0.91893, 0.5619, 0.59812, 0.45953, 0.42229], "triangles": [5, 21, 23, 6, 21, 5, 22, 11, 21, 9, 10, 11, 11, 8, 9, 6, 7, 22, 6, 22, 21, 22, 8, 11, 7, 8, 22, 24, 2, 3, 4, 24, 3, 23, 24, 4, 13, 24, 23, 12, 13, 23, 11, 12, 23, 5, 23, 4, 11, 23, 21, 24, 16, 17, 24, 15, 16, 19, 0, 17, 0, 19, 20, 24, 0, 1, 24, 1, 2, 24, 14, 15, 17, 18, 19, 0, 24, 17, 13, 14, 24], "vertices": [2, 24, 36.59, 22.21, 0.75589, 25, -19.39, 27.21, 0.24411, 2, 24, 44.97, 24.04, 0.67367, 25, -10.8, 27.13, 0.32633, 2, 24, 56.31, 26.52, 0.56564, 25, 0.04, 27.04, 0.43436, 2, 24, 72.57, 25.87, 0.18512, 25, 15.86, 23.21, 0.81488, 2, 24, 81.66, 19.03, 0.03676, 25, 23.44, 14.74, 0.96324, 2, 25, 53.85, 9.07, 0.12472, 27, -8.38, 3.31, 0.87528, 2, 25, 60.1, 5.8, 0.46819, 27, -3.06, 7.95, 0.53181, 2, 25, 65.09, -8.25, 0.1899, 27, 11.84, 7.48, 0.8101, 2, 25, 65.02, -18.32, 0.00212, 27, 21.19, 3.76, 0.99788, 1, 27, 26.68, -3.71, 1, 1, 27, 26.4, -8.07, 1, 2, 25, 48.31, -10.94, 0.34611, 27, 8.25, -9.12, 0.65389, 1, 25, 21.56, -8.06, 1, 2, 24, 72.89, -14.21, 0.34592, 25, 7.89, -16.37, 0.65408, 2, 24, 64.8, -21.86, 0.5821, 25, -1.09, -22.07, 0.4179, 2, 24, 55.02, -21.43, 0.74235, 25, -11.14, -19.44, 0.25765, 2, 24, 47.56, -21.14, 0.86156, 25, -18.35, -17.49, 0.13844, 2, 24, 38.9, -20.81, 0.99979, 25, -26.28, -15.98, 0.00021, 1, 24, 10.44, -14.19, 1, 1, 24, 11.03, 0.39, 1, 1, 24, 11.69, 16.76, 1, 2, 25, 53.74, -0.27, 0.29907, 27, 0.28, -0.19, 0.70093, 2, 25, 57.68, -7.97, 0.03404, 27, 8.88, 0.69, 0.96596, 2, 24, 81.83, 4.44, 0.00019, 25, 20.76, 0.4, 0.99981, 2, 24, 60.9, 0.37, 0.73752, 25, -0.57, 0.49, 0.26248], "hull": 21}}, "honghaier6": {"honghaier6": {"type": "mesh", "uvs": [1, 0.52132, 0.76795, 0.65479, 0.76036, 0.86799, 0.73382, 0.95292, 0.47781, 0.96257, 0.22953, 1, 0, 1, 0, 0.90092, 0.31294, 0.85759, 0.32811, 0.66172, 0.10061, 0.49186, 0.14232, 0.25266, 0.27882, 0, 0.61249, 0, 1, 0, 0.41911, 0.48319, 0.50253, 0.24919, 0.47599, 0.66692, 0.55561, 0.90265, 0.34328, 0.93559], "triangles": [19, 8, 18, 7, 8, 19, 4, 19, 18, 4, 18, 3, 5, 6, 7, 19, 5, 7, 5, 19, 4, 10, 11, 15, 1, 15, 0, 9, 10, 15, 17, 9, 15, 1, 17, 15, 8, 9, 17, 2, 17, 1, 18, 8, 17, 2, 18, 17, 3, 18, 2, 16, 12, 13, 11, 12, 16, 15, 11, 16, 16, 13, 14, 0, 16, 14, 15, 16, 0], "vertices": [2, 20, 58.06, 27.18, 0.85581, 21, 7.91, 26.85, 0.14419, 2, 20, 73.35, 17.88, 0.21667, 21, 20.3, 13.93, 0.78333, 2, 21, 42.43, 10.56, 0.99897, 23, -10.75, -0.27, 0.00103, 2, 21, 51.1, 8.1, 0.78367, 23, -6.89, 7.87, 0.21633, 2, 21, 50.45, -4.21, 0.00748, 23, 5.15, 5.19, 0.99252, 1, 23, 17.69, 5.41, 1, 1, 23, 28.21, 2.14, 1, 1, 23, 25.13, -7.8, 1, 2, 21, 38.46, -10.57, 0.30727, 23, 9.43, -7.68, 0.69273, 1, 21, 18.18, -7.09, 1, 2, 20, 60.39, -16.04, 0.02151, 21, -0.96, -15.51, 0.97849, 2, 20, 35.22, -17.2, 0.89966, 21, -25.58, -10.16, 0.10034, 1, 20, 8.08, -14.02, 1, 1, 20, 6.07, 1.87, 1, 1, 20, 3.75, 20.33, 1, 1, 21, 0.19, -0.24, 1, 2, 20, 32.69, -0.09, 0.99994, 21, -23.62, 7.03, 6e-05, 1, 21, 19.68, -0.13, 1, 2, 21, 44.72, 0.33, 0.5252, 23, -0.29, 0.29, 0.4748, 1, 23, 10.47, 0.57, 1], "hull": 15}}, "honghaier7": {"honghaier8": {"x": 29.25, "y": -1.5, "rotation": 90.19, "width": 45, "height": 202}}, "honghaier9": {"honghaier9": {"type": "mesh", "uvs": [1, 0.19197, 1, 0.37638, 0.76248, 0.53272, 0.5741, 0.6806, 0.62694, 0.77841, 0.63154, 0.87855, 0.39492, 1, 0, 1, 0, 0.75941, 0.05207, 0.6062, 0.16277, 0.38702, 0.30807, 0.25551, 0.43261, 0.11524, 0.61942, 0, 1, 0, 0.32541, 0.66644, 0.34228, 0.76189, 0.46033, 0.4912, 0.60087, 0.33022, 0.76952, 0.17066], "triangles": [8, 9, 15, 16, 15, 3, 8, 15, 16, 16, 3, 4, 7, 8, 16, 5, 6, 16, 5, 16, 4, 7, 16, 6, 18, 10, 11, 17, 10, 18, 2, 18, 1, 17, 18, 2, 9, 10, 17, 15, 9, 17, 3, 17, 2, 15, 17, 3, 19, 13, 14, 12, 13, 19, 19, 14, 0, 18, 12, 19, 11, 12, 18, 19, 0, 1, 18, 19, 1], "vertices": [2, 16, 10.78, 8.2, 0.99667, 17, -15.21, 9.68, 0.00333, 2, 16, 22.57, 14.7, 0.68654, 17, -2.86, 15.04, 0.31346, 3, 16, 36.81, 12.52, 0.0549, 17, 11.11, 11.52, 0.94383, 18, -8.02, 17.42, 0.00128, 2, 17, 23.79, 9.43, 0.52303, 18, 2.14, 9.56, 0.47697, 2, 17, 29.56, 14.06, 0.06099, 18, 9.42, 10.9, 0.93901, 2, 17, 36.19, 17.13, 0.00049, 18, 16.72, 10.45, 0.99951, 1, 18, 24.81, 0.97, 1, 1, 18, 23.57, -13.59, 1, 2, 17, 37.52, -7.77, 0.01338, 18, 6.07, -12.1, 0.98662, 2, 17, 26.49, -10.45, 0.62702, 18, -4.91, -9.23, 0.37298, 2, 16, 38.22, -12.04, 0.02109, 17, 10.19, -13.07, 0.97891, 2, 16, 27.22, -11.98, 0.50866, 17, -0.76, -11.96, 0.49134, 2, 16, 16.03, -12.89, 0.98514, 17, -11.99, -11.81, 0.01486, 1, 16, 5.32, -10.9, 1, 1, 16, -1.48, 1.42, 1, 2, 17, 26.5, 0.58, 0.34022, 18, 0.33, 0.48, 0.65978, 2, 17, 32.64, 3.92, 0.00258, 18, 7.32, 0.51, 0.99742, 1, 17, 12.78, 0.06, 1, 2, 16, 26.76, 0.14, 0.55331, 17, -0.07, 0.15, 0.44669, 1, 16, 13.54, -0.02, 1], "hull": 15}}, "honghaier_0000": {"honghaier_0000": {"type": "mesh", "uvs": [0.26383, 0.03137, 0.4183, 0.02552, 0.6321, 0.04308, 0.74974, 0.08275, 0.82749, 0.15688, 0.8326, 0.25248, 0.91179, 0.33876, 0.94398, 0.40444, 1, 0.4974, 1, 0.675, 0.95428, 0.77368, 0.84615, 0.85018, 0.67349, 0.9156, 0.2427, 0.93666, 0.15026, 0.95662, 0.0805, 1, 0, 1, 0, 0.90229, 0.05335, 0.77833, 0.2003, 0.71199, 0.33448, 0.68356, 0.42606, 0.61992, 0.4644, 0.56712, 0.55172, 0.51025, 0.62626, 0.43037, 0.60496, 0.40058, 0.61135, 0.3383, 0.64756, 0.25842, 0.65608, 0.17718, 0.57728, 0.12438, 0.40902, 0.10813, 0.24772, 0.10967, 0.07184, 0.05133, 0.05909, 0, 0.14516, 0, 0.18673, 0.04377, 0.61028, 0.08198, 0.75063, 0.34052, 0.68524, 0.70013], "triangles": [35, 34, 0, 32, 33, 34, 32, 34, 35, 30, 0, 1, 31, 35, 0, 31, 0, 30, 32, 35, 31, 36, 1, 2, 36, 2, 3, 30, 1, 36, 29, 30, 36, 28, 36, 3, 28, 3, 4, 29, 36, 28, 28, 4, 5, 27, 28, 5, 37, 27, 5, 37, 5, 6, 26, 27, 37, 37, 25, 26, 24, 25, 37, 8, 24, 7, 23, 24, 8, 37, 6, 7, 7, 24, 37, 38, 23, 8, 22, 23, 38, 21, 22, 38, 9, 38, 8, 10, 38, 9, 11, 38, 10, 38, 20, 21, 12, 38, 11, 12, 20, 38, 13, 20, 12, 13, 19, 20, 18, 19, 13, 14, 17, 18, 13, 14, 18, 15, 16, 17, 14, 15, 17], "vertices": [2, 42, -30.63, 6.95, 0.12954, 41, 6.97, 1.53, 0.87046, 2, 42, -16.89, 7.77, 0.59627, 41, 20.72, 2.35, 0.40373, 3, 43, -10.48, 41.46, 0.00212, 42, 2.14, 5.31, 0.99682, 41, 39.75, -0.11, 0.00106, 2, 43, -0.01, 35.91, 0.15035, 42, 12.61, -0.24, 0.84965, 2, 43, 6.91, 25.53, 0.42092, 42, 19.53, -10.62, 0.57908, 2, 43, 7.37, 12.15, 0.80573, 42, 19.99, -24, 0.19427, 3, 44, 20.76, 50.05, 0.04766, 43, 14.42, 0.07, 0.94743, 42, 27.03, -36.08, 0.0049, 2, 44, 23.63, 40.86, 0.18396, 43, 17.28, -9.13, 0.81604, 2, 44, 28.61, 27.84, 0.43702, 43, 22.27, -22.14, 0.56298, 2, 44, 28.61, 2.98, 0.84883, 43, 22.27, -47.01, 0.15117, 3, 36, 80.74, 26.96, 0.00026, 44, 24.54, -10.84, 0.96299, 43, 18.2, -60.82, 0.03675, 3, 36, 71.11, 16.25, 0.01915, 44, 14.92, -21.55, 0.97948, 43, 8.57, -71.53, 0.00137, 2, 36, 55.75, 7.1, 0.11144, 44, -0.45, -30.7, 0.88856, 2, 36, 17.41, 4.15, 0.72005, 44, -38.79, -33.65, 0.27995, 2, 36, 9.18, 1.35, 0.91023, 44, -47.01, -36.45, 0.08977, 2, 36, 2.97, -4.72, 0.99858, 44, -53.22, -42.52, 0.00142, 1, 36, -4.19, -4.72, 1, 2, 36, -4.19, 8.96, 0.95223, 44, -60.39, -28.84, 0.04777, 2, 36, 0.56, 26.31, 0.76206, 44, -55.64, -11.49, 0.23794, 2, 36, 13.63, 35.6, 0.54876, 44, -42.56, -2.2, 0.45124, 3, 36, 25.58, 39.58, 0.31249, 44, -30.62, 1.78, 0.68392, 43, -36.96, -48.2, 0.00359, 3, 36, 33.73, 48.49, 0.10545, 44, -22.47, 10.69, 0.83955, 43, -28.81, -39.3, 0.055, 3, 36, 37.14, 55.88, 0.04002, 44, -19.06, 18.08, 0.81917, 43, -25.4, -31.9, 0.14082, 3, 36, 44.91, 63.84, 0.00604, 44, -11.28, 26.04, 0.64312, 43, -17.63, -23.94, 0.35084, 2, 44, -4.65, 37.23, 0.24583, 43, -11, -12.76, 0.75417, 2, 44, -6.55, 41.4, 0.12744, 43, -12.89, -8.59, 0.87256, 3, 44, -5.98, 50.12, 0.02469, 43, -12.32, 0.13, 0.96189, 42, 0.3, -36.02, 0.01342, 2, 43, -9.1, 11.31, 0.80098, 42, 3.52, -24.83, 0.19902, 2, 43, -8.34, 22.69, 0.35641, 42, 4.28, -13.46, 0.64359, 3, 43, -15.36, 30.08, 0.03027, 42, -2.74, -6.07, 0.95102, 41, 34.87, -11.49, 0.01871, 2, 42, -17.71, -3.79, 0.58824, 41, 19.89, -9.21, 0.41176, 2, 42, -32.07, -4.01, 0.13849, 41, 5.54, -9.43, 0.86151, 1, 41, -10.12, -1.26, 1, 1, 41, -11.25, 5.92, 1, 1, 41, -3.59, 5.92, 1, 2, 42, -37.49, 5.22, 0.00127, 41, 0.11, -0.2, 0.99873, 2, 43, -12.42, 36.02, 0.00151, 42, 0.2, -0.13, 0.99849, 2, 44, 6.42, 49.81, 0.00097, 43, 0.07, -0.18, 0.99903, 1, 44, 0.6, -0.54, 1], "hull": 35}}, "honghaier_0003": {"honghaier_0003": {"type": "mesh", "uvs": [0.2301, 0.10973, 0.28191, 0.16583, 0.40463, 0.22368, 0.5328, 0.2868, 0.64734, 0.33063, 0.80551, 0.41653, 0.89824, 0.46737, 0.95774, 0.53052, 1, 0.61933, 1, 0.72346, 0.96328, 0.78458, 0.91131, 0.82865, 0.81955, 0.89688, 0.72779, 0.94663, 0.64487, 0.97932, 0.54758, 1, 0.41712, 1, 0.38395, 0.96227, 0.51883, 0.93526, 0.57301, 0.88338, 0.59291, 0.83647, 0.6024, 0.79754, 0.61725, 0.75347, 0.60126, 0.70353, 0.56813, 0.64258, 0.51806, 0.59123, 0.45382, 0.54011, 0.37582, 0.48112, 0.29935, 0.43294, 0.20453, 0.38772, 0.10818, 0.31693, 0, 0.21074, 0, 0, 0.24646, 0, 0.56642, 0.95254, 0.67635, 0.91563, 0.74427, 0.85757, 0.81219, 0.79635, 0.80589, 0.73693, 0.79678, 0.67302, 0.75055, 0.60247, 0.69594, 0.53225, 0.59931, 0.4708, 0.51318, 0.41139, 0.41341, 0.35805, 0.31048, 0.30606, 0.20264, 0.21906, 0.05251, 0.10187], "triangles": [19, 20, 35, 34, 18, 19, 34, 19, 35, 14, 34, 35, 14, 35, 13, 16, 17, 18, 15, 18, 34, 15, 34, 14, 16, 18, 15, 21, 22, 37, 36, 21, 37, 20, 21, 36, 12, 37, 11, 36, 37, 12, 35, 20, 36, 13, 35, 36, 12, 13, 36, 39, 8, 9, 38, 39, 9, 23, 39, 38, 22, 23, 38, 10, 38, 9, 37, 38, 10, 22, 38, 37, 11, 37, 10, 41, 5, 6, 40, 41, 6, 40, 6, 7, 40, 7, 8, 40, 24, 41, 39, 40, 8, 23, 24, 40, 23, 40, 39, 42, 43, 4, 42, 4, 5, 41, 42, 5, 26, 27, 43, 26, 43, 42, 25, 26, 42, 25, 42, 41, 24, 25, 41, 44, 45, 2, 3, 44, 2, 43, 44, 3, 43, 3, 4, 28, 29, 45, 28, 45, 44, 27, 28, 44, 27, 44, 43, 47, 32, 33, 0, 47, 33, 31, 32, 47, 46, 47, 0, 46, 0, 1, 31, 47, 46, 2, 46, 1, 45, 46, 2, 30, 31, 46, 30, 46, 45, 29, 30, 45], "vertices": [1, 45, 9.99, 10.94, 1, 2, 45, 18.11, 9.63, 0.99126, 46, -12.76, 12.42, 0.00874, 2, 45, 30.08, 12.59, 0.54102, 46, -0.45, 12.94, 0.45898, 4, 45, 42.83, 15.46, 0.01744, 46, 12.63, 13.21, 0.90076, 47, -10.26, 11.96, 0.0757, 48, -33.43, 0.86, 0.0061, 3, 46, 23.32, 14.7, 0.38446, 47, 0.14, 14.85, 0.50487, 48, -24.53, 6.96, 0.11066, 3, 46, 40.09, 14.25, 0.00106, 47, 16.82, 16.62, 0.19894, 48, -9.32, 14.05, 0.8, 3, 47, 26.64, 17.61, 0.00368, 48, -0.36, 18.17, 0.9962, 49, -24.87, 10.24, 0.00012, 2, 48, 8.89, 19.21, 0.96008, 49, -16.54, 14.4, 0.03992, 2, 48, 20.48, 17.62, 0.64454, 49, -5.11, 16.89, 0.35546, 3, 48, 32.39, 12.11, 0.11141, 49, 7.97, 15.82, 0.88752, 50, -16.67, 6.3, 0.00107, 3, 48, 38.13, 6.18, 0.0057, 49, 15.4, 12.22, 0.89457, 50, -8.74, 8.58, 0.09973, 2, 49, 20.59, 7.57, 0.47169, 50, -1.77, 8.58, 0.52831, 1, 50, 9.57, 7.85, 1, 2, 50, 19.06, 5.71, 0.63777, 51, -1.7, 5.5, 0.36223, 1, 51, 6.16, 5.99, 1, 1, 51, 14.34, 4.59, 1, 1, 51, 23.67, -0.37, 1, 1, 51, 23.81, -5.83, 1, 2, 50, 28.14, -8.65, 0.00308, 51, 12.57, -3.71, 0.99692, 4, 48, 36.16, -27.74, 2e-05, 49, 25.21, -20.3, 0.01189, 50, 20.28, -9.1, 0.29931, 51, 5.63, -7.42, 0.68878, 5, 47, 43.14, -32.41, 0.00036, 48, 31.47, -23.79, 0.00651, 49, 19.46, -18.21, 0.09862, 50, 14.59, -11.38, 0.68567, 51, 1.43, -11.88, 0.20884, 5, 47, 40.13, -28.46, 0.00472, 48, 27.34, -21.04, 0.03339, 49, 14.63, -17.05, 0.2542, 50, 10.22, -13.73, 0.65548, 51, -1.55, -15.85, 0.05222, 5, 47, 36.95, -23.76, 0.02942, 48, 22.81, -17.62, 0.13574, 49, 9.2, -15.39, 0.44993, 50, 5.07, -16.13, 0.38052, 51, -5.22, -20.19, 0.00439, 4, 47, 31.51, -20.34, 0.12905, 48, 16.55, -16.15, 0.36368, 49, 2.82, -16.17, 0.38452, 50, 0.83, -20.96, 0.12275, 4, 47, 24.1, -16.97, 0.46419, 48, 8.46, -15.36, 0.40616, 49, -5.06, -18.21, 0.11238, 50, -3.67, -27.73, 0.01726, 5, 46, 35.66, -17.49, 0.00495, 47, 16.62, -15.43, 0.86665, 48, 0.88, -16.33, 0.11382, 49, -11.84, -21.72, 0.014, 50, -6.38, -34.87, 0.00058, 4, 46, 27.57, -15.71, 0.10819, 47, 8.37, -14.73, 0.88992, 48, -7.15, -18.35, 0.00187, 49, -18.68, -26.38, 2e-05, 2, 46, 17.99, -13.85, 0.58899, 47, -1.37, -14.15, 0.41101, 3, 45, 44.85, -10.85, 0.02972, 46, 9.36, -12.98, 0.91423, 47, -10.03, -14.43, 0.05605, 2, 45, 35.55, -13.1, 0.39855, 46, -0.19, -13.33, 0.60145, 2, 45, 23.71, -13.37, 0.94993, 46, -11.86, -11.24, 0.05007, 1, 45, 7.82, -11.53, 1, 1, 45, -12.56, 5.49, 1, 1, 45, 0.23, 20.81, 1, 1, 51, 10.19, 0.02, 1, 2, 50, 18.46, 0.03, 0.45295, 51, 0.14, 0.1, 0.54705, 1, 50, 9.31, 0, 1, 3, 48, 34.34, -5.55, 2e-05, 49, 15.87, -0.1, 0.58442, 50, -0.16, -0.28, 0.41557, 3, 47, 46.01, -11.28, 0, 49, 8.37, 0.01, 0.99972, 50, -5.82, -5.2, 0.00028, 4, 47, 39.68, -6.24, 0.00011, 48, 19.71, -0.16, 0.41504, 49, 0.28, -0.07, 0.58473, 50, -11.8, -10.66, 0.00012, 1, 48, 10.07, 0.17, 1, 2, 47, 21.22, 0.12, 0.54469, 48, 0.18, -0.13, 0.45531, 2, 46, 31.32, -1.53, 6e-05, 47, 10.21, -0.18, 0.99994, 2, 46, 21.19, -0.04, 0.50633, 47, -0.02, -0.04, 0.49367, 1, 46, 10.68, 0.16, 1, 2, 45, 33.16, 0.08, 0.47944, 46, 0.08, 0.07, 0.52056, 1, 45, 19.14, 0.4, 1, 1, 45, 0.01, 0.53, 1], "hull": 34}}, "honghaier_0009": {"honghaier_0009": {"type": "mesh", "uvs": [0.67836, 0.036, 0.77826, 0.05088, 0.87137, 0.07004, 0.95857, 0.12752, 0.98218, 0.25015, 0.9612, 0.31453, 0.98283, 0.36282, 1, 0.44546, 1, 0.61444, 0.96515, 0.68703, 0.88778, 0.75248, 0.81855, 0.78342, 0.77885, 0.73701, 0.83586, 0.70845, 0.84807, 0.67394, 0.85418, 0.62277, 0.84706, 0.54899, 0.86742, 0.46569, 0.85422, 0.38748, 0.82433, 0.34012, 0.77783, 0.30051, 0.70476, 0.26169, 0.61441, 0.22907, 0.51903, 0.22913, 0.41934, 0.21824, 0.34666, 0.252, 0.26187, 0.31844, 0.21994, 0.36418, 0.19944, 0.41319, 0.18174, 0.49488, 0.2041, 0.56676, 0.23112, 0.63211, 0.28948, 0.69473, 0.34161, 0.75138, 0.39373, 0.83156, 0.41016, 0.90745, 0.40797, 0.96124, 0.39591, 1, 0.34763, 1, 0.34594, 0.96101, 0.31465, 0.9205, 0.26721, 0.89101, 0.20928, 0.86567, 0.13474, 0.82816, 0.06175, 0.77189, 0.01883, 0.68571, 0, 0.6072, 0, 0.51993, 0.02346, 0.42151, 0.05977, 0.34646, 0.07582, 0.31094, 0.13038, 0.24654, 0.19599, 0.19571, 0.21664, 0.15124, 0.26175, 0.09024, 0.32589, 0.03815, 0.39255, 0.01723, 0.47661, 0, 0.60047, 0, 0.91625, 0.66989, 0.94744, 0.33502, 0.6649, 0.05402, 0.46362, 0.10093, 0.27482, 0.19151, 0.13604, 0.39357, 0.10945, 0.64627, 0.87825, 0.10491], "triangles": [11, 13, 10, 11, 12, 13, 10, 59, 9, 13, 14, 10, 10, 14, 59, 9, 59, 8, 14, 15, 59, 59, 15, 8, 17, 8, 16, 8, 17, 7, 8, 15, 16, 17, 60, 6, 17, 6, 7, 17, 18, 60, 18, 19, 60, 60, 5, 6, 60, 19, 4, 66, 4, 19, 5, 60, 4, 3, 4, 66, 20, 66, 19, 66, 1, 2, 66, 20, 1, 3, 66, 2, 20, 21, 1, 22, 61, 21, 21, 61, 1, 1, 61, 0, 22, 58, 61, 61, 58, 0, 24, 62, 23, 22, 23, 62, 22, 62, 58, 58, 62, 57, 24, 55, 62, 62, 55, 56, 62, 56, 57, 26, 63, 25, 26, 52, 63, 25, 63, 24, 63, 55, 24, 52, 53, 63, 53, 54, 63, 63, 54, 55, 51, 52, 26, 29, 48, 64, 47, 48, 29, 48, 49, 64, 29, 64, 28, 28, 64, 27, 49, 50, 64, 27, 64, 51, 64, 50, 51, 27, 51, 26, 42, 32, 33, 42, 43, 32, 44, 65, 43, 43, 31, 32, 43, 65, 31, 44, 45, 65, 45, 46, 65, 65, 46, 29, 65, 30, 31, 65, 29, 30, 29, 46, 47, 36, 37, 39, 37, 38, 39, 36, 39, 35, 39, 40, 35, 35, 40, 34, 40, 41, 34, 41, 33, 34, 41, 42, 33], "vertices": [2, 41, 2.49, 1.68, 0.98106, 42, -35.11, 7.1, 0.01894, 3, 41, 19.77, -0.52, 0.48516, 42, -17.83, 4.9, 0.51396, 43, -30.45, 41.05, 0.00088, 2, 41, 35.88, -3.36, 0.01024, 42, -1.72, 2.06, 0.98976, 2, 42, 13.36, -6.45, 0.75349, 43, 0.74, 29.7, 0.24651, 2, 42, 17.45, -24.6, 0.27922, 43, 4.83, 11.55, 0.72078, 2, 42, 13.82, -34.13, 0.02526, 43, 1.2, 2.02, 0.97474, 3, 42, 17.56, -41.27, 0.00046, 43, 4.94, -5.12, 0.93305, 44, 11.29, 44.86, 0.06649, 3, 42, 20.53, -53.5, 0, 43, 7.91, -17.36, 0.69328, 44, 14.26, 32.63, 0.30671, 2, 43, 7.91, -42.36, 0.1343, 44, 14.26, 7.62, 0.86569, 2, 43, 1.88, -53.11, 0.00559, 44, 8.23, -3.12, 0.99441, 1, 44, -5.16, -12.81, 1, 1, 44, -17.13, -17.39, 1, 1, 44, -24, -10.52, 1, 1, 44, -14.14, -6.29, 1, 2, 43, -18.37, -51.17, 0.00598, 44, -12.03, -1.18, 0.99402, 2, 43, -17.32, -43.6, 0.07332, 44, -10.97, 6.39, 0.92668, 2, 43, -18.55, -32.68, 0.28066, 44, -12.2, 17.31, 0.71934, 4, 41, 35.2, -61.92, 0.00198, 42, -2.41, -56.5, 0.00051, 43, -15.03, -20.35, 0.61295, 44, -8.68, 29.64, 0.38456, 4, 41, 32.91, -50.34, 0.02956, 42, -4.69, -44.92, 0.05155, 43, -17.31, -8.77, 0.82407, 44, -10.96, 41.21, 0.09481, 4, 41, 27.74, -43.33, 0.10667, 42, -9.86, -37.91, 0.18972, 43, -22.48, -1.76, 0.68952, 44, -16.13, 48.22, 0.0141, 5, 35, 53.68, -30.05, 0.00275, 41, 19.7, -37.47, 0.26704, 42, -17.91, -32.05, 0.31655, 43, -30.52, 4.1, 0.41352, 44, -24.18, 54.08, 0.00014, 4, 35, 41.04, -24.3, 0.04773, 41, 7.06, -31.72, 0.54673, 42, -30.55, -26.3, 0.25681, 43, -43.17, 9.85, 0.14873, 4, 35, 25.41, -19.48, 0.30577, 41, -8.57, -26.9, 0.60547, 42, -46.18, -21.48, 0.06236, 43, -58.8, 14.67, 0.02639, 5, 38, 42.48, -6.87, 0.01533, 35, 8.91, -19.48, 0.73543, 41, -25.07, -26.91, 0.24603, 42, -62.68, -21.48, 0.00187, 43, -75.3, 14.66, 0.00134, 3, 38, 25.23, -5.25, 0.32914, 35, -8.33, -17.87, 0.65941, 41, -42.32, -25.29, 0.01145, 3, 39, 36.07, 19.89, 0.02101, 38, 12.66, -10.25, 0.7772, 35, -20.91, -22.87, 0.20178, 3, 39, 21.4, 10.05, 0.38992, 38, -2.01, -20.08, 0.60891, 35, -35.58, -32.7, 0.00117, 3, 40, 19.8, 40.89, 0.00104, 39, 14.14, 3.28, 0.73981, 38, -9.26, -26.85, 0.25915, 3, 40, 16.26, 33.64, 0.04842, 39, 10.6, -3.97, 0.89718, 38, -12.81, -34.11, 0.0544, 3, 37, -32.93, 67.71, 0.00143, 40, 13.19, 21.55, 0.41361, 39, 7.54, -16.06, 0.58495, 3, 37, -29.06, 57.07, 0.03163, 40, 17.06, 10.91, 0.76679, 39, 11.4, -26.7, 0.20157, 3, 37, -24.39, 47.4, 0.13989, 40, 21.74, 1.24, 0.82357, 39, 16.08, -36.37, 0.03654, 3, 37, -14.29, 38.13, 0.3695, 40, 31.83, -8.03, 0.63021, 39, 26.17, -45.64, 0.00029, 2, 37, -5.27, 29.74, 0.58858, 40, 40.85, -16.41, 0.41142, 2, 37, 3.74, 17.88, 0.83232, 40, 49.87, -28.28, 0.16768, 2, 37, 6.58, 6.65, 0.96573, 40, 52.71, -39.51, 0.03427, 1, 37, 6.21, -1.32, 1, 1, 37, 4.12, -7.05, 1, 1, 37, -4.23, -7.05, 1, 2, 37, -4.52, -1.28, 0.98506, 40, 41.6, -47.44, 0.01494, 2, 37, -9.94, 4.71, 0.84989, 40, 36.19, -41.44, 0.15011, 2, 37, -18.14, 9.08, 0.63816, 40, 27.98, -37.08, 0.36184, 2, 37, -28.17, 12.83, 0.42074, 40, 17.96, -33.33, 0.57926, 2, 37, -41.06, 18.38, 0.20526, 40, 5.06, -27.77, 0.79474, 2, 37, -53.69, 26.71, 0.06226, 40, -7.57, -19.45, 0.93774, 3, 37, -61.11, 39.46, 0.00411, 40, -14.99, -6.69, 0.98561, 39, -20.65, -44.3, 0.01028, 2, 40, -18.25, 4.93, 0.86976, 39, -23.91, -32.68, 0.13024, 2, 40, -18.25, 17.84, 0.60557, 39, -23.91, -19.77, 0.39443, 2, 40, -14.19, 32.41, 0.24568, 39, -19.85, -5.2, 0.75432, 3, 40, -7.91, 43.52, 0.03665, 39, -13.56, 5.91, 0.95302, 38, -36.97, -24.23, 0.01033, 3, 40, -5.13, 48.77, 0.0036, 39, -10.79, 11.16, 0.9435, 38, -34.19, -18.97, 0.05289, 2, 39, -1.35, 20.69, 0.68604, 38, -24.75, -9.44, 0.31396, 2, 39, 10, 28.22, 0.25091, 38, -13.41, -1.92, 0.74909, 3, 39, 13.57, 34.8, 0.06035, 38, -9.83, 4.66, 0.93622, 35, -43.4, -7.96, 0.00342, 2, 38, -2.03, 13.69, 0.85739, 35, -35.6, 1.07, 0.14261, 2, 38, 9.07, 21.4, 0.52671, 35, -24.5, 8.78, 0.47329, 2, 38, 20.6, 24.5, 0.22671, 35, -12.97, 11.88, 0.77329, 3, 38, 35.14, 27.04, 0.01157, 35, 1.57, 14.43, 0.90729, 41, -32.41, 7, 0.08113, 3, 35, 23, 14.43, 0.30728, 41, -10.98, 7.01, 0.69267, 42, -48.59, 12.43, 5e-05, 1, 44, -0.23, -0.59, 1, 3, 41, 49.04, -42.58, 0.0011, 43, -1.18, -1.01, 0.99224, 44, 5.16, 48.98, 0.00666, 4, 35, 34.15, 6.43, 0.00141, 41, 0.16, -0.99, 0.99425, 42, -37.44, 4.43, 0.004, 43, -50.06, 40.58, 0.00034, 2, 38, 32.89, 12.11, 0.00987, 35, -0.67, -0.51, 0.99013, 3, 39, 23.64, 28.84, 0.01099, 38, 0.23, -1.3, 0.98868, 35, -33.34, -13.92, 0.00033, 2, 40, 5.29, 36.54, 0.01314, 39, -0.37, -1.07, 0.98686, 2, 37, -45.44, 45.3, 0.00438, 40, 0.69, -0.86, 0.99562, 3, 41, 37.07, -8.52, 0.01139, 42, -0.53, -3.1, 0.95672, 43, -13.15, 33.05, 0.03189], "hull": 59}}, "honghaier_0011": {"honghaier_0011": {"type": "mesh", "uvs": [1, 0.0851, 1, 0.25696, 0.96029, 0.34693, 0.83666, 0.42533, 0.70653, 0.51144, 0.59809, 0.56156, 0.54604, 0.59755, 0.507, 0.62968, 0.47447, 0.67209, 0.46362, 0.71451, 0.46579, 0.76849, 0.47663, 0.8109, 0.50266, 0.85974, 0.53736, 0.89059, 0.6393, 0.94714, 0.841, 1, 0.53655, 1, 0.42419, 0.97025, 0.34248, 0.95008, 0.24204, 0.90468, 0.17505, 0.88474, 0.05639, 0.81882, 0, 0.73166, 0, 0.57284, 0.0329, 0.50493, 0.10595, 0.45567, 0.26714, 0.39597, 0.40944, 0.37284, 0.5656, 0.34, 0.66136, 0.29944, 0.77936, 0.23788, 0.84093, 0.15579, 0.85889, 0.08434, 0.82682, 0.02049, 0.84734, 0, 0.93585, 0, 0.48286, 0.94049, 0.32325, 0.8459, 0.20217, 0.72605, 0.2407, 0.56297, 0.47685, 0.4512, 0.75955, 0.36784, 0.95231, 0.1722, 0.86574, 0.25717, 0.60469, 0.41587, 0.35987, 0.50965, 0.22541, 0.64382, 0.26396, 0.78346, 0.40108, 0.88904, 0.59138, 0.96753], "triangles": [49, 36, 13, 14, 49, 13, 16, 36, 49, 17, 36, 16, 49, 14, 15, 16, 49, 15, 37, 10, 11, 48, 37, 11, 12, 48, 11, 19, 20, 37, 19, 37, 48, 13, 36, 48, 13, 48, 12, 18, 19, 48, 17, 18, 48, 36, 17, 48, 38, 22, 46, 47, 38, 9, 10, 47, 9, 21, 22, 38, 21, 38, 47, 37, 47, 10, 20, 21, 47, 20, 47, 37, 23, 24, 39, 46, 23, 39, 46, 39, 7, 8, 46, 7, 9, 46, 8, 46, 22, 23, 38, 46, 9, 40, 45, 26, 40, 26, 27, 25, 26, 45, 45, 40, 5, 39, 25, 45, 24, 25, 39, 6, 45, 5, 7, 45, 6, 39, 45, 7, 41, 44, 28, 41, 28, 29, 27, 28, 44, 40, 27, 44, 3, 4, 44, 3, 44, 41, 5, 40, 44, 5, 44, 4, 35, 33, 34, 32, 33, 35, 32, 35, 0, 42, 32, 0, 31, 32, 42, 42, 0, 1, 43, 31, 42, 43, 42, 1, 30, 31, 43, 2, 43, 1, 41, 29, 30, 41, 30, 43, 41, 43, 2, 3, 41, 2], "vertices": [1, 52, -9.85, -1.94, 1, 1, 52, 6.21, 7.36, 1, 1, 52, 15.89, 10.03, 1, 2, 52, 27.19, 7.43, 0.4198, 53, -1.63, 7.82, 0.5802, 3, 53, 9.98, 12.41, 0.90453, 54, -7.52, 14.25, 0.08711, 55, -8.91, 28.64, 0.00836, 3, 53, 18.61, 14.15, 0.46125, 54, 1.28, 14.19, 0.44295, 55, -2.66, 22.44, 0.0958, 4, 53, 23.32, 16.14, 0.18629, 54, 6.3, 15.17, 0.55948, 55, 1.62, 19.63, 0.25332, 56, -0.73, 25.58, 0.00091, 5, 53, 27.11, 18.12, 0.06561, 54, 10.41, 16.34, 0.47117, 55, 5.38, 17.6, 0.44993, 56, 1, 21.67, 0.0127, 57, -8.47, 24.29, 0.00059, 5, 53, 31.02, 21.29, 0.01259, 54, 14.89, 18.64, 0.25925, 55, 10.18, 16.11, 0.63528, 56, 3.89, 17.56, 0.08042, 57, -6.6, 19.62, 0.01246, 5, 53, 33.69, 25.08, 0.0009, 54, 18.28, 21.81, 0.10832, 55, 14.82, 16.01, 0.59077, 56, 7.5, 14.64, 0.23516, 57, -3.77, 15.95, 0.06485, 4, 54, 21.78, 26.46, 0.02626, 55, 20.58, 16.89, 0.29886, 56, 12.6, 11.82, 0.39258, 57, 0.54, 12.02, 0.2823, 5, 54, 24.08, 30.49, 0.00532, 55, 25.04, 18.16, 0.10455, 56, 16.9, 10.1, 0.25365, 57, 4.33, 9.35, 0.63558, 58, -5.61, 12.53, 0.00091, 5, 54, 26.05, 35.66, 0.0001, 55, 30.06, 20.48, 0.01289, 56, 22.29, 8.87, 0.03218, 57, 9.29, 6.91, 0.85998, 58, -1.99, 8.34, 0.09485, 4, 55, 33.08, 23.11, 0.00075, 56, 26.29, 9.1, 0.00056, 57, 13.23, 6.21, 0.52347, 58, 1.37, 6.16, 0.47522, 1, 58, 9.78, 3.13, 1, 1, 58, 23.89, 3, 1, 1, 58, 6, -4.71, 1, 2, 57, 14.44, -4.98, 0.68645, 58, -1.88, -4.61, 0.31355, 3, 56, 25.52, -4.91, 0.00031, 57, 9.25, -7.25, 0.99894, 58, -7.55, -4.67, 0.00075, 2, 56, 18.04, -7.98, 0.35551, 57, 1.26, -8.5, 0.64449, 2, 56, 14.02, -10.59, 0.75352, 57, -3.26, -10.11, 0.24648, 2, 56, 4.03, -13.54, 0.99822, 57, -13.65, -10.67, 0.00178, 2, 55, 20.43, -13.19, 0.13457, 56, -5.91, -11.89, 0.86543, 3, 54, 32.05, -8.6, 0.13992, 55, 3.41, -15.37, 0.7408, 56, -20.71, -3.22, 0.11927, 3, 54, 25.85, -13.05, 0.43705, 55, -4.13, -14.21, 0.5364, 56, -25.97, 2.31, 0.02655, 3, 54, 18.89, -14.32, 0.75527, 55, -10, -10.25, 0.24245, 56, -28.2, 9.03, 0.00228, 3, 53, 29.55, -11.32, 0.02731, 54, 6.79, -12.98, 0.97205, 55, -17.71, -0.84, 0.00064, 2, 53, 20.29, -9.48, 0.51661, 54, -1.9, -9.29, 0.48339, 3, 52, 27.9, -12.2, 0.03082, 53, 9.76, -8.18, 0.96836, 54, -11.94, -5.87, 0.00082, 2, 52, 21.04, -9.09, 0.47287, 53, 2.32, -9.36, 0.52713, 2, 52, 11.5, -5.89, 0.99039, 53, -7.4, -11.93, 0.00961, 1, 52, 1.86, -6.92, 1, 1, 52, -5.4, -9.79, 1, 1, 52, -10.34, -15.02, 1, 1, 52, -12.91, -15, 1, 1, 52, -15.75, -10.09, 1, 2, 57, 14.71, -0.04, 0.38219, 58, 0.3, -0.17, 0.61781, 2, 56, 15.19, -0.28, 0.38731, 57, 0.27, -0.35, 0.61269, 1, 56, 0.11, -0.42, 1, 3, 54, 19.31, 0.12, 0.33697, 55, 0.4, -0.22, 0.66277, 56, -13.84, 10.61, 0.00026, 3, 53, 20.21, 0.02, 0.51135, 54, -0.03, 0.03, 0.48863, 55, -13.5, 13.23, 2e-05, 2, 52, 24.29, 0.05, 0.49238, 53, 0.01, 0.06, 0.50762, 1, 52, -0.18, 0.14, 1, 2, 52, 10.54, -0.06, 1, 53, -11.42, -7.59, 0, 3, 53, 11.19, 0.27, 0.99961, 54, -8.82, 2.11, 0.00036, 55, -18.32, 20.86, 3e-05, 3, 53, 29.73, 2.32, 0.00029, 54, 9.76, 0.33, 0.99691, 55, -6.28, 6.61, 0.0028, 1, 55, 9.18, -0.08, 1, 1, 56, 7.46, -0.14, 1, 4, 54, 33.11, 34.11, 0, 55, 34.02, 14.44, 4e-05, 56, 21.73, 1.66, 0.0001, 57, 7.08, 0.02, 0.99986, 1, 58, 7.83, -0.1, 1], "hull": 36}}}}], "animations": {"idle": {"bones": {"xiaoniu2": {"translate": [{"y": -0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "y": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "y": -0.1}]}, "honghaier2": {"translate": [{"y": -0.5, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "y": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "y": -0.5}]}, "honghaier5": {"rotate": [{"angle": 1.26, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2333, "angle": 4.52, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -4.35, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.2, "angle": 1.26}]}, "honghaier5b": {"rotate": [{"angle": -5.31, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.4667, "angle": 6.99, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": -7.15, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.2, "angle": -5.31}]}, "honghaier5c": {"rotate": [{"angle": -8.41, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1333, "angle": -11.07, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": 9.38, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.2, "angle": -8.41}]}, "honghaier5d": {"rotate": [{"angle": 6.62, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.3667, "angle": -16.05, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": 19.8, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.2, "angle": 6.62}]}, "honghaier6": {"rotate": [{"angle": 0.47, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -0.99, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": 0.47}], "translate": [{"y": -0.76, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "y": 0.55, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "y": -0.76}]}, "honghaier7": {"rotate": [{"angle": 0.75, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.1667, "angle": 1.32, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -1.78, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1.2, "angle": 0.75}], "translate": [{"x": 0.32, "y": -0.03, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "x": 0.64, "y": -0.06, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.2, "x": 0.32, "y": -0.03}]}, "honghaier0": {"rotate": [{"angle": 0.59, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3, "angle": 2.18, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -0.99, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.2, "angle": 0.59}]}, "honghaier1": {"rotate": [{"angle": -2.1, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.1667, "angle": -3.84, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 5.6, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1.2, "angle": -2.1}]}, "honghaier4": {"rotate": [{"angle": -3.86, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3, "angle": -9.11, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": 1.38, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.2, "angle": -3.86}]}, "honghaier8": {"rotate": [{"angle": 5.63, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.4667, "angle": -4.29, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": 7.87, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 1.2, "angle": 5.63}]}, "honghaier9": {"rotate": [{"angle": 2.45, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -4.72, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": 2.45}]}, "honghaier10": {"rotate": [{"angle": 6.69, "curve": 0.362, "c2": 0.64, "c3": 0.698}, {"time": 0.0667, "angle": 7.53, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -8.72, "curve": 0.245, "c3": 0.707, "c4": 0.82}, {"time": 1.2, "angle": 6.69}]}, "honghaier12": {"rotate": [{"angle": 0.27, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3, "angle": 1.55, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -1.01, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.2, "angle": 0.27}]}, "honghaier13": {"rotate": [{"angle": -2.7, "curve": 0.306, "c2": 0.23, "c3": 0.756}, {"time": 0.5, "angle": 3.31, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "angle": -3.24, "curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 1.2, "angle": -2.7}]}, "honghaier14": {"rotate": [{"angle": -3.44, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1333, "angle": -4.62, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": 4.4, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.2, "angle": -3.44}]}, "honghaier11": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -3.24, "curve": 0.25, "c3": 0.75}, {"time": 1.2}]}, "honghaier15": {"rotate": [{"angle": 0.25, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3, "angle": 2.34, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -1.84, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.2, "angle": 0.25}]}, "honghaier22": {"rotate": [{"angle": -9.55, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.5333, "angle": 8.45, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": -10.35, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 1.2, "angle": -9.55}]}, "honghaier23": {"rotate": [{"angle": -7.75, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1667, "angle": -12.93, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 8.45, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1.2, "angle": -7.75}]}, "bone2": {"rotate": [{"angle": -0.45, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2, "angle": -1.58, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": 2.13, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 1.2, "angle": -0.45}]}, "bone2b": {"rotate": [{"angle": 2.85, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.4333, "angle": -6.56, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "angle": 5.86, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 1.2, "angle": 2.85}]}, "bone2c": {"rotate": [{"angle": 13.59, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.0333, "angle": 14, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -9.99, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 1.2, "angle": 13.59}]}, "xiaoniu10": {"translate": [{"y": 2.32, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.5, "y": -7.29, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "y": 3.35, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 1.2, "y": 2.32}]}, "xiaoniu9": {"translate": [{"y": 4.11, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.1333, "y": 7.34, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "y": -13.58, "curve": 0.243, "c3": 0.674, "c4": 0.69}, {"time": 1.2, "y": 4.11}]}, "xiaoniu8": {"translate": [{"y": -7.13, "curve": 0.361, "c2": 0.44, "c3": 0.755}, {"time": 0.3667, "y": 8.01, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "y": -15.4, "curve": 0.259, "c3": 0.618, "c4": 0.45}, {"time": 1.2, "y": -7.13}]}, "xiaoniu7": {"translate": [{"y": -14.4, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "y": 8.71, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "y": -14.4}]}, "xiaoniu6": {"translate": [{"y": -3.43, "curve": 0.369, "c2": 0.47, "c3": 0.753}, {"time": 0.3333, "y": 3.09, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "y": -8.29, "curve": 0.254, "c3": 0.621, "c4": 0.47}, {"time": 1.2, "y": -3.43}]}, "xiaoniu5": {"translate": [{"y": 3.22, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1, "y": 5.29, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "y": -16.25, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 1.2, "y": 3.22}]}, "xiaoniu4": {"translate": [{"y": 5.03, "curve": 0.326, "c2": 0.31, "c3": 0.757}, {"time": 0.4667, "y": -14.07, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "y": 8.52, "curve": 0.28, "c3": 0.623, "c4": 0.39}, {"time": 1.2, "y": 5.03}]}, "honghaier_0009": {"translate": [{"y": -5.61, "curve": 0.382, "c2": 0.55, "c3": 0.741}, {"time": 0.2333, "y": -14.4, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "y": 10.5, "curve": 0.245, "c3": 0.639, "c4": 0.56}, {"time": 1.2, "y": -5.61}]}, "honghaier_8": {"rotate": [{"angle": 22.27, "curve": 0.362, "c2": 0.64, "c3": 0.698}, {"time": 0.0667, "angle": 24.25, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -14.27, "curve": 0.245, "c3": 0.707, "c4": 0.82}, {"time": 1.2, "angle": 22.27}]}, "honghaier_7": {"rotate": [{"angle": 10.15, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.4, "angle": -12.23, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 19.02, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.2, "angle": 10.15}]}, "honghaier_6": {"rotate": [{"angle": -10.16, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.1333, "angle": -14.6, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": 14.23, "curve": 0.243, "c3": 0.674, "c4": 0.69}, {"time": 1.2, "angle": -10.16}]}, "honghaier_5": {"rotate": [{"angle": -7.46, "curve": 0.326, "c2": 0.31, "c3": 0.757}, {"time": 0.4667, "angle": 11.39, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": -10.89, "curve": 0.28, "c3": 0.623, "c4": 0.39}, {"time": 1.2, "angle": -7.46}]}, "honghaier_4": {"rotate": [{"angle": 5.14, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.2, "angle": 10.47, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": -8.32, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.2, "angle": 5.14}]}, "honghaier_3": {"rotate": [{"angle": 9.25, "curve": 0.293, "c2": 0.18, "c3": 0.755}, {"time": 0.5333, "angle": -5.47, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": 10.05, "curve": 0.302, "c3": 0.638, "c4": 0.36}, {"time": 1.2, "angle": 9.25}]}, "honghaier_0003": {"rotate": [{"angle": 2.12, "curve": 0.379, "c2": 0.53, "c3": 0.746}, {"time": 0.2667, "angle": -1.99, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": 7.63, "curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 1.2, "angle": 2.12}]}, "honghaier_0000": {"translate": [{"y": 1.41, "curve": 0.369, "c2": 0.47, "c3": 0.753}, {"time": 0.3333, "y": -0.83, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "y": 3.08, "curve": 0.254, "c3": 0.621, "c4": 0.47}, {"time": 1.2, "y": 1.41}]}, "honghaier_16": {"rotate": [{"angle": 2.34, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3, "angle": -23.23, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": 27.91, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.2, "angle": 2.34}]}, "honghaier_15": {"rotate": [{"angle": -16.51, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 25.38, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": -16.51}]}, "honghaier_14": {"rotate": [{"angle": 0.79, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3, "angle": 20.12, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -18.55, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.2, "angle": 0.79}]}, "honghaier_13": {"rotate": [{"angle": 17.36, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -18.33, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": 17.36}]}, "honghaier_12": {"rotate": [{"angle": 2.63, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3, "angle": -13.76, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": 19.03, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.2, "angle": 2.63}]}, "honghaier_11": {"rotate": [{"angle": -9.37, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 8.72, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": -9.37}]}, "honghaier_0011": {"rotate": [{"angle": -0.42, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3, "angle": 3.69, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -4.53, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.2, "angle": -0.42}]}, "honghaier_0": {"translate": [{"y": 3.6, "curve": 0.326, "c2": 0.31, "c3": 0.757}, {"time": 0.4667, "y": -1.27, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "y": 4.49, "curve": 0.28, "c3": 0.623, "c4": 0.39}, {"time": 1.2, "y": 3.6}]}}}, "move": {"bones": {"xiaoniu2": {"translate": [{"y": -0.22}, {"time": 0.1333, "x": -1.43, "y": -0.92}, {"time": 0.2667, "x": -2.86, "y": 0.49}, {"time": 0.4, "x": -3.47, "y": 3.32}, {"time": 0.5333, "y": -0.22}, {"time": 0.6667, "x": -1.43, "y": -0.92}, {"time": 0.8, "x": -2.86, "y": 0.49}, {"time": 0.9333, "x": -3.47, "y": 3.32}, {"time": 1.0667, "y": -0.22}]}, "leg1": {"rotate": [{"angle": -32.93}, {"time": 0.1333, "angle": -7.41}, {"time": 0.2667, "angle": 8.08}, {"time": 0.4, "angle": 17.61}, {"time": 0.5333, "angle": 34.7}, {"time": 0.6667, "angle": 54.81}, {"time": 0.8, "angle": 64.52}, {"time": 0.9333, "angle": 32.54}, {"time": 1.0667, "angle": -32.93}], "translate": [{"x": -24.92, "y": 1.12}, {"time": 0.1333, "x": -6.02, "y": -2.62}, {"time": 0.2667, "x": 23.91, "y": 1.53}, {"time": 0.4, "x": 35.98, "y": 5.37}, {"time": 0.5333, "x": 47.86, "y": 9.17}, {"time": 0.6667, "x": 49.37, "y": 15}, {"time": 0.8, "x": 27.39, "y": 25.54}, {"time": 0.9333, "x": 2.27, "y": 9.81}, {"time": 1.0667, "x": -24.92, "y": 1.12}]}, "leg2": {"rotate": [{"angle": 33.61}, {"time": 0.1333, "angle": 40.69}, {"time": 0.2667, "angle": 48.47}, {"time": 0.4, "angle": 19.62}, {"time": 0.5333, "angle": -48.39}, {"time": 0.6667, "angle": -18.73}, {"time": 0.8, "angle": -21.06}, {"time": 0.9333, "angle": 4.98}, {"time": 1.0667, "angle": 33.61}], "translate": [{"x": 24.1, "y": 12}, {"time": 0.1333, "x": 10.41, "y": 20.71}, {"time": 0.2667, "x": -7.19, "y": 33.95}, {"time": 0.4, "x": -32.18, "y": 25.98}, {"time": 0.5333, "x": -58.55, "y": 7.89}, {"time": 0.6667, "x": -32.36, "y": 2.05}, {"time": 0.8, "x": -12.18, "y": 0.93}, {"time": 0.9333, "x": 1.53, "y": 6.37}, {"time": 1.0667, "x": 24.1, "y": 12}]}, "honghaier1": {"rotate": [{"angle": -26.86, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 8.1, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": -26.86}]}, "honghaier2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -2.36, "curve": 0.25, "c3": 0.75}, {"time": 1.0667}], "translate": [{"y": -0.5, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "y": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "y": -0.5}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": 0.96, "curve": 0.25, "c3": 0.75}, {"time": 1.0667}]}, "honghaier5": {"rotate": [{"angle": 1.26, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2, "angle": 7.3, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": -15.96, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.0667, "angle": 1.26}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": -3.55, "y": -0.09, "curve": 0.25, "c3": 0.75}, {"time": 1.0667}]}, "honghaier6": {"rotate": [{"angle": 0.47, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -0.99, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": 0.47}], "translate": [{"y": -0.76, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "y": 0.55, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "y": -0.76}]}, "honghaier7": {"rotate": [{"angle": 0.75, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.1333, "angle": 1.32, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -1.78, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1.0667, "angle": 0.75}], "translate": [{"x": 0.32, "y": -0.03, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "x": 0.64, "y": -0.06, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.0667, "x": 0.32, "y": -0.03}]}, "honghaier0": {"rotate": [{"angle": 0.59, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667, "angle": 2.18, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": -0.99, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.0667, "angle": 0.59}]}, "honghaier4": {"rotate": [{"angle": -15.41, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -5.7, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": -15.41}]}, "honghaier8": {"rotate": [{"angle": 3.01, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 0.27, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": 3.01}]}, "honghaier9": {"rotate": [{"angle": 2.45, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -4.72, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": 2.45}]}, "honghaier10": {"rotate": [{"angle": 6.69, "curve": 0.362, "c2": 0.64, "c3": 0.698}, {"time": 0.0667, "angle": 7.53, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -8.72, "curve": 0.245, "c3": 0.707, "c4": 0.82}, {"time": 1.0667, "angle": 6.69}]}, "honghaier12": {"rotate": [{"angle": 6.96, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -0.68, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": 6.96}]}, "honghaier13": {"rotate": [{"angle": 3.77, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -3.68, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": 3.77}]}, "honghaier14": {"rotate": [{"angle": -3.44, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -3.05, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": -3.44}]}, "honghaier11": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -3.24, "curve": 0.25, "c3": 0.75}, {"time": 1.0667}]}, "honghaier15": {"rotate": [{"angle": 0.25, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667, "angle": -4.98, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": -13.7, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.0667, "angle": 0.25}]}, "honghaier22": {"rotate": [{"angle": -9.55, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.4667, "angle": 8.45, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -10.35, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 1.0667, "angle": -9.55}]}, "honghaier23": {"rotate": [{"angle": -7.75, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1333, "angle": -12.93, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 8.45, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1.0667, "angle": -7.75}]}, "bone2": {"rotate": [{"angle": -0.45, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1667, "angle": -1.58, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": 2.13, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 1.0667, "angle": -0.45}]}, "bone2b": {"rotate": [{"angle": 2.85, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.4, "angle": -6.56, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 5.86, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 1.0667, "angle": 2.85}]}, "bone2c": {"rotate": [{"angle": 13.59, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.0333, "angle": 14, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -9.99, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 1.0667, "angle": 13.59}]}, "honghaier16": {"rotate": [{"angle": -9.65}, {"time": 0.5333, "angle": 24.41}, {"time": 1.0667, "angle": -9.65}], "translate": [{"y": -1.91}, {"time": 0.2667, "y": -2.48}, {"time": 0.4, "y": -5.93}, {"time": 0.5333, "y": -3.05}, {"time": 1.0667, "y": -1.91}]}, "honghaier20": {"scale": [{}, {"time": 0.1333, "x": 0.989}, {"time": 0.2667, "x": 0.966}, {"time": 0.4, "x": 0.964}, {"time": 0.5333}]}, "honghaier5b": {"rotate": [{"angle": -5.31, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.4, "angle": 8.94, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": -9.33, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.0667, "angle": -5.31}]}, "honghaier5c": {"rotate": [{"angle": -8.41, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1333, "angle": -20.24, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 14, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.0667, "angle": -8.41}]}, "honghaier5d": {"rotate": [{"angle": 6.62, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.3333, "angle": -25.24, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": 28.59, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.0667, "angle": 6.62}]}, "xiaoniu9": {"translate": [{"y": 4.11, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.1333, "y": 7.34, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 4.75, "y": -13.58, "curve": 0.243, "c3": 0.674, "c4": 0.69}, {"time": 1.0667, "y": 4.11}]}, "xiaoniu8": {"translate": [{"y": -7.13, "curve": 0.361, "c2": 0.44, "c3": 0.755}, {"time": 0.3333, "y": 8.01, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "x": 3.29, "y": -15.4, "curve": 0.259, "c3": 0.618, "c4": 0.45}, {"time": 1.0667, "y": -7.13}]}, "xiaoniu4": {"translate": [{"y": 5.03, "curve": 0.326, "c2": 0.31, "c3": 0.757}, {"time": 0.4, "y": -14.07, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "y": 8.52, "curve": 0.28, "c3": 0.623, "c4": 0.39}, {"time": 1.0667, "y": 5.03}]}, "honghaier_0000": {"translate": [{"x": -14.68, "y": 1.41, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 4.47, "y": 1.41, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "x": -14.68, "y": 1.41}]}, "xiaoniu5": {"translate": [{"y": 3.22, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1, "y": 5.29, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "y": -16.25, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 1.0667, "y": 3.22}]}, "xiaoniu7": {"translate": [{"y": -14.4, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": 4.46, "y": 8.71, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "y": -14.4}]}, "honghaier_13": {"rotate": [{"angle": 17.36, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -18.33, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": 17.36}]}, "xiaoniu10": {"translate": [{"x": -8.03, "y": 2.32, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.4333, "x": 5.85, "y": -7.29, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "x": -8.48, "y": 3.35, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 1.0667, "x": -8.03, "y": 2.32}]}, "xiaoniu3": {"translate": [{"x": -3.49, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": 0.91, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "x": -3.49}]}, "honghaier_16": {"rotate": [{"angle": 2.34, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667, "angle": -23.23, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": 27.91, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.0667, "angle": 2.34}]}, "honghaier_5": {"rotate": [{"angle": -7.46, "curve": 0.326, "c2": 0.31, "c3": 0.757}, {"time": 0.4, "angle": 11.39, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": -10.89, "curve": 0.28, "c3": 0.623, "c4": 0.39}, {"time": 1.0667, "angle": -7.46}]}, "honghaier_7": {"rotate": [{"angle": 10.15, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.3667, "angle": -12.23, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": 19.02, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.0667, "angle": 10.15}]}, "honghaier_4": {"rotate": [{"angle": 5.14, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "angle": 10.47, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -8.32, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.0667, "angle": 5.14}]}, "honghaier_15": {"rotate": [{"angle": -16.51, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 25.38, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": -16.51}]}, "xiaoniu6": {"translate": [{"y": -3.43, "curve": 0.369, "c2": 0.47, "c3": 0.753}, {"time": 0.3, "x": 2.37, "y": 3.09, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 3.39, "y": -8.29, "curve": 0.254, "c3": 0.621, "c4": 0.47}, {"time": 1.0667, "y": -3.43}]}, "honghaier_0011": {"rotate": [{"angle": -0.42, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667, "angle": 3.69, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": -4.53, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.0667, "angle": -0.42}]}, "honghaier_3": {"rotate": [{"angle": 9.25, "curve": 0.293, "c2": 0.18, "c3": 0.755}, {"time": 0.4667, "angle": -5.47, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 10.05, "curve": 0.302, "c3": 0.638, "c4": 0.36}, {"time": 1.0667, "angle": 9.25}]}, "honghaier_0003": {"rotate": [{"angle": 2.12, "curve": 0.379, "c2": 0.53, "c3": 0.746}, {"time": 0.2333, "angle": -1.99, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 7.63, "curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 1.0667, "angle": 2.12}]}, "honghaier_8": {"rotate": [{"angle": 22.27, "curve": 0.362, "c2": 0.64, "c3": 0.698}, {"time": 0.0667, "angle": 24.25, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -14.27, "curve": 0.245, "c3": 0.707, "c4": 0.82}, {"time": 1.0667, "angle": 22.27}]}, "honghaier_0009": {"translate": [{"y": -5.61, "curve": 0.382, "c2": 0.55, "c3": 0.741}, {"time": 0.2, "y": -14.4, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "y": 10.5, "curve": 0.245, "c3": 0.639, "c4": 0.56}, {"time": 1.0667, "y": -5.61}]}, "honghaier_12": {"rotate": [{"angle": 2.63, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667, "angle": -13.76, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": 19.03, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.0667, "angle": 2.63}]}, "honghaier_6": {"rotate": [{"angle": -10.16, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.1333, "angle": -14.6, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 14.23, "curve": 0.243, "c3": 0.674, "c4": 0.69}, {"time": 1.0667, "angle": -10.16}]}, "honghaier_14": {"rotate": [{"angle": 0.79, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667, "angle": 20.12, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": -18.55, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.0667, "angle": 0.79}]}, "honghaier_11": {"rotate": [{"angle": -9.37, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 8.72, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": -9.37}]}, "honghaier_0": {"translate": [{"x": 3.51, "y": -0.17, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": -7.22, "y": -0.17, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "x": 3.51, "y": -0.17}]}}, "deform": {"default": {"honghaier3": {"honghaier3": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5333, "offset": 58, "vertices": [-0.86005, -0.66219, -0.73935, -0.79474, 0.29823, -1.04367, -0.90695, -0.59631, -0.86005, -0.66219, -0.73935, -0.79474, 0.29823, -1.04367, -0.90695, -0.59631, -0.86005, -0.66219, -0.73935, -0.79474, 0.29823, -1.04367, -0.90695, -0.59631, -0.86005, -0.66219, -0.73935, -0.79474, 0.41754, -1.46112, -1.2697, -0.83485, -1.20405, -0.92707, 0.41754, -1.46112, -1.2697, -0.83485, -1.20405, -0.92707, 0.41754, -1.46112, 0.41795, -1.46102, -1.2697, -0.83485, -1.20405, -0.92707, 0.41754, -1.46112, 0.41795, -1.46102, -1.2697, -0.83485, -1.20405, -0.92707, 0.41754, -1.46112, 0.41795, -1.46102, 0.32684, -1.48406, -1.2697, -0.83485, 0.41795, -1.46102, 0.32684, -1.48406, 0.41795, -1.46102, 0.32684, -1.48406, 0.32684, -1.48406, 0.49993, -2.27001, 0.09338, -0.42401, 0.41795, -1.46102, 0.32684, -1.48406, 0.41795, -1.46102, 0.32684, -1.48406, 0.41754, -1.46112, 0.41795, -1.46102, 0.32684, -1.48406, 0.41754, -1.46112, 0.41795, -1.46102, 0.41754, -1.46112, 0.41795, -1.46102, 0.41754, -1.46112, 0.41795, -1.46102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.68448, -0.00013, 0.7376, -2.58113, -1.08545, -4e-05, -0.90695, -0.59631, -0.86005, -0.66219, -0.73935, -0.79474, -1.73417, -1.14032, -1.64453, -1.26625, -2.07558, -0.00012, 0.5703, -1.99567, -1.73417, -1.14032, 0.5703, -1.99567, 0.57086, -1.99553, 0.51942, -1.81746, 0.51989, -1.81738, -1.57923, -1.03857, -1.49767, -1.15318, 0.51989, -1.81738, -1.57923, -1.03857, 0.51989, -1.81738, 0.40655, -1.846, 0.49993, -2.27001, -2.07558, -0.00012, 0.5703, -1.99567, -1.73417, -1.14032, 0.29823, -1.04367, -0.90695, -0.59631, -0.86005, -0.66219, 0.29823, -1.04367, -0.90695, -0.59631, -0.86005, -0.66219, -0.73935, -0.79474, -0.86005, -0.66219, -0.73935, -0.79474], "curve": 0.25, "c3": 0.75}, {"time": 1.0667}]}, "honghaier4": {"honghaier4": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5333, "offset": 42, "vertices": [0.65804, 4.55943, 1.06721, 4.48133, 1.75248, 4.2603, 1.45251, 4.37168, 0.79176, 6.91612, 1.41325, 6.81631, 2.00016, 6.66776], "curve": 0.25, "c3": 0.75}, {"time": 1.0667}]}, "honghaier5": {"honghaier5": [{}, {"time": 0.4, "offset": 46, "vertices": [1.35211, 5.32474, 5.48996, 0.20305, -0.69353, 5.53637, 5.11668, 2.22551, 0.84253, 5.18195, 5.20939, 0.65183, 0.12485, 4.25112, 4.11407, 1.07813, -0.26762, 3.29794, 3.08892, 1.1861]}, {"time": 0.5333}]}}}}}}