{"skeleton": {"hash": "CRCi4WGQOg8FaYNP1+wywDZcSv0", "spine": "3.8.99", "x": -194.27, "y": -35.79, "width": 298.34, "height": 273.39}, "bones": [{"name": "root"}, {"name": "all", "parent": "root"}, {"name": "allshaseng", "parent": "all", "scaleX": 0.36, "scaleY": 0.36}, {"name": "body", "parent": "allshaseng", "y": 272}, {"name": "shaseng_58", "parent": "body", "y": -1.5}, {"name": "shaseng_13", "parent": "shaseng_58", "length": 46.18, "rotation": -88.79, "x": -63.11, "y": -4.39}, {"name": "shaseng_14", "parent": "shaseng_13", "length": 40.83, "rotation": 7.7, "x": 46.18}, {"name": "shaseng_15", "parent": "shaseng_14", "length": 42.86, "rotation": 20.28, "x": 40.83}, {"name": "shaseng_16", "parent": "shaseng_15", "length": 37.6, "rotation": -14.96, "x": 42.86}, {"name": "shaseng_17", "parent": "shaseng_58", "length": 47.45, "rotation": -122.88, "x": 65.68, "y": -5.36}, {"name": "shaseng_18", "parent": "shaseng_17", "length": 48.82, "rotation": -13.74, "x": 47.45}, {"name": "shaseng_19", "parent": "shaseng_18", "length": 51.96, "rotation": 39.09, "x": 48.82}, {"name": "shaseng_20", "parent": "shaseng_19", "length": 54.05, "rotation": 11.13, "x": 51.96}, {"name": "shaseng_22", "parent": "body", "length": 84.57, "rotation": 86.19, "x": -0.97, "y": -1.28}, {"name": "shaseng_23", "parent": "shaseng_22", "length": 123.84, "rotation": 6.95, "x": 84.57}, {"name": "shaseng_6", "parent": "shaseng_23", "length": 77.67, "rotation": -2.58, "x": 132.16, "y": 13.89}, {"name": "shaseng_3", "parent": "shaseng_6", "length": 47.69, "rotation": -8.69, "x": 67.09, "y": 9.06}, {"name": "shaseng_4", "parent": "shaseng_3", "length": 34.76, "rotation": -14.7, "x": 47.69}, {"name": "shaseng_5", "parent": "shaseng_4", "length": 26.77, "rotation": -28.13, "x": 34.76}, {"name": "shaseng_7", "parent": "shaseng_3", "length": 29.69, "rotation": -21.49, "x": 8.29, "y": -32.41}, {"name": "shaseng_8", "parent": "shaseng_7", "length": 32.15, "rotation": -32.2, "x": 29.69}, {"name": "shaseng_9", "parent": "shaseng_8", "length": 23.87, "rotation": 40.7, "x": 32.15}, {"name": "shaseng_10", "parent": "shaseng_3", "length": 30.57, "rotation": 27.46, "x": -1.95, "y": 21.33}, {"name": "shaseng_11", "parent": "shaseng_10", "length": 23.33, "rotation": -23.07, "x": 30.57}, {"name": "shaseng_21", "parent": "shaseng_11", "length": 19.37, "rotation": -37.03, "x": 23.33}, {"name": "shaseng_1", "parent": "shaseng_6", "length": 26.05, "rotation": -50.32, "x": 24.27, "y": 7.3}, {"name": "shaseng_2", "parent": "shaseng_1", "length": 22.31, "rotation": 1.98, "x": 26.05}, {"name": "shaseng_25", "parent": "shaseng_6", "length": 22.41, "rotation": 38.35, "x": 24.7, "y": 19.54}, {"name": "shaseng_26", "parent": "shaseng_25", "length": 19.69, "rotation": 5.47, "x": 22.61, "y": 0.24}, {"name": "shaseng_27", "parent": "shaseng_6", "length": 13.27, "rotation": 63.47, "x": 17.03, "y": 17.17}, {"name": "shaseng_28", "parent": "shaseng_27", "length": 8.23, "rotation": 67.96, "x": 13.27}, {"name": "shaseng_29", "parent": "shaseng_28", "length": 12.79, "rotation": 26.98, "x": 8.23}, {"name": "shaseng_30", "parent": "shaseng_6", "length": 9.32, "rotation": -67.36, "x": 19.07, "y": 7.36}, {"name": "shaseng_31", "parent": "shaseng_30", "length": 11.21, "rotation": -58.19, "x": 9.32}, {"name": "shaseng_32", "parent": "shaseng_31", "length": 17.23, "rotation": -35.29, "x": 11.21}, {"name": "shaseng_24", "parent": "shaseng_6", "length": 24.92, "rotation": 163.15, "x": 3.34, "y": 13.86}, {"name": "shaseng_33", "parent": "shaseng_24", "length": 29.09, "rotation": 14.12, "x": 24.92}, {"name": "shaseng_34", "parent": "shaseng_33", "length": 19.67, "rotation": 9.7, "x": 29.09}, {"name": "shaseng_35", "parent": "shaseng_34", "length": 17.12, "rotation": 20.69, "x": 19.67}, {"name": "shaseng_36", "parent": "shaseng_6", "length": 28.72, "rotation": -59.72, "x": 48.84, "y": -37.74}, {"name": "shaseng_37", "parent": "shaseng_36", "length": 31.23, "rotation": -22.71, "x": 28.9, "y": 0.32}, {"name": "shaseng_38", "parent": "shaseng_37", "length": 22.35, "rotation": 48.96, "x": 31.23}, {"name": "shaseng_39", "parent": "shaseng_38", "length": 27.84, "rotation": 62.18, "x": 22.35}, {"name": "shaseng_40", "parent": "shaseng_6", "length": 23.49, "rotation": 54.01, "x": 50.29, "y": 35.85}, {"name": "shaseng_41", "parent": "shaseng_40", "length": 19.26, "rotation": -28.49, "x": 23.49}, {"name": "shaseng_42", "parent": "shaseng_41", "length": 15.09, "rotation": -24.68, "x": 19.26}, {"name": "shaseng_43", "parent": "shaseng_42", "length": 13.07, "rotation": -33.74, "x": 15.09}, {"name": "shaseng_0", "parent": "shaseng_23", "length": 140.1, "rotation": -178.96, "x": 63.55, "y": -134.18}, {"name": "shaseng_44", "parent": "shaseng_0", "length": 109.91, "rotation": -25.98, "x": 140.1}, {"name": "shaseng_45", "parent": "shaseng_44", "length": 54.66, "rotation": -33.14, "x": 109.91}, {"name": "shaseng_47", "parent": "shaseng_23", "length": 109.05, "rotation": 167.33, "x": 127.64, "y": 73.79}, {"name": "shaseng_48", "parent": "shaseng_47", "length": 104.64, "rotation": -31.21, "x": 109.05}, {"name": "shaseng_49", "parent": "shaseng_48", "length": 29.89, "rotation": 70.67, "x": 104.64}, {"name": "shaseng_46", "parent": "shaseng_49", "length": 204.76, "rotation": -82.1, "x": 6.95, "y": 10.49}, {"name": "shaseng_51", "parent": "body", "length": 137.43, "rotation": -101.14, "x": -40.45, "y": -10.16}, {"name": "shaseng_52", "parent": "shaseng_51", "length": 111.16, "rotation": 18.18, "x": 137.43}, {"name": "shaseng_53", "parent": "shaseng_52", "length": 54.31, "rotation": -66.94, "x": 110.4, "y": -0.59}, {"name": "shaseng_54", "parent": "shaseng_53", "length": 34.48, "rotation": -21.01, "x": 54.31}, {"name": "shaseng_55", "parent": "body", "length": 188.44, "rotation": -71.89, "x": 34.46, "y": 0.52}, {"name": "shaseng_56", "parent": "shaseng_55", "length": 98.68, "rotation": 8.99, "x": 188.44}, {"name": "leg2", "parent": "allshaseng", "x": 137.69, "y": 5.91}, {"name": "shaseng_57", "parent": "leg2", "length": 71.06, "rotation": -76.49}, {"name": "shaseng_59", "parent": "body", "length": 130.53, "rotation": -122.86, "x": -39.09, "y": 1.89}, {"name": "shaseng_60", "parent": "shaseng_59", "length": 108.11, "rotation": 51.98, "x": 130.53}, {"name": "leg1", "parent": "allshaseng", "x": -74.95, "y": 62.93}, {"name": "shaseng_61", "parent": "leg1", "length": 70.82, "rotation": -157.08}, {"name": "shaseng_12", "parent": "shaseng_58", "x": 70.27, "y": -30.19}, {"name": "shaseng_50", "parent": "shaseng_12", "length": 21.62, "rotation": 103.78, "x": -0.46, "y": 1.52}, {"name": "shaseng_50b", "parent": "shaseng_12", "length": 21.62, "rotation": -69.57, "x": -0.37, "y": 0.28}, {"name": "shaseng_50c", "parent": "shaseng_50b", "length": 21.62, "x": 21.62}, {"name": "shaseng_63", "parent": "shaseng_58", "length": 28.69, "rotation": -130.79, "x": -76.79, "y": 15}, {"name": "shaseng_64", "parent": "shaseng_46", "length": 34.59, "rotation": 91.52, "x": 181.57, "y": 22.85}, {"name": "shaseng_65", "parent": "shaseng_46", "length": 42.02, "rotation": -16.17, "x": 165.14, "y": -31.45}, {"name": "shaseng_63b", "parent": "shaseng_63", "length": 28.69, "x": 28.69}, {"name": "shaseng_63c", "parent": "shaseng_63b", "length": 28.69, "x": 28.69}, {"name": "shaseng_77", "parent": "shaseng_58"}, {"name": "shaseng_66", "parent": "shaseng_77", "length": 91.42, "rotation": -64.75, "x": -81.79, "y": 16.15}, {"name": "shaseng_67", "parent": "shaseng_66", "length": 66.87, "rotation": 11.53, "x": 91.42}, {"name": "shaseng_68", "parent": "shaseng_67", "length": 49.18, "rotation": -3.43, "x": 66.46, "y": 0.31}, {"name": "shaseng_69", "parent": "shaseng_68", "length": 45.23, "rotation": -13.88, "x": 49.18}, {"name": "shaseng_70", "parent": "shaseng_77", "length": 58.87, "rotation": -65.47, "x": 83.05, "y": 4.71}, {"name": "shaseng_71", "parent": "shaseng_70", "length": 48.96, "rotation": -2.05, "x": 58.87}, {"name": "shaseng_72", "parent": "shaseng_71", "length": 37.67, "rotation": 3.73, "x": 48.96}, {"name": "shaseng_73", "parent": "shaseng_72", "length": 27.29, "rotation": 4.75, "x": 37.67}, {"name": "shaseng_62", "parent": "body", "length": 137.43, "rotation": -93.94, "x": 47.63, "y": -17.06}, {"name": "shaseng_74", "parent": "shaseng_62", "length": 111.16, "rotation": 18.18, "x": 137.43}, {"name": "shaseng_75", "parent": "shaseng_74", "length": 54.31, "rotation": -66.94, "x": 110.4, "y": -0.59}, {"name": "shaseng_76", "parent": "shaseng_75", "length": 34.48, "rotation": -21.01, "x": 54.31}, {"name": "<PERSON><PERSON><PERSON>", "parent": "shaseng_46", "rotation": 3.41, "x": 284.15, "y": 13.41, "scaleY": 1.0975}, {"name": "a_canying2", "parent": "all", "y": 100, "scaleX": 1.3, "scaleY": 1.8}, {"name": "daoguang1", "parent": "all", "length": 130.75, "x": -325.45, "y": -15, "scaleX": 1.09, "scaleY": 1.953}, {"name": "diguang_0", "parent": "all", "length": 378.73, "x": -270, "y": -15, "scaleX": 0.6503, "scaleY": 0.6624}, {"name": "d1", "parent": "all", "x": 12.26, "y": 171.06, "scaleX": 1.413, "scaleY": 1.413}, {"name": "daoguang2", "parent": "all", "length": 130, "x": -314.42, "y": -16, "scaleX": 5.3979, "scaleY": 7.9073}, {"name": "6_00000", "parent": "all", "scaleX": -1}, {"name": "atu2", "parent": "all", "scaleX": -1, "scaleY": 0.75}, {"name": "all0", "parent": "root"}, {"name": "alltx", "parent": "all0"}, {"name": "xuewu7", "parent": "alltx", "rotation": -85.8, "x": 15.91, "y": -6.13}, {"name": "bing", "parent": "alltx", "rotation": -0.28, "scaleX": 2, "scaleY": 1.5}, {"name": "xuewu8", "parent": "alltx", "rotation": -85.8, "x": 15.91, "y": -6.13}, {"name": "xuewu9", "parent": "alltx", "rotation": -85.8, "x": 15.91, "y": -6.13}, {"name": "bing2", "parent": "alltx", "rotation": -2.56, "scaleX": 2, "scaleY": 1.5}, {"name": "xuewu10", "parent": "alltx", "rotation": -85.8, "x": 15.91, "y": -6.13}, {"name": "xuewu11", "parent": "alltx", "rotation": -85.8, "x": 15.91, "y": -6.13}, {"name": "xuewu12", "parent": "alltx", "rotation": -85.8, "x": 15.91, "y": -6.13}, {"name": "lanse", "parent": "alltx"}, {"name": "b", "parent": "alltx", "scaleY": 1.25}, {"name": "bing3", "parent": "alltx", "rotation": -2.56, "scaleX": 2, "scaleY": 1.5}, {"name": "a1", "parent": "alltx", "scaleY": 1.25}, {"name": "fasan", "parent": "all0", "length": 159.51, "rotation": 87.24}, {"name": "dilie1s", "parent": "all0", "rotation": 180, "scaleY": 0.75}, {"name": "ag1", "parent": "all0", "rotation": -76, "x": -154, "y": -16, "scaleX": 2.22, "scaleY": 1.9}, {"name": "shaseng_78", "parent": "shaseng_46", "length": 204.76, "scaleY": 1.2049}, {"name": "asansheguang222", "parent": "alltx"}, {"name": "01_00", "parent": "all0", "length": 143.14, "rotation": 90}, {"name": "01_0", "parent": "all0", "length": 143.14, "rotation": 90}, {"name": "01_1", "parent": "all0", "length": 143.14, "rotation": 90}], "slots": [{"name": "dilie1s", "bone": "dilie1s"}, {"name": "bing", "bone": "bing", "color": "ffffff6a"}, {"name": "bing2", "bone": "bing2", "color": "ffffff91"}, {"name": "bing3", "bone": "bing3", "color": "ffffff91"}, {"name": "a1", "bone": "a1"}, {"name": "b", "bone": "b"}, {"name": "xuewu7", "bone": "xuewu7"}, {"name": "xuewu12", "bone": "xuewu12"}, {"name": "xuewu10", "bone": "xuewu10"}, {"name": "xuewu9", "bone": "xuewu9", "color": "ffffff6b"}, {"name": "xuewu8", "bone": "xuewu8", "color": "ffffff6b"}, {"name": "xuewu11", "bone": "xuewu11", "color": "ffffff6b"}, {"name": "lanse", "bone": "lanse", "color": "ffffff92"}, {"name": "diguang_0", "bone": "diguang_0"}, {"name": "ag1", "bone": "ag1"}, {"name": "asansheguang222", "bone": "asansheguang222"}, {"name": "shaseng_19", "bone": "shaseng_46", "attachment": "shaseng_20"}, {"name": "shaseng_21", "bone": "shaseng_78"}, {"name": "shaseng_18", "bone": "shaseng_64", "attachment": "shaseng_18"}, {"name": "shaseng_17", "bone": "shaseng_65", "attachment": "shaseng_17"}, {"name": "shaseng_16", "bone": "shaseng_47", "attachment": "shaseng_16"}, {"name": "shaseng_15", "bone": "shaseng_51"}, {"name": "shaseng_8", "bone": "shaseng_63", "attachment": "shaseng_8"}, {"name": "shaseng_20", "bone": "shaseng_62"}, {"name": "shaseng_14", "bone": "shaseng_59", "attachment": "shaseng_14"}, {"name": "shaseng_13", "bone": "shaseng_55", "attachment": "shaseng_13"}, {"name": "shaseng_12", "bone": "shaseng_22", "attachment": "shaseng_12"}, {"name": "shaseng_11", "bone": "shaseng_70", "attachment": "shaseng_11"}, {"name": "shaseng_10", "bone": "shaseng_58", "attachment": "shaseng_10"}, {"name": "shaseng_9", "bone": "shaseng_58", "attachment": "shaseng_9"}, {"name": "shaseng_7", "bone": "shaseng_12", "attachment": "shaseng_7"}, {"name": "shaseng_6", "bone": "shaseng_6", "attachment": "shaseng_6"}, {"name": "shaseng_5", "bone": "shaseng_27", "attachment": "shaseng_5"}, {"name": "shaseng_4", "bone": "shaseng_30", "attachment": "shaseng_4"}, {"name": "shaseng_3", "bone": "shaseng_3", "attachment": "shaseng_3"}, {"name": "shaseng_2", "bone": "shaseng_25", "attachment": "shaseng_2"}, {"name": "shaseng_1", "bone": "shaseng_1", "attachment": "shaseng_1"}, {"name": "shaseng_0", "bone": "shaseng_0", "attachment": "shaseng_0"}, {"name": "daoying1", "bone": "<PERSON><PERSON><PERSON>", "attachment": "daoying1"}, {"name": "<PERSON><PERSON><PERSON>", "bone": "<PERSON><PERSON><PERSON>", "attachment": "<PERSON><PERSON><PERSON>"}, {"name": "a_canying", "bone": "a_canying2"}, {"name": "daoguang3", "bone": "daoguang1"}, {"name": "daoguang5", "bone": "daoguang2"}, {"name": "d1", "bone": "d1"}, {"name": "6_00000", "bone": "6_00000"}, {"name": "fasan", "bone": "fasan"}, {"name": "atu2", "bone": "atu2"}, {"name": "01_00", "bone": "01_00"}, {"name": "01_0", "bone": "01_0"}, {"name": "01_1", "bone": "01_1"}], "ik": [{"name": "leg1", "bones": ["shaseng_59", "shaseng_60"], "target": "leg1"}, {"name": "leg2", "order": 1, "bones": ["shaseng_55", "shaseng_56"], "target": "leg2"}], "skins": [{"name": "default", "attachments": {"01_0": {"01_00": {"x": 80, "rotation": 90, "width": 50, "height": 159}, "01_02": {"x": 79.5, "rotation": 90, "width": 50, "height": 159}, "01_04": {"x": 79.5, "rotation": 90, "width": 50, "height": 159}, "01_06": {"x": 79.5, "rotation": 90, "width": 50, "height": 159}, "01_08": {"x": 79.5, "rotation": 90, "width": 50, "height": 159}}, "01_00": {"01_00": {"x": 80, "rotation": 90, "width": 50, "height": 159}, "01_02": {"x": 79.5, "rotation": 90, "width": 50, "height": 159}, "01_04": {"x": 79.5, "rotation": 90, "width": 50, "height": 159}, "01_06": {"x": 79.5, "rotation": 90, "width": 50, "height": 159}, "01_08": {"x": 79.5, "rotation": 90, "width": 50, "height": 159}}, "01_1": {"01_00": {"x": 80, "rotation": 90, "width": 50, "height": 159}, "01_02": {"x": 79.5, "rotation": 90, "width": 50, "height": 159}, "01_04": {"x": 79.5, "rotation": 90, "width": 50, "height": 159}, "01_06": {"x": 79.5, "rotation": 90, "width": 50, "height": 159}, "01_08": {"x": 79.5, "rotation": 90, "width": 50, "height": 159}}, "6_00000": {"6_00000": {"width": 75, "height": 60}, "6_00002": {"width": 75, "height": 60}, "6_00004": {"width": 75, "height": 60}, "6_00006": {"width": 75, "height": 60}, "6_00008": {"width": 75, "height": 60}, "6_00010": {"width": 75, "height": 60}}, "a1": {"a1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [41.5, -24.5, -41.5, -24.5, -41.5, 24.5, 41.5, 24.5], "hull": 4}}, "a_canying": {"a_canying": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [112, -58.5, -112, -58.5, -112, 58.5, 112, 58.5], "hull": 4}}, "ag1": {"ag2": {"type": "mesh", "path": "ag", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-201.19, 19.41, -24.6, -90.29, 57.37, 89.83, -119.22, 199.53], "hull": 4}}, "asansheguang222": {"asansheguang222": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [41.5, -24.5, -41.5, -24.5, -41.5, 24.5, 41.5, 24.5], "hull": 4}}, "atu2": {"atu2": {"width": 232, "height": 232}}, "b": {"b1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [41.5, -24.5, -41.5, -24.5, -41.5, 24.5, 41.5, 24.5], "hull": 4}, "b2": {"type": "mesh", "path": "b1", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-41.5, -24.5, 41.5, -24.5, 41.5, 24.5, -41.5, 24.5], "hull": 4}}, "bing": {"sanguanga2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [11, -12, -11, -12, -11, 12, 11, 12], "hull": 4}}, "bing2": {"sanguanga2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [11, -12, -11, -12, -11, 12, 11, 12], "hull": 4}}, "bing3": {"sanguanga2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [11, -12, -11, -12, -11, 12, 11, 12], "hull": 4}}, "d1": {"d1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [164, -92, -164, -92, -164, 92, 164, 92], "hull": 4}, "d3": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [212.5, -107.5, -212.5, -107.5, -212.5, 107.5, 212.5, 107.5], "hull": 4}, "d5": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [197.5, -108.5, -197.5, -108.5, -197.5, 108.5, 197.5, 108.5], "hull": 4}, "d7": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [176.2, -37.79, -117.8, -37.79, -117.8, 106.21, 176.2, 106.21], "hull": 4}}, "daoguang": {"daoguang": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-114.52, 117.46, 117.46, 114.52, 114.52, -117.46, -117.46, -114.52], "hull": 4}}, "daoguang3": {"1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [379.94, -24.08, -19.06, -24.08, -19.06, 259.92, 379.94, 259.92], "hull": 4}, "3": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [169.91, -12.51, -19.09, -12.51, -19.09, 244.49, 169.91, 244.49], "hull": 4}, "5": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [145.39, -10.61, -23.61, -10.61, -23.61, 218.39, 145.39, 218.39], "hull": 4}}, "daoguang5": {"dao3_2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [73.36, -40.15, -23.7, -0.11, 16.72, 97.88, 113.79, 57.84], "hull": 4}, "dao3_4": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [61, -33.38, -28.67, 3.61, 8.7, 94.2, 98.37, 57.21], "hull": 4}}, "daoying1": {"daoying1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-71.55, 137.82, 151.4, 73.68, 87.26, -149.28, -135.69, -85.14], "hull": 4}}, "diguang_0": {"diguang_2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [503, -80, -502, -80, -502, 80, 503, 80], "hull": 4}, "diguang_4": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [513, -28, -513, -28, -513, 29, 513, 29], "hull": 4}}, "dilie1s": {"dilie1s": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [105, 107, -105, 107, -105, -107, 105, -107], "hull": 4}}, "fasan": {"fasan": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-42, -94.63, -50.32, 78.17, 180.42, 89.28, 188.73, -83.52], "hull": 4}, "fasan2": {"type": "mesh", "path": "fasan", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-50.94, 91.16, -42.62, -81.64, 188.11, -70.54, 179.79, 102.26], "hull": 4}}, "lanse": {"lanse": {"type": "mesh", "color": "ffcdb3ff", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [16, -16, -16, -16, -16, 16, 16, 16], "hull": 4}}, "shaseng_0": {"shaseng_0": {"type": "mesh", "uvs": [0.8703, 0.07112, 0.92696, 0.27596, 1, 0.5199, 0.92924, 0.67429, 0.60197, 0.87529, 0.45554, 0.98109, 0.27785, 1, 0, 1, 0, 0.83984, 0.12385, 0.79612, 0.25585, 0.79275, 0.33369, 0.71456, 0.24062, 0.58088, 0.30831, 0.47747, 0.22539, 0.41021, 0.20514, 0.28581, 0.29645, 0.18405, 0.32744, 0.06096, 0.5875, 0, 0.70978, 0, 0.60825, 0.08702, 0.64161, 0.28707, 0.67924, 0.53506, 0.53616, 0.70439, 0.41693, 0.85604, 0.25716, 0.91646], "triangles": [25, 10, 24, 9, 10, 25, 8, 9, 25, 25, 24, 5, 7, 8, 25, 6, 25, 5, 7, 25, 6, 3, 22, 2, 22, 12, 13, 23, 22, 3, 23, 12, 22, 11, 12, 23, 24, 11, 23, 10, 11, 24, 4, 23, 3, 24, 23, 4, 5, 24, 4, 20, 18, 19, 20, 19, 0, 17, 18, 20, 16, 17, 20, 21, 20, 0, 21, 0, 1, 16, 20, 21, 15, 16, 21, 14, 15, 21, 13, 14, 21, 22, 21, 1, 22, 1, 2, 13, 21, 22], "vertices": [1, 47, -2.78, 40.94, 1, 1, 47, 62.01, 45.07, 1, 2, 47, 139.23, 50.85, 0.69506, 48, -23.06, 45.34, 0.30494, 2, 47, 186.78, 36.31, 0.04641, 48, 26.05, 53.09, 0.95359, 2, 48, 103.61, 29.13, 0.90946, 49, -21.2, 20.95, 0.09054, 2, 48, 142.94, 20.26, 0.04163, 49, 16.58, 35.02, 0.95837, 1, 49, 42.69, 23.96, 1, 1, 49, 78.17, -0.94, 1, 2, 48, 128.16, -62.2, 0.01708, 49, 49.28, -42.11, 0.98292, 2, 48, 108.23, -49.36, 0.11217, 49, 25.58, -42.24, 0.88783, 2, 48, 99.61, -30.63, 0.48189, 49, 8.12, -31.28, 0.51811, 3, 47, 192.63, -57.27, 0.00176, 48, 72.3, -28.47, 0.97482, 49, -15.93, -44.4, 0.02342, 2, 47, 149.7, -68.69, 0.17072, 48, 38.72, -57.54, 0.82928, 2, 47, 118.09, -55.79, 0.64575, 48, 4.65, -59.8, 0.35425, 2, 47, 96.08, -67.16, 0.90351, 48, -10.16, -79.65, 0.09649, 2, 47, 56.89, -67.46, 0.99429, 48, -45.25, -97.09, 0.00571, 1, 47, 26.06, -50.93, 1, 1, 47, -12.13, -43.29, 1, 1, 47, -28.27, -1.43, 1, 1, 47, -26.88, 17.59, 1, 1, 47, -0.78, -0.19, 1, 1, 47, 62.25, 0.42, 1, 2, 47, 140.34, 0.6, 0.48157, 48, -0.05, 0.65, 0.51843, 1, 48, 57.6, -0.33, 1, 2, 48, 108.72, 0.08, 0.66222, 49, -1.04, -0.58, 0.33778, 1, 49, 30.26, 0.63, 1], "hull": 20}}, "shaseng_1": {"shaseng_1": {"type": "mesh", "uvs": [1, 0.10181, 0.84042, 0.39724, 0.68984, 0.67172, 0.4278, 0.86448, 0.06993, 1, 0, 1, 0, 0.82886, 0.13056, 0.502, 0.35153, 0.26524, 0.61553, 0.13952, 0.92842, 0, 1, 0, 0.08639, 0.88822, 0.30486, 0.69765, 0.53492, 0.48843, 0.74566, 0.28129], "triangles": [10, 11, 0, 15, 9, 10, 15, 10, 0, 1, 15, 0, 14, 8, 9, 14, 9, 15, 14, 15, 1, 2, 14, 1, 7, 8, 14, 13, 7, 14, 6, 7, 13, 3, 13, 14, 3, 14, 2, 12, 6, 13, 12, 13, 3, 5, 6, 12, 4, 5, 12, 4, 12, 3], "vertices": [1, 26, 26.66, -2.05, 1, 2, 25, 39.27, -5.96, 0.00016, 26, 13, -6.41, 0.99984, 2, 25, 26.65, -10.38, 0.60399, 26, 0.23, -10.4, 0.39601, 1, 25, 12.42, -8.95, 1, 1, 25, -3.55, -2.89, 1, 1, 25, -5.95, -0.86, 1, 1, 25, -1.31, 4.63, 1, 1, 25, 12.04, 11.31, 1, 2, 25, 26.06, 12.48, 0.69426, 26, 0.43, 12.47, 0.30574, 2, 25, 38.53, 8.84, 0.03744, 26, 12.78, 8.4, 0.96256, 1, 26, 27.14, 3.28, 1, 1, 26, 29.53, 1.12, 1, 1, 25, 0.05, 0.22, 1, 1, 25, 12.72, -0.02, 1, 2, 25, 26.3, 0, 0.28151, 26, 0.24, -0.01, 0.71849, 2, 25, 39.16, 0.51, 3e-05, 26, 13.11, 0.06, 0.99997], "hull": 12}}, "shaseng_2": {"shaseng_2": {"type": "mesh", "uvs": [0.42947, 0.14205, 0.68947, 0.23205, 0.90697, 0.44805, 1, 0.86205, 1, 1, 0.85697, 1, 0.47697, 0.8418, 0.34947, 0.74505, 0.25697, 0.5583, 0.08197, 0.2838, 0, 0, 0.8847, 0.91449, 0.69687, 0.68334, 0.48795, 0.47289, 0.24454, 0.24864], "triangles": [14, 10, 0, 9, 10, 14, 13, 0, 1, 13, 1, 2, 14, 0, 13, 8, 14, 13, 9, 14, 8, 12, 13, 2, 7, 8, 13, 7, 13, 12, 6, 7, 12, 12, 2, 3, 11, 12, 3, 6, 12, 11, 5, 6, 11, 11, 3, 4, 5, 11, 4], "vertices": [33.97, -6.48, 25.29, -11.51, 13.65, -12.17, -1.34, -4.38, -5.63, -0.92, -2.4, 3.09, 11.12, 9.76, 17.01, 10.9, 24.92, 8.81, 37.42, 6.81, 48.11, 1.98, -0.36, 0.17, 11.08, -0.38, 22.35, 0.19, 34.84, 1.37], "hull": 11}}, "shaseng_3": {"shaseng_3": {"type": "mesh", "uvs": [0.65631, 0.11217, 0.6796, 0.26438, 0.60864, 0.36511, 0.61854, 0.41596, 0.64412, 0.36703, 0.67961, 0.31185, 0.71975, 0.29022, 0.87346, 0.26405, 0.91751, 0.29364, 1, 0.4473, 1, 0.60367, 0.93743, 0.66993, 0.87588, 0.79317, 0.76189, 0.8422, 0.6821, 0.94292, 0.58977, 1, 0.5, 1, 0.43703, 0.88063, 0.33673, 0.9522, 0.28543, 0.96677, 0.212, 0.89336, 0.16501, 0.97019, 0.13711, 0.96848, 0.05634, 0.81482, 0, 0.68848, 0.00471, 0.57578, 0.00952, 0.48486, 0.05752, 0.40171, 0.11154, 0.32366, 0.19023, 0.3027, 0.20408, 0.31119, 0.17365, 0.37373, 0.16089, 0.42993, 0.17298, 0.46817, 0.19983, 0.34485, 0.23299, 0.28102, 0.29941, 0.18416, 0.37259, 0.08992, 0.4728, 0.0729, 0.57638, 0, 0.59439, 0, 0.31953, 0.8665, 0.3258, 0.84122, 0.33988, 0.66864, 0.36547, 0.48118, 0.41154, 0.3443, 0.46273, 0.22081, 0.54719, 0.14196, 0.17095, 0.85908, 0.13, 0.74303, 0.09289, 0.62401, 0.09929, 0.53474, 0.10569, 0.4291, 0.14847, 0.37346, 0.54355, 0.8394, 0.59241, 0.73916, 0.65181, 0.61998, 0.74857, 0.56429, 0.84587, 0.50621, 0.88122, 0.40344], "triangles": [10, 58, 9, 58, 59, 9, 6, 4, 5, 58, 6, 59, 59, 8, 9, 6, 7, 59, 59, 7, 8, 56, 57, 13, 13, 57, 12, 12, 57, 11, 11, 57, 58, 56, 44, 3, 11, 58, 10, 56, 3, 57, 3, 4, 57, 57, 4, 58, 6, 58, 4, 14, 15, 54, 15, 16, 54, 16, 17, 54, 54, 55, 14, 14, 55, 13, 54, 17, 43, 55, 56, 13, 54, 43, 55, 55, 44, 56, 33, 52, 32, 52, 53, 32, 32, 53, 31, 53, 52, 28, 52, 27, 28, 31, 53, 29, 53, 28, 29, 30, 31, 29, 33, 50, 51, 52, 33, 51, 24, 25, 50, 50, 25, 51, 25, 26, 51, 51, 26, 52, 52, 26, 27, 20, 21, 48, 21, 22, 48, 22, 23, 48, 42, 20, 48, 23, 49, 48, 48, 49, 42, 23, 24, 49, 24, 50, 49, 49, 50, 43, 46, 47, 2, 2, 47, 1, 47, 0, 1, 46, 38, 47, 46, 37, 38, 0, 47, 40, 47, 39, 40, 47, 38, 39, 3, 44, 45, 3, 45, 2, 33, 34, 44, 34, 35, 44, 44, 35, 45, 45, 46, 2, 35, 36, 45, 45, 36, 46, 36, 37, 46, 19, 41, 18, 19, 20, 41, 18, 41, 17, 41, 20, 42, 41, 42, 17, 17, 42, 43, 42, 49, 43, 50, 33, 43, 43, 44, 55, 43, 33, 44], "vertices": [3, 17, 58.53, -21.24, 0.00349, 18, 30.97, -7.52, 0.99289, 20, 30.94, 55.44, 0.00363, 5, 17, 42.56, -31.57, 0.12113, 18, 21.77, -24.16, 0.82711, 19, 70.78, 18.28, 0.00302, 20, 25.04, 37.36, 0.04708, 21, 18.97, 32.96, 0.00166, 6, 16, 67.14, -33.05, 0.00926, 17, 27.21, -27.03, 0.35933, 18, 6.08, -27.39, 0.3608, 19, 55, 20.97, 0.04093, 20, 10.24, 31.23, 0.20184, 21, 3.76, 37.96, 0.02784, 6, 16, 61.15, -35.33, 0.01902, 17, 21.99, -30.76, 0.25119, 18, 3.24, -33.14, 0.10805, 19, 50.26, 16.65, 0.06426, 20, 8.54, 25.05, 0.44851, 21, -1.56, 34.39, 0.10897, 6, 16, 67.63, -38.1, 0.00311, 17, 28.96, -31.79, 0.1072, 18, 9.88, -30.77, 0.03309, 19, 57.3, 16.45, 0.01469, 20, 14.6, 28.63, 0.53147, 21, 5.37, 33.15, 0.31045, 6, 16, 75.06, -42.17, 3e-05, 17, 37.18, -33.84, 0.05686, 18, 18.09, -28.7, 0.01343, 19, 65.71, 15.39, 0.0011, 20, 22.28, 32.22, 0.44483, 21, 13.53, 30.86, 0.48375, 4, 17, 41.86, -38.1, 0.03732, 18, 24.23, -30.25, 0.00729, 20, 28.6, 31.85, 0.36019, 21, 18.08, 26.46, 0.5952, 4, 17, 53.36, -57.1, 0.00136, 18, 43.33, -41.59, 0, 20, 49.49, 24.31, 0.01981, 21, 29, 7.12, 0.97882, 3, 17, 52.45, -64.32, 3e-05, 20, 53.33, 18.13, 0.00039, 21, 27.88, -0.07, 0.99958, 2, 20, 54.8, -4.1, 0.04248, 21, 14.5, -17.88, 0.95752, 3, 19, 57.15, -42.18, 0.00164, 20, 45.72, -21.06, 0.47346, 21, -3.44, -24.81, 0.52489, 3, 19, 45.64, -38.43, 0.03582, 20, 33.98, -24.02, 0.76991, 21, -14.27, -19.41, 0.19428, 3, 19, 28.12, -38.27, 0.20353, 20, 19.06, -33.22, 0.79337, 21, -31.58, -16.66, 0.0031, 2, 19, 14.82, -27.08, 0.59473, 20, 1.85, -30.84, 0.40527, 2, 19, -1.59, -23.29, 0.93328, 20, -14.06, -36.37, 0.06672, 2, 19, -14.22, -15.28, 0.9967, 20, -29.01, -36.33, 0.0033, 2, 16, -12.36, -28.71, 0.02258, 19, -20.57, -4.12, 0.97742, 2, 16, 0.9, -17.72, 0.48421, 19, -12.26, 10.96, 0.51579, 3, 16, -9.84, -4.77, 0.97868, 22, -19.04, -19.51, 0.00424, 19, -27, 19.08, 0.01708, 2, 16, -12.65, 2.24, 0.9494, 22, -18.3, -12, 0.0506, 2, 16, -5.2, 13.91, 0.31701, 22, -6.31, -5.08, 0.68299, 2, 16, -15.51, 19.23, 0.00175, 22, -13, 4.39, 0.99825, 1, 22, -11.48, 8.09, 1, 1, 22, 10.18, 12.73, 1, 2, 22, 27.51, 15.18, 0.81666, 23, -8.76, 12.77, 0.18334, 2, 22, 40.37, 9.96, 0.11385, 23, 5.11, 13, 0.88615, 2, 23, 16.32, 13.04, 0.99439, 24, -13.45, 6.19, 0.00561, 2, 23, 26.97, 6.86, 0.42457, 24, -1.22, 7.67, 0.57543, 1, 24, 11.09, 8.09, 1, 1, 24, 20.39, 1.25, 1, 1, 24, 20.9, -0.93, 1, 4, 16, 57.3, 28.38, 0.00029, 17, 2.09, 29.89, 0.0003, 23, 31.49, -9.49, 0.00153, 24, 12.23, -2.66, 0.99788, 4, 16, 50.2, 29.21, 0.0322, 17, -4.99, 28.89, 0.03025, 23, 24.47, -8.12, 0.24107, 24, 5.8, -5.79, 0.69648, 5, 16, 45.78, 26.83, 0.15694, 17, -8.65, 25.47, 0.15636, 22, 44.89, -17.13, 0.00542, 23, 19.89, -10.15, 0.50124, 24, 3.37, -10.17, 0.18004, 4, 16, 61.34, 25.18, 0.08621, 17, 6.82, 27.82, 0.74004, 23, 35.27, -12.99, 0.15473, 24, 17.36, -3.18, 0.01902, 5, 16, 69.78, 21.59, 0.02248, 17, 15.89, 26.5, 0.89146, 18, -29.13, 14.47, 0.00093, 23, 43.42, -17.21, 0.07847, 24, 26.41, -1.64, 0.00666, 4, 17, 30.56, 22.36, 0.86438, 18, -14.25, 17.74, 0.12106, 23, 55.92, -25.92, 0.01423, 24, 41.63, -1.06, 0.00033, 3, 17, 45.3, 17.22, 0.38144, 18, 1.18, 20.16, 0.61822, 23, 68.17, -35.6, 0.00034, 2, 17, 52.79, 4.82, 0.01836, 18, 13.63, 12.76, 0.98164, 1, 18, 30.78, 10.39, 1, 1, 18, 32.78, 8.77, 1, 2, 16, 0.24, -0.84, 0.99525, 19, -19.05, 26.43, 0.00475, 2, 16, 3.45, -1.29, 0.99082, 19, -15.9, 27.19, 0.00918, 2, 16, 24.75, -0.28, 0.99873, 19, 3.54, 35.93, 0.00127, 4, 16, 48.09, -0.64, 0.38453, 17, 0.56, -0.52, 0.6134, 19, 25.4, 44.15, 0.00161, 20, -27.15, 35.07, 0.00046, 6, 16, 65.69, -4.78, 1e-05, 17, 18.63, -0.06, 0.99975, 18, -14.2, -7.65, 0.0001, 19, 43.29, 46.74, 5e-05, 20, -13.39, 46.8, 9e-05, 21, -4.01, 65.18, 0, 5, 17, 35.47, -0.91, 0.28233, 18, 1.05, -0.47, 0.71729, 19, 60.11, 47.89, 5e-05, 20, 0.23, 56.73, 0.00032, 21, 12.79, 63.82, 1e-05, 5, 17, 49.09, -8.28, 0.0008, 18, 16.54, -0.54, 0.99885, 19, 74.51, 42.18, 0, 20, 15.46, 59.58, 0.00035, 21, 26.19, 56.05, 0, 2, 16, -1.86, 20.32, 0.02327, 22, -0.39, -0.94, 0.97673, 3, 16, 11.45, 28.14, 0.00134, 22, 15.02, -0.14, 0.99862, 23, -14.25, -6.22, 4e-05, 2, 22, 30.59, 0.02, 0.49532, 23, 0.02, 0.03, 0.50468, 3, 16, 36.19, 36.11, 0.00044, 22, 40.65, -4.47, 0.00027, 23, 11.03, -0.17, 0.99929, 2, 23, 24.06, -0.23, 0.32626, 24, 0.72, 0.25, 0.67374, 1, 24, 9.9, 0.09, 1, 2, 16, 8.08, -32.08, 0.00505, 19, -0.32, 0.23, 0.99495, 3, 16, 21.27, -37.26, 0.00144, 17, -16.1, -42.74, 7e-05, 19, 13.85, 0.25, 0.99849, 6, 16, 36.98, -43.59, 0.0013, 17, 0.71, -44.88, 0.00225, 18, -8.86, -55.63, 0.00023, 19, 30.8, 0.11, 0.26657, 20, 0.88, 0.69, 0.72939, 21, -23.26, 20.91, 0.00027, 4, 17, 12.39, -54.98, 0.00015, 18, 6.2, -59.03, 3e-05, 20, 16.31, 0.19, 0.99923, 21, -11.88, 10.47, 0.00059, 3, 19, 56.68, -17.09, 1e-05, 20, 31.95, -0.08, 0.54405, 21, -0.2, 0.07, 0.45594, 1, 21, 13.41, -0.1, 1], "hull": 41}}, "shaseng_4": {"shaseng_4": {"type": "mesh", "uvs": [0.5233, 0.1052, 0.75847, 0.15062, 0.89806, 0.50261, 1, 0.86477, 1, 1, 0.89561, 1, 0.73631, 0.76181, 0.56251, 0.48543, 0.37423, 0.46917, 0.22651, 0.35265, 0, 0.37704, 0, 0.17474, 0.14399, 0.07113, 0.30937, 0, 0.3913, 0, 0.09373, 0.23511, 0.24348, 0.17685, 0.35766, 0.13524, 0.52521, 0.22263, 0.6809, 0.3433, 0.77728, 0.58188], "triangles": [5, 3, 4, 5, 6, 3, 6, 20, 3, 20, 2, 3, 6, 7, 20, 2, 20, 19, 20, 7, 19, 19, 1, 2, 19, 7, 18, 18, 8, 9, 7, 8, 18, 19, 18, 1, 17, 0, 18, 18, 0, 1, 17, 14, 0, 9, 16, 17, 18, 9, 17, 10, 15, 9, 10, 11, 15, 9, 15, 16, 15, 12, 16, 15, 11, 12, 17, 16, 13, 16, 12, 13, 17, 13, 14], "vertices": [2, 32, 13.87, -1.11, 0.00131, 33, 3.34, 3.28, 0.99869, 2, 33, 9.74, 6.03, 0.92118, 34, -4.69, 4.08, 0.07882, 1, 34, 6.95, 4.2, 1, 1, 34, 18.52, 3.2, 1, 1, 34, 22.46, 1.78, 1, 1, 34, 21.44, -1.07, 1, 1, 34, 12.93, -2.92, 1, 3, 32, 10.27, -12.39, 0.02068, 33, 11.03, -5.73, 0.29052, 34, 3.17, -4.78, 0.68879, 3, 32, 5.45, -9.78, 0.22437, 33, 6.27, -8.45, 0.61712, 34, 0.85, -9.75, 0.15851, 3, 32, 2.93, -4.77, 0.77122, 33, 0.69, -7.94, 0.2138, 34, -4, -12.56, 0.01498, 1, 32, -3.4, -2.88, 1, 1, 32, -0.93, 2.88, 1, 1, 32, 4.17, 4.19, 1, 2, 32, 9.45, 4.33, 0.85321, 33, -3.61, 2.39, 0.14679, 2, 32, 11.63, 3.39, 0.56054, 33, -1.66, 3.75, 0.43946, 1, 32, 0.83, 0.09, 1, 1, 32, 5.53, 0.04, 1, 3, 32, 9.08, -0.08, 0.75053, 33, -0.06, -0.24, 0.24947, 34, -9.06, -6.7, 1e-05, 1, 33, 5.47, 0.33, 1, 3, 32, 15.16, -9.7, 2e-05, 33, 11.32, -0.15, 0.30425, 34, 0.18, -0.06, 0.69573, 1, 34, 8.08, 0.08, 1], "hull": 15}}, "shaseng_5": {"shaseng_5": {"type": "mesh", "uvs": [0.83565, 0.13615, 1, 0.26948, 1, 0.58948, 0.77161, 0.50115, 0.5968, 0.38448, 0.50507, 0.44281, 0.40469, 0.53948, 0.29565, 0.70448, 0.13815, 1, 0, 1, 0, 0.84114, 0.062, 0.59614, 0.13988, 0.32115, 0.3095, 0.13615, 0.48777, 0, 0.59507, 0, 0.94407, 0.39664, 0.72427, 0.28497, 0.47677, 0.18664, 0.35043, 0.28831, 0.23273, 0.41331, 0.13408, 0.64497], "triangles": [6, 20, 19, 11, 12, 20, 21, 11, 20, 21, 20, 6, 7, 21, 6, 10, 11, 21, 10, 8, 9, 7, 10, 21, 7, 8, 10, 18, 13, 14, 19, 13, 18, 12, 13, 19, 5, 19, 18, 20, 12, 19, 5, 6, 19, 17, 15, 0, 17, 0, 1, 18, 14, 15, 17, 18, 15, 4, 18, 17, 16, 17, 1, 4, 5, 18, 3, 17, 16, 4, 17, 3, 16, 1, 2, 3, 16, 2], "vertices": [1, 29, 5.36, -5.36, 1, 1, 29, -0.06, -4, 1, 1, 29, -3.84, 3.77, 1, 1, 29, 2.54, 4.23, 1, 3, 29, 8.01, 3.39, 0.83207, 30, 1.16, 6.15, 0.15699, 31, -3.51, 8.69, 0.01094, 3, 29, 9.46, 5.85, 0.34297, 30, 3.99, 5.73, 0.5304, 31, -1.18, 7.03, 0.12663, 3, 29, 10.67, 9.34, 0.04985, 30, 7.68, 5.92, 0.35582, 31, 2.19, 5.53, 0.59433, 2, 30, 12.76, 7.34, 0.01131, 31, 7.37, 4.48, 0.98869, 1, 31, 16.28, 3.53, 1, 1, 31, 17.57, 0.17, 1, 1, 31, 13.57, -1.37, 1, 1, 31, 6.82, -2.24, 1, 2, 30, 8.85, -3.07, 0.5353, 31, -0.84, -3.01, 0.4647, 2, 29, 17.66, 0.63, 0.00451, 30, 2.23, -3.83, 0.99549, 2, 29, 15.1, -4.7, 0.77616, 30, -3.67, -3.46, 0.22384, 2, 29, 12.6, -5.93, 0.95941, 30, -5.75, -1.59, 0.04059, 1, 29, -0.25, -0.27, 1, 1, 29, 6.21, -0.48, 1, 2, 29, 13.15, -0.05, 0.66489, 30, -0.09, 0.09, 0.33511, 1, 30, 4.19, -0.06, 1, 3, 29, 16.18, 8.23, 0.00018, 30, 8.72, 0.4, 0.01333, 31, 0.61, 0.14, 0.98649, 1, 31, 7.37, -0.01, 1], "hull": 16}}, "shaseng_6": {"shaseng_6": {"type": "mesh", "uvs": [0.9893, 0.23895, 0.98014, 0.307, 0.95091, 0.32283, 0.9743, 0.3489, 0.97277, 0.43182, 0.90467, 0.50803, 0.76083, 0.56924, 0.74847, 0.59072, 0.68779, 0.66374, 0.63035, 0.71865, 0.55739, 0.77957, 0.47367, 0.83536, 0.42913, 0.82802, 0.37613, 0.86986, 0.34311, 0.90435, 0.3385, 0.95353, 0.35463, 1, 0.32468, 1, 0.27859, 0.97408, 0.2448, 0.92711, 0.21331, 0.86839, 0.18762, 0.81767, 0.14061, 0.75552, 0.09125, 0.68514, 0.05658, 0.59635, 0.05019, 0.51312, 0.07416, 0.48639, 0.0406, 0.48792, 0, 0.41768, 0, 0.34836, 0.03856, 0.31664, 0.03381, 0.29171, 0.01722, 0.26565, 0, 0.2209, 0.00951, 0.16765, 0.01819, 0.1151, 0.03376, 0.07672, 0.06245, 0.03912, 0.10054, 0, 0.12006, 0, 0.11414, 0.04228, 0.10378, 0.0861, 0.11562, 0.11014, 0.14373, 0.13134, 0.15556, 0.16103, 0.17184, 0.18507, 0.19699, 0.19779, 0.19847, 0.13417, 0.27096, 0.06631, 0.47067, 0.05359, 0.56683, 0.07621, 0.60825, 0.14548, 0.61269, 0.23031, 0.63784, 0.19072, 0.68074, 0.17376, 0.734, 0.15962, 0.79318, 0.14831, 0.83312, 0.13559, 0.85679, 0.09883, 0.84791, 0.06631, 0.81833, 0, 0.86273, 0, 0.92335, 0.06171, 0.96448, 0.11895, 0.99097, 0.17116, 0.31274, 0.55163, 0.27576, 0.66801, 0.29109, 0.60939, 0.26974, 0.73993, 0.27178, 0.8121, 0.27586, 0.85697, 0.29101, 0.90492, 0.30637, 0.94602, 0.199, 0.32027, 0.58389, 0.31933, 0.60152, 0.35864, 0.70338, 0.24726, 0.64266, 0.29312, 0.78858, 0.24352, 0.85322, 0.2276, 0.89925, 0.18455, 0.9208, 0.1312, 0.8875, 0.06849, 0.15394, 0.28751, 0.104, 0.25381, 0.07559, 0.20889, 0.05699, 0.16583, 0.05364, 0.12381, 0.05442, 0.09143, 0.07193, 0.06575, 0.24867, 0.54639, 0.427, 0.55099, 0.20337, 0.42295, 0.5658, 0.43216, 0.507, 0.49756, 0.21686, 0.49572, 0.20144, 0.37136, 0.54266, 0.46993, 0.47615, 0.54547], "triangles": [81, 57, 58, 58, 82, 81, 81, 62, 63, 81, 82, 62, 58, 59, 82, 62, 82, 61, 82, 59, 61, 59, 60, 61, 1, 2, 0, 0, 2, 79, 0, 79, 80, 0, 80, 64, 64, 80, 81, 64, 81, 63, 79, 57, 80, 80, 57, 81, 6, 78, 5, 78, 77, 76, 5, 78, 2, 5, 2, 4, 2, 78, 79, 4, 2, 3, 76, 55, 78, 76, 54, 55, 78, 56, 79, 78, 55, 56, 56, 57, 79, 7, 97, 6, 97, 93, 6, 93, 75, 6, 75, 77, 6, 6, 77, 78, 94, 74, 93, 74, 73, 49, 92, 74, 94, 93, 74, 75, 52, 74, 49, 75, 74, 77, 74, 52, 77, 49, 51, 52, 77, 52, 76, 52, 53, 76, 53, 54, 76, 88, 89, 41, 88, 36, 89, 41, 89, 40, 36, 37, 89, 89, 37, 40, 37, 38, 40, 40, 38, 39, 43, 86, 42, 34, 87, 86, 34, 35, 87, 86, 87, 42, 41, 87, 88, 87, 41, 42, 87, 35, 88, 35, 36, 88, 31, 32, 84, 32, 33, 85, 32, 85, 84, 86, 33, 34, 33, 86, 85, 45, 85, 44, 45, 84, 85, 44, 86, 43, 86, 44, 85, 26, 92, 95, 27, 28, 26, 28, 30, 26, 26, 96, 92, 73, 96, 83, 83, 96, 30, 83, 30, 84, 26, 30, 96, 74, 92, 96, 28, 29, 30, 74, 96, 73, 83, 46, 73, 49, 73, 46, 30, 31, 84, 83, 45, 46, 83, 84, 45, 17, 15, 16, 18, 72, 17, 17, 72, 15, 18, 19, 72, 15, 72, 14, 19, 71, 72, 72, 71, 14, 19, 70, 71, 19, 20, 70, 71, 70, 14, 14, 70, 13, 12, 13, 69, 20, 69, 70, 20, 21, 69, 69, 13, 70, 66, 67, 68, 69, 21, 68, 21, 22, 68, 67, 69, 68, 68, 22, 66, 66, 22, 23, 12, 69, 67, 67, 65, 91, 23, 24, 90, 23, 90, 66, 95, 24, 26, 24, 95, 90, 66, 90, 67, 67, 90, 65, 24, 25, 26, 91, 65, 95, 95, 65, 90, 12, 67, 91, 11, 12, 10, 10, 91, 98, 10, 12, 91, 10, 98, 9, 9, 98, 8, 98, 94, 8, 94, 97, 8, 8, 97, 7, 94, 98, 91, 92, 94, 91, 93, 97, 94, 92, 91, 95, 49, 46, 48, 48, 46, 47, 51, 49, 50], "vertices": [3, 39, 77.51, -26.49, 0.00405, 40, 55.19, -5.97, 0.02558, 41, 11.23, -21.99, 0.97037, 4, 15, 51.25, -116.12, 0.00015, 39, 68.9, -37.44, 0.03379, 40, 51.47, -19.39, 0.22271, 41, -1.33, -28, 0.74335, 4, 15, 48.1, -110.42, 0.00165, 39, 62.39, -37.29, 0.06964, 40, 45.41, -21.77, 0.37486, 41, -7.11, -24.99, 0.55385, 4, 15, 42.76, -114.9, 0.00443, 39, 63.57, -44.16, 0.11472, 40, 49.15, -27.65, 0.48429, 41, -9.09, -31.67, 0.39656, 5, 15, 25.93, -114.44, 0.01606, 36, -52.84, 133.16, 9e-05, 39, 54.69, -58.47, 0.19249, 40, 46.48, -44.27, 0.55457, 41, -23.38, -40.57, 0.2368, 6, 15, 10.59, -101.08, 0.05503, 36, -36.88, 120.54, 0.00206, 37, -44.73, 129.94, 0.00038, 39, 35.41, -64.98, 0.30469, 40, 31.21, -57.72, 0.5244, 41, -43.55, -37.88, 0.11346, 6, 15, -1.56, -73.06, 0.27355, 36, -23.41, 93.13, 0.02531, 37, -36.06, 100.64, 0.00794, 39, 5.08, -61.34, 0.43485, 40, 1.83, -66.07, 0.24744, 41, -69.14, -21.2, 0.01092, 6, 15, -5.9, -70.62, 0.33558, 36, -18.96, 90.9, 0.0354, 37, -32.05, 97.7, 0.01151, 39, 0.79, -63.85, 0.41726, 40, -1.16, -70.05, 0.19508, 41, -74.1, -21.56, 0.00517, 6, 15, -20.61, -58.7, 0.49786, 36, -3.7, 79.7, 0.08319, 37, -18.9, 84.09, 0.02986, 39, -16.92, -70.55, 0.30231, 40, -14.91, -83.06, 0.08673, 41, -92.94, -19.73, 5e-05, 5, 15, -31.65, -47.45, 0.55495, 36, 7.86, 68.99, 0.14696, 37, -9.31, 71.58, 0.05842, 39, -32.2, -74.41, 0.19735, 40, -27.52, -92.52, 0.04232, 5, 15, -43.88, -33.18, 0.50674, 36, 20.76, 55.31, 0.25568, 37, 1.1, 55.93, 0.12377, 39, -50.69, -77.77, 0.09848, 40, -43.28, -102.76, 0.01532, 5, 15, -55.04, -16.83, 0.3586, 36, 32.69, 39.51, 0.35286, 37, 10.2, 38.34, 0.2421, 39, -70.44, -79.17, 0.04203, 40, -60.96, -111.67, 0.00441, 6, 15, -53.47, -8.2, 0.26333, 36, 31.53, 30.82, 0.36783, 37, 7.59, 29.97, 0.33843, 38, -0.71, 32.31, 0.00252, 39, -77.1, -73.46, 0.02568, 40, -69.31, -108.98, 0.00221, 6, 15, -61.86, 2.16, 0.0775, 36, 40.4, 20.87, 0.18869, 37, 14.67, 18.67, 0.62779, 38, 1.91, 19.23, 0.10068, 39, -90.28, -75.48, 0.00517, 40, -80.68, -115.93, 0.00016, 5, 15, -68.8, 8.64, 0.01389, 36, 47.65, 14.73, 0.02592, 37, 20.77, 11.4, 0.4611, 38, 5.05, 10.27, 0.49837, 39, -99.37, -78.21, 0.00071, 4, 15, -78.78, 9.63, 0.00023, 37, 30.55, 9.2, 0.00736, 38, 13.43, 4.77, 0.9924, 39, -105.25, -86.32, 0, 5, 15, -88.24, 6.59, 0, 36, 66.96, 17.7, 1e-05, 37, 40.31, 11.07, 0.00017, 38, 23.22, 3.06, 0.99982, 39, -107.4, -96.03, 0, 4, 36, 67.18, 11.9, 0, 37, 39.55, 5.31, 4e-05, 38, 20.47, -2.06, 0.99996, 39, -112.39, -93.05, 0, 1, 38, 11.61, -7.45, 1, 2, 37, 22.85, -8.12, 0.42767, 38, 0.11, -8.72, 0.57233, 3, 36, 41.31, -10.71, 0.07071, 37, 10.23, -12.61, 0.92584, 38, -13.29, -8.46, 0.00345, 4, 35, 59.11, -7.98, 0.00803, 36, 31.21, -16.08, 0.60683, 37, -0.62, -16.2, 0.3824, 43, -57.23, 83.52, 0.00274, 4, 35, 49.56, -20.27, 0.14204, 36, 18.95, -25.67, 0.80609, 37, -14.33, -23.59, 0.02265, 43, -42.48, 78.53, 0.02921, 3, 35, 38.53, -33.47, 0.40227, 36, 5.03, -35.78, 0.49471, 43, -26.4, 72.44, 0.10303, 3, 35, 23.12, -44.99, 0.54623, 36, -12.72, -43.19, 0.20927, 43, -10.47, 61.65, 0.2445, 3, 35, 7.25, -50.92, 0.5006, 36, -29.56, -45.07, 0.08459, 43, 0.34, 48.61, 0.41482, 3, 35, 0.73, -47.98, 0.4141, 36, -35.16, -40.63, 0.04236, 43, -0.3, 41.49, 0.54354, 3, 35, 2.86, -54.14, 0.33494, 36, -34.6, -47.12, 0.02149, 43, 4.82, 45.52, 0.64357, 4, 35, -8.62, -65.7, 0.21787, 36, -48.55, -55.53, 0.00426, 43, 19.51, 38.46, 0.76492, 44, -21.85, 31.91, 0.01296, 4, 35, -22.12, -69.65, 0.14067, 36, -62.61, -56.07, 0.00016, 43, 27.66, 27, 0.79321, 44, -9.21, 25.72, 0.06597, 3, 35, -30.4, -64.27, 0.07555, 43, 25.3, 17.42, 0.70054, 44, -6.71, 16.17, 0.22391, 3, 35, -35, -66.58, 0.03004, 43, 28.99, 13.83, 0.45446, 44, -1.76, 14.78, 0.5155, 3, 35, -39.18, -71.15, 0.0077, 43, 34.68, 11.38, 0.17536, 44, 4.41, 15.34, 0.81695, 4, 35, -46.96, -76.91, 0.00024, 43, 42.66, 5.92, 0.00794, 44, 14.03, 14.35, 0.95937, 45, -10.74, 10.86, 0.03245, 2, 44, 22.93, 7.94, 0.44088, 45, 0.03, 8.75, 0.55912, 1, 45, 10.65, 6.81, 1, 2, 45, 18.36, 3.6, 0.29865, 46, 0.72, 4.81, 0.70135, 1, 46, 10.15, 4.19, 1, 1, 46, 20.81, 2.2, 1, 1, 46, 22.84, -1, 1, 1, 46, 14.97, -4.62, 1, 5, 15, 97.75, 53.45, 0, 43, 42.12, -28.05, 0.00113, 44, 29.76, -15.76, 0.0342, 45, 16.13, -9.94, 0.23717, 46, 6.38, -7.69, 0.72751, 5, 15, 92.84, 51.2, 0.00166, 43, 37.42, -25.41, 0.01571, 44, 24.37, -15.68, 0.18555, 45, 11.2, -12.11, 0.51804, 46, 3.49, -12.24, 0.27903, 5, 15, 88.49, 45.79, 0.01063, 43, 30.49, -25.06, 0.06808, 44, 18.11, -18.69, 0.41416, 45, 6.76, -17.46, 0.42206, 46, 2.77, -19.15, 0.08507, 5, 15, 82.44, 43.55, 0.03903, 43, 25.12, -21.48, 0.18912, 44, 11.68, -18.1, 0.5063, 45, 0.68, -19.61, 0.24057, 46, -1.09, -24.31, 0.02498, 5, 15, 77.53, 40.44, 0.12051, 43, 19.72, -19.34, 0.39333, 44, 5.91, -18.79, 0.39593, 45, -4.28, -22.65, 0.08692, 46, -3.53, -29.59, 0.00332, 4, 15, 74.9, 35.59, 0.3253, 43, 14.25, -20.06, 0.50276, 44, 1.45, -22.04, 0.1552, 45, -6.98, -27.46, 0.01674, 4, 15, 87.81, 35.18, 0.65034, 43, 21.5, -30.75, 0.31089, 44, 12.92, -27.97, 0.03856, 45, 5.93, -28.06, 0.00021, 3, 15, 101.45, 20.98, 0.85167, 43, 18.03, -50.13, 0.14156, 44, 19.12, -46.66, 0.00678, 2, 15, 103.66, -17.79, 0.79988, 39, 10.41, 57.39, 0.20012, 2, 15, 98.88, -36.4, 0.61508, 39, 24.07, 43.89, 0.38492, 2, 15, 84.74, -44.29, 0.45914, 39, 23.76, 27.69, 0.54086, 3, 15, 67.51, -44.99, 0.11831, 39, 15.68, 12.47, 0.87436, 40, -16.89, 6.1, 0.00733, 3, 15, 75.5, -49.95, 0.01261, 39, 23.99, 16.87, 0.80504, 40, -10.93, 13.37, 0.18235, 3, 15, 78.87, -58.3, 0.0006, 39, 32.9, 15.56, 0.45058, 40, -2.2, 15.6, 0.54882, 4, 39, 43.24, 12.72, 0.07181, 40, 8.43, 16.98, 0.90246, 41, -2.16, 28.34, 0.02309, 42, 13.63, 34.9, 0.00264, 3, 40, 20.12, 17.63, 0.74633, 41, 6.01, 19.95, 0.20642, 42, 10.02, 23.77, 0.04725, 3, 40, 28.16, 19.09, 0.32272, 41, 12.39, 14.85, 0.44593, 42, 8.48, 15.74, 0.23134, 3, 40, 33.76, 25.83, 0.04653, 41, 21.15, 15.05, 0.17274, 42, 12.74, 8.09, 0.78073, 3, 40, 32.99, 32.61, 0.00284, 41, 25.75, 20.08, 0.01218, 42, 19.34, 6.36, 0.98498, 1, 42, 33.89, 4.79, 1, 1, 42, 29.68, -2.73, 1, 1, 42, 13, -6.86, 1, 2, 41, 29.07, -4.71, 0.41326, 42, -1.04, -8.14, 0.58674, 2, 41, 22.96, -14.79, 0.97322, 42, -12.8, -7.44, 0.02678, 2, 15, 2.85, 13.83, 0.00283, 35, 0.46, 0.17, 0.99717, 3, 35, 25.15, -0.09, 0.47106, 36, 0.2, -0.14, 0.52875, 43, -53.55, 48.85, 0.0002, 2, 35, 12.89, -0.57, 0.99893, 43, -49.07, 37.43, 0.00107, 3, 35, 39.49, 2.88, 0.00031, 36, 14.83, -0.76, 0.99945, 43, -61.06, 61.43, 0.00024, 4, 15, -49.94, 22.29, 9e-05, 36, 29.46, 0.19, 0.44488, 37, 0.39, 0.13, 0.55503, 39, -101.65, -55.04, 0, 1, 37, 9.53, -0.28, 1, 5, 15, -68.82, 18.74, 0.00058, 36, 48.14, 4.64, 0.00025, 37, 19.56, 1.36, 0.47763, 38, 0.38, 1.31, 0.52153, 39, -108.11, -73.13, 2e-05, 2, 15, -77.19, 15.84, 1e-05, 38, 9.14, 0, 0.99999, 3, 15, 50.03, 35.44, 0.00504, 35, -38.43, -34.19, 0.00129, 43, -0.49, -0.03, 0.99367, 2, 39, 1.62, -0.19, 0.99992, 40, -24.98, -11, 8e-05, 5, 15, 41.49, -42.57, 0.0399, 36, -64.96, 60.62, 0.00053, 37, -82.49, 75.6, 0.00011, 39, 0.46, -8.79, 0.93412, 40, -22.72, -19.38, 0.02533, 3, 15, 63.9, -62.55, 2e-05, 39, 29.02, 0.49, 0.48847, 40, 0.04, 0.21, 0.51151, 3, 36, -78.55, 68.09, 1e-05, 39, 14.13, -1.46, 0.99252, 40, -12.94, -7.34, 0.00747, 3, 15, 64.5, -79.08, 0, 39, 43.6, -7.33, 0.00505, 40, 16.51, -1.38, 0.99495, 2, 40, 29.38, 0.05, 0.81081, 41, -1.18, 1.42, 0.18919, 1, 41, 11.01, -1.33, 1, 3, 40, 45.12, 17.57, 0.00047, 41, 22.37, 1.05, 0.39375, 42, 0.94, 0.47, 0.60578, 1, 42, 15.2, -0.12, 1, 4, 15, 56.77, 44.12, 0.00158, 43, 10.49, -0.38, 0.99721, 44, -11.24, -6.53, 0.00119, 45, -24.98, -18.67, 2e-05, 4, 15, 63.7, 53.74, 0.00045, 43, 22.35, -0.34, 0.67132, 44, -0.84, -0.84, 0.32803, 45, -17.91, -9.15, 0.00021, 2, 35, -53.41, -63.51, 0, 44, 9.78, 0.1, 1, 5, 15, 81.65, 62.69, 3e-05, 43, 40.14, -9.6, 0.00028, 44, 19.22, -0.5, 0.48466, 45, 0.17, -0.47, 0.51454, 46, -12.15, -8.68, 0.00049, 5, 15, 90.19, 63.25, 0, 43, 45.61, -16.17, 2e-05, 44, 27.16, -3.66, 0.00058, 45, 8.71, -0.03, 0.99935, 46, -5.29, -3.57, 4e-05, 4, 43, 49.3, -21.62, 0, 44, 33, -6.69, 0.0003, 45, 15.28, -0.34, 0.45333, 46, 0.34, -0.18, 0.54637, 3, 44, 36.19, -12.03, 0.00018, 45, 20.41, -3.86, 0.00199, 46, 6.57, -0.26, 0.99783, 4, 15, 4.04, 26.25, 0.00678, 35, 2.93, -12.06, 0.86893, 36, -24.27, -6.33, 0.0088, 43, -34.95, 31.78, 0.1155, 5, 15, 2.77, -8.33, 0.93499, 36, -24.65, 28.27, 0.01298, 37, -48.21, 36.92, 0.00333, 39, -48.63, -24.96, 0.04529, 40, -61.76, -53.25, 0.0034, 4, 15, 29.18, 34.8, 0.0846, 35, -18.66, -27.53, 0.32774, 36, -48.98, -16.07, 0.00175, 43, -13.26, 16.46, 0.5859, 5, 15, 26.63, -35.49, 0.34944, 36, -49.78, 54.27, 0.00425, 37, -68.6, 66.78, 0.00128, 39, -13.14, -18.05, 0.61044, 40, -31.69, -33.17, 0.0346, 5, 15, 13.46, -23.96, 0.69045, 36, -36.08, 43.37, 0.01152, 37, -56.93, 53.73, 0.00364, 39, -29.74, -23.61, 0.27594, 40, -44.86, -44.71, 0.01845, 4, 15, 14.38, 32.32, 0.04469, 35, -5.21, -20.87, 0.63786, 36, -34.32, -12.89, 0.0102, 43, -23.96, 26.98, 0.30725, 4, 15, 39.66, 35.07, 0.06625, 35, -28.61, -30.83, 0.13563, 36, -59.43, -16.84, 6e-05, 43, -6.88, 8.15, 0.79806, 5, 15, 19.01, -30.93, 0.52335, 36, -41.95, 50.07, 0.00866, 37, -61.59, 61.32, 0.00272, 39, -20.92, -22.33, 0.43515, 40, -37.22, -40.13, 0.03011, 5, 15, 3.8, -17.88, 0.81291, 36, -26.13, 37.76, 0.02618, 37, -48.08, 46.52, 0.0075, 39, -39.86, -28.89, 0.14106, 40, -52.16, -53.49, 0.01235], "hull": 65}}, "shaseng_7": {"shaseng_7": {"type": "mesh", "uvs": [0.68759, 0.09172, 0.64948, 0.26003, 0.81826, 0.37294, 0.9462, 0.49651, 1, 0.61368, 1, 0.71807, 0.8972, 0.90342, 0.70937, 1, 0.35321, 1, 0.13558, 0.92581, 0.03929, 0.72534, 0.06047, 0.5294, 0.05716, 0.38792, 0, 0.37416, 0, 0.22343, 0.18565, 0, 0.5297, 0, 0.27515, 0.06157, 0.34004, 0.20334, 0.40493, 0.3578, 0.47793, 0.48476, 0.55363, 0.64769, 0.64826, 0.82754], "triangles": [8, 22, 7, 7, 22, 6, 22, 8, 21, 6, 22, 5, 22, 21, 5, 21, 4, 5, 21, 3, 4, 9, 10, 21, 8, 9, 21, 21, 11, 20, 11, 21, 10, 21, 2, 3, 21, 20, 2, 11, 19, 20, 11, 12, 19, 20, 1, 2, 20, 19, 1, 19, 12, 18, 18, 12, 14, 12, 13, 14, 19, 18, 1, 1, 18, 0, 14, 17, 18, 14, 15, 17, 18, 16, 0, 18, 17, 16, 17, 15, 16], "vertices": [2, 66, 14.85, 19.35, 0.29196, 67, 13.66, -19.12, 0.70804, 4, 66, 12.68, 7.51, 0.4884, 67, 2.69, -14.19, 0.49041, 68, -2.22, 14.75, 0.01401, 69, -23.85, 14.75, 0.00718, 4, 66, 21.52, -0.73, 0.47988, 67, -7.42, -20.82, 0.22436, 68, 8.59, 20.16, 0.17422, 69, -13.04, 20.16, 0.12154, 4, 66, 28.29, -9.41, 0.30982, 67, -17.46, -25.32, 0.09712, 68, 19.08, 23.47, 0.18535, 69, -2.54, 23.47, 0.4077, 4, 66, 31.09, -17.53, 0.19193, 67, -26.02, -26.1, 0.04333, 68, 27.67, 23.26, 0.10192, 69, 6.05, 23.26, 0.66281, 4, 66, 30.99, -24.73, 0.11482, 67, -32.99, -24.29, 0.01818, 68, 34.39, 20.66, 0.03895, 69, 12.76, 20.66, 0.82805, 3, 66, 25.28, -37.45, 0.01593, 67, -43.98, -15.71, 0.00013, 69, 22.68, 10.86, 0.98394, 2, 68, 46.86, -1, 0.00122, 69, 25.23, -1, 0.99878, 2, 68, 39.91, -18.94, 0.28702, 69, 18.29, -18.94, 0.71298, 2, 68, 30.9, -28.05, 0.56229, 69, 9.27, -28.05, 0.43771, 3, 67, -20.46, 26.02, 0.00089, 68, 16.12, -27.87, 0.83553, 69, -5.5, -27.87, 0.16358, 3, 67, -7.64, 21.37, 0.10336, 68, 3.92, -21.76, 0.88186, 69, -17.7, -21.76, 0.01479, 2, 67, 1.91, 18.95, 0.4487, 68, -5.28, -18.26, 0.5513, 2, 67, 3.58, 21.69, 0.53305, 68, -7.26, -20.78, 0.46695, 2, 67, 13.69, 19.12, 0.74855, 68, -17, -17.06, 0.25145, 2, 67, 26.28, 5.69, 0.99513, 68, -27.95, -2.27, 0.00487, 2, 66, 6.33, 25.69, 0.14447, 67, 21.85, -12.35, 0.85553, 2, 67, 21, 2.01, 0.99774, 68, -22.29, 0.78, 0.00226, 2, 67, 10.67, 0.94, 0.99334, 68, -11.9, 0.65, 0.00666, 2, 67, -0.61, 0.2, 0.29931, 68, -0.61, 0.07, 0.70069, 4, 66, 3.01, -8.27, 0.00459, 67, -10.33, -1.04, 0.00154, 68, 9.19, 0.18, 0.99377, 69, -12.43, 0.18, 0.0001, 2, 68, 21.15, -0.07, 0.77042, 69, -0.47, -0.06, 0.22958, 3, 66, 11.9, -32.04, 0.00022, 67, -35.54, -4.01, 1e-05, 69, 12.95, 0.22, 0.99977], "hull": 17}}, "shaseng_8": {"shaseng_8": {"type": "mesh", "uvs": [1, 0.74479, 0.8914, 0.72383, 0.74694, 0.79154, 0.61377, 0.89472, 0.45126, 0.96889, 0.22645, 1, 0, 1, 0, 0.83074, 0.06216, 0.72982, 0.18373, 0.64298, 0.27245, 0.45523, 0.36773, 0.31676, 0.51887, 0.11962, 0.64206, 0, 1, 0, 0.88894, 0.27707, 0.75554, 0.391, 0.61634, 0.49871, 0.48595, 0.60616, 0.35298, 0.73037, 0.21746, 0.83448], "triangles": [20, 9, 19, 8, 9, 20, 7, 8, 20, 4, 19, 3, 20, 19, 4, 6, 7, 20, 5, 20, 4, 6, 20, 5, 10, 11, 17, 18, 10, 17, 9, 10, 18, 2, 18, 17, 19, 9, 18, 1, 2, 17, 19, 18, 2, 3, 19, 2, 15, 13, 14, 12, 13, 15, 16, 11, 12, 15, 16, 12, 17, 11, 16, 16, 15, 0, 14, 0, 15, 1, 16, 0, 17, 16, 1], "vertices": [2, 70, 29.35, 35.62, 0.62583, 73, 0.66, 35.62, 0.37417, 3, 70, 32.76, 28.52, 0.54619, 73, 4.07, 28.52, 0.45121, 74, -24.62, 28.52, 0.0026, 3, 70, 44.39, 25.2, 0.20709, 73, 15.7, 25.2, 0.71617, 74, -12.99, 25.2, 0.07674, 3, 70, 58.14, 24.75, 0.02989, 73, 29.45, 24.75, 0.56041, 74, 0.75, 24.75, 0.4097, 3, 70, 71.07, 20.88, 0.00044, 73, 42.38, 20.88, 0.19939, 74, 13.69, 20.88, 0.80018, 2, 73, 54.97, 10.96, 0.00568, 74, 26.27, 10.96, 0.99432, 1, 74, 36.63, -1.04, 1, 1, 74, 24.07, -11.88, 1, 2, 73, 42.43, -15.05, 0.03491, 74, 13.74, -15.05, 0.96509, 3, 70, 59.12, -14.16, 0.00012, 73, 30.43, -14.16, 0.50363, 74, 1.74, -14.16, 0.49626, 3, 70, 41.14, -21.48, 0.16831, 73, 12.44, -21.48, 0.83088, 74, -16.25, -21.48, 0.00081, 2, 70, 26.5, -25.29, 0.65267, 73, -2.19, -25.29, 0.34733, 2, 70, 4.96, -29.9, 0.9887, 73, -23.73, -29.9, 0.0113, 1, 70, -9.54, -31.03, 1, 1, 70, -25.91, -12.06, 1, 1, 70, -0.27, -0.21, 1, 1, 70, 14.28, 0.01, 1, 2, 70, 28.64, -0.47, 0.52972, 73, -0.06, -0.47, 0.47028, 1, 73, 13.88, -0.5, 1, 3, 70, 57.87, 0.4, 2e-05, 73, 29.17, 0.4, 0.29705, 74, 0.48, 0.4, 0.70293, 1, 74, 14.4, -0.11, 1], "hull": 15}}, "shaseng_9": {"shaseng_9": {"type": "mesh", "uvs": [1, 1, 0.83333, 1, 0.66667, 1, 0.5, 1, 0.33333, 1, 0.16667, 1, 0, 1, 0, 0.75, 0, 0.5, 0, 0.25, 0, 0, 0.16667, 0, 0.33333, 0, 0.5, 0, 0.66667, 0, 0.83333, 0, 1, 0, 1, 0.25, 1, 0.5, 1, 0.75, 0.83333, 0.75, 0.66667, 0.75, 0.5, 0.75, 0.33333, 0.75, 0.16667, 0.75, 0.83333, 0.5, 0.66667, 0.5, 0.5, 0.5, 0.33333, 0.5, 0.16667, 0.5, 0.83333, 0.25, 0.66667, 0.25, 0.5, 0.25, 0.33333, 0.25, 0.16667, 0.25], "triangles": [0, 1, 19, 1, 20, 19, 19, 20, 18, 20, 25, 18, 18, 25, 17, 25, 30, 17, 17, 30, 16, 30, 15, 16, 1, 2, 20, 2, 21, 20, 20, 21, 25, 21, 26, 25, 25, 26, 30, 26, 31, 30, 30, 31, 15, 31, 14, 15, 2, 3, 21, 3, 22, 21, 21, 22, 26, 22, 27, 26, 26, 27, 31, 27, 32, 31, 31, 32, 14, 32, 13, 14, 3, 4, 22, 4, 23, 22, 22, 23, 27, 23, 28, 27, 27, 28, 32, 28, 33, 32, 32, 33, 13, 33, 12, 13, 4, 5, 23, 5, 24, 23, 23, 24, 28, 24, 29, 28, 28, 29, 33, 29, 34, 33, 33, 34, 12, 34, 11, 12, 5, 6, 24, 6, 7, 24, 24, 7, 29, 7, 8, 29, 29, 8, 34, 8, 9, 34, 34, 9, 11, 9, 10, 11], "vertices": [87, -68.5, 58, -68.5, 29, -68.5, 0, -68.5, -29, -68.5, -58, -68.5, -87, -68.5, -87, -34.25, -87, 0, -87, 34.25, -87, 68.5, -58, 68.5, -29, 68.5, 0, 68.5, 29, 68.5, 58, 68.5, 87, 68.5, 87, 34.25, 87, 0, 87, -34.25, 58, -34.25, 29, -34.25, 0, -34.25, -29, -34.25, -58, -34.25, 58, 0, 29, 0, 0, 0, -29, 0, -58, 0, 58, 34.25, 29, 34.25, 0, 34.25, -29, 34.25, -58, 34.25], "hull": 20}}, "shaseng_10": {"shaseng_10": {"type": "mesh", "uvs": [1, 0.09294, 0.95387, 0.23544, 0.83607, 0.32284, 0.70687, 0.40929, 0.57514, 0.49099, 0.54571, 0.58985, 0.51738, 0.71903, 0.52737, 0.84667, 0.5333, 1, 0.47397, 1, 0.30665, 0.90452, 0.27224, 0.79594, 0.2224, 0.72029, 0.15001, 0.62417, 0.09542, 0.52538, 0.05414, 0.42723, 0.02862, 0.31413, 0, 0.16943, 0, 0, 0.09949, 0, 0.50077, 0, 1, 0, 0.4937, 0.06057, 0.94272, 0.08738, 0.85472, 0.18218, 0.76512, 0.28898, 0.64953, 0.37114, 0.52605, 0.45808, 0.50395, 0.58746, 0.48499, 0.7137, 0.49406, 0.8501, 0.26546, 0.7016, 0.29876, 0.79273, 0.33386, 0.88588, 0.50216, 0.97025, 0.12862, 0.51933, 0.19475, 0.60381, 0.08454, 0.31515, 0.1048, 0.41623, 0.07689, 0.08422, 0.08185, 0.17362, 0.42685, 0.93271, 0.40221, 0.82051, 0.38285, 0.70567, 0.36525, 0.59083, 0.33533, 0.48391, 0.33885, 0.38227, 0.36173, 0.29779, 0.40925, 0.19747], "triangles": [42, 43, 29, 30, 42, 29, 6, 30, 29, 7, 30, 6, 41, 42, 30, 34, 30, 7, 41, 30, 34, 9, 41, 34, 9, 10, 41, 34, 7, 8, 9, 34, 8, 45, 46, 27, 28, 45, 27, 28, 27, 4, 5, 28, 4, 44, 45, 28, 43, 44, 28, 29, 43, 28, 29, 28, 5, 6, 29, 5, 26, 48, 25, 47, 48, 26, 27, 46, 47, 3, 26, 25, 3, 25, 2, 26, 27, 47, 4, 27, 26, 4, 26, 3, 23, 20, 21, 23, 21, 0, 23, 24, 20, 1, 23, 0, 24, 23, 1, 25, 22, 24, 2, 25, 24, 1, 2, 24, 32, 31, 43, 11, 12, 31, 32, 11, 31, 32, 43, 42, 33, 32, 42, 33, 11, 32, 10, 11, 33, 33, 42, 41, 10, 33, 41, 36, 35, 45, 44, 36, 45, 13, 35, 36, 14, 35, 13, 31, 36, 44, 13, 36, 31, 31, 44, 43, 12, 13, 31, 46, 37, 47, 38, 37, 46, 15, 16, 37, 15, 37, 38, 45, 38, 46, 35, 38, 45, 15, 38, 35, 14, 15, 35, 39, 18, 19, 48, 39, 19, 17, 18, 39, 48, 40, 39, 17, 39, 40, 47, 40, 48, 16, 17, 40, 37, 16, 40, 47, 37, 40, 22, 19, 20, 24, 22, 20, 19, 22, 48, 48, 22, 25], "vertices": [1, 9, -4.44, 8.22, 1, 1, 9, 23.25, 17.88, 1, 2, 9, 47.52, 12.53, 0.62122, 10, -2.91, 12.19, 0.37878, 1, 10, 23.05, 11.44, 1, 2, 10, 48.64, 9.74, 0.31879, 11, 6, 7.68, 0.68121, 1, 11, 26.18, 5.89, 1, 2, 11, 52.35, 5.06, 0.3519, 12, 1.36, 4.89, 0.6481, 1, 12, 26.93, 4.78, 1, 1, 12, 57.59, 3.74, 1, 2, 8, 64.99, 15.53, 0.019, 12, 57.03, -5.15, 0.981, 2, 8, 40.32, -4.11, 0.99571, 12, 36.39, -28.99, 0.00429, 1, 8, 18, -3.78, 1, 2, 7, 42.42, -7.45, 0.46225, 8, 1.49, -7.31, 0.53775, 1, 7, 20.34, -7.56, 1, 2, 6, 41.74, -5.07, 0.53099, 7, -0.9, -5.07, 0.46901, 1, 6, 21.39, -8.15, 1, 2, 5, 45.77, -8.56, 0.62349, 6, -1.55, -8.43, 0.37651, 1, 5, 16.75, -12.25, 1, 2, 4, -75, 12.5, 4e-05, 5, -17.13, -11.53, 0.99996, 2, 4, -60.08, 12.5, 0.04589, 5, -16.82, 3.39, 0.95411, 3, 4, 0.12, 12.5, 0.97355, 5, -15.55, 63.57, 0.01175, 9, 20.59, -64.76, 0.01469, 2, 4, 75, 12.5, 0.00117, 9, -20.06, -1.87, 0.99883, 3, 4, -0.95, 0.39, 0.99488, 5, -3.46, 62.25, 0.00511, 6, -40.86, 68.34, 1e-05, 1, 9, -0.72, 0.4, 1, 3, 4, 53.21, -23.94, 0.00086, 9, 22.37, -0.39, 0.99905, 10, -24.27, -6.33, 8e-05, 5, 4, 39.77, -45.3, 4e-05, 5, 43.07, 101.99, 0, 6, 10.57, 101.49, 0, 9, 47.61, -0.08, 0.46963, 10, 0.17, -0.04, 0.53034, 5, 4, 22.43, -61.73, 0.00011, 5, 59.13, 84.31, 2e-05, 6, 24.12, 81.82, 1e-05, 9, 70.82, -5.72, 0, 10, 24.06, -0.01, 0.99985, 6, 4, 3.91, -79.12, 0.00018, 5, 76.13, 65.43, 0.00019, 6, 38.44, 60.83, 0.00081, 7, 18.84, 57.89, 0.0007, 10, 49.47, -0.1, 0.39349, 11, 0.44, -0.48, 0.60463, 4, 6, 63.49, 53.55, 4e-05, 7, 39.81, 42.38, 0.00134, 8, -13.89, 40.15, 1e-05, 11, 26.53, -0.38, 0.99861, 2, 11, 51.93, 0.1, 0.50377, 12, -0.01, 0.11, 0.49623, 2, 8, 36.67, 25.82, 0.00313, 12, 27.3, -0.25, 0.99687, 2, 7, 42.3, 0.01, 0.60643, 8, -0.54, -0.13, 0.39357, 3, 8, 18.35, 0.24, 0.99822, 11, 71.26, -25.52, 5e-05, 12, 14, -28.77, 0.00173, 2, 8, 37.7, 0.76, 0.98773, 12, 32.93, -24.68, 0.01227, 2, 8, 60.26, 21.09, 0.00183, 12, 51.36, -0.55, 0.99817, 2, 6, 41.31, 0.04, 0.40689, 7, 0.47, -0.13, 0.59311, 4, 6, 59.54, 7.22, 0, 7, 20.05, 0.29, 0.99935, 10, 105.6, -13.05, 0, 11, 35.84, -45.94, 0.00065, 2, 5, 46.15, -0.18, 0.50683, 6, -0.05, -0.18, 0.49317, 1, 6, 20.39, -0.3, 1, 1, 5, -0.05, -0.36, 1, 1, 5, 17.84, 0.01, 1, 2, 8, 50.21, 11.99, 0.28687, 12, 43.15, -11.35, 0.71313, 2, 8, 27.55, 13.91, 0.50242, 12, 20.53, -13.63, 0.49758, 4, 7, 51.6, 14.99, 0.10975, 8, 4.57, 16.74, 0.36726, 11, 52.34, -15.3, 0.35074, 12, -2.58, -15.08, 0.17225, 5, 6, 60.93, 32.89, 0.01054, 7, 30.26, 23.88, 0.42632, 8, -18.34, 19.82, 0.00912, 10, 85.23, 2.63, 0.0003, 11, 29.92, -20.92, 0.55371, 6, 4, -24.7, -84.28, 0.00713, 5, 80.69, 36.72, 0.0117, 6, 39.11, 31.77, 0.1837, 7, 9.4, 30.39, 0.30366, 10, 73.8, -16, 0.08131, 11, 9.31, -28.17, 0.41249, 6, 4, -24.17, -63.95, 0.05752, 5, 60.37, 37.67, 0.10719, 6, 19.11, 35.43, 0.29371, 7, -8.09, 40.76, 0.07385, 10, 59.46, -30.41, 0.30398, 11, -10.92, -30.31, 0.16374, 6, 4, -20.74, -47.06, 0.17457, 5, 43.55, 41.46, 0.23562, 6, 2.95, 41.44, 0.16719, 7, -21.17, 52, 0.01136, 10, 45.36, -40.33, 0.37525, 11, -28.11, -29.12, 0.03602, 7, 4, -13.61, -26.99, 0.45193, 5, 23.65, 49.01, 0.23337, 6, -15.77, 51.59, 0.04063, 7, -35.21, 68.01, 3e-05, 9, 61.21, -54.85, 0.00515, 10, 26.39, -50.01, 0.26808, 11, -48.94, -24.68, 0.00081], "hull": 22}}, "shaseng_11": {"shaseng_11": {"type": "mesh", "uvs": [0.70305, 0.08236, 0.78839, 0.19713, 0.8035, 0.31552, 0.8705, 0.40052, 0.8995, 0.49843, 0.9425, 0.56191, 1, 0.60818, 1, 0.73299, 0.93663, 0.85551, 0.86513, 0.74996, 0.83852, 0.65872, 0.80527, 0.62294, 0.76869, 0.63546, 0.6656, 0.70524, 0.61405, 0.79827, 0.57082, 0.89666, 0.52593, 1, 0.47106, 1, 0.40954, 0.9235, 0.38293, 0.8251, 0.35134, 0.7267, 0.29314, 0.66588, 0.19837, 0.57106, 0.12687, 0.44404, 0.06368, 0.26513, 0, 0.06964, 0, 0, 0.45772, 0, 0.90839, 0.66768, 0.94172, 0.71646, 0.87905, 0.58734, 0.84439, 0.51705, 0.80705, 0.43097, 0.77505, 0.32194, 0.72572, 0.22439, 0.67505, 0.10245, 0.02972, 0.05224, 0.10439, 0.22152, 0.18172, 0.39941, 0.25372, 0.50987, 0.33505, 0.62751, 0.39372, 0.71646, 0.43905, 0.79966, 0.47905, 0.90869, 0.54172, 0.75232, 0.47905, 0.67055, 0.43105, 0.573, 0.35772, 0.4654, 0.28839, 0.35637, 0.19372, 0.17848, 0.11505, 0.03646, 0.56305, 0.81544, 0.57905, 0.12971, 0.63239, 0.26743, 0.67905, 0.36211, 0.71239, 0.47114, 0.74572, 0.54717, 0.43772, 0.21865, 0.35372, 0.07376, 0.50305, 0.36068, 0.56572, 0.46827, 0.60172, 0.55291, 0.64305, 0.63182], "triangles": [58, 50, 27, 57, 58, 27, 57, 27, 52, 57, 48, 49, 57, 49, 58, 48, 57, 59, 29, 28, 6, 29, 6, 7, 9, 10, 28, 9, 28, 29, 8, 9, 29, 8, 29, 7, 30, 31, 4, 30, 4, 5, 11, 56, 31, 11, 31, 30, 12, 56, 11, 62, 56, 12, 10, 11, 30, 28, 30, 5, 28, 5, 6, 10, 30, 28, 13, 62, 12, 32, 2, 3, 33, 2, 32, 54, 33, 32, 55, 54, 32, 60, 54, 55, 31, 32, 3, 4, 31, 3, 56, 55, 32, 56, 32, 31, 61, 60, 55, 61, 55, 56, 62, 61, 56, 35, 27, 0, 52, 27, 35, 34, 0, 1, 35, 0, 34, 53, 52, 35, 53, 35, 34, 57, 52, 53, 33, 34, 1, 2, 33, 1, 59, 57, 53, 54, 53, 34, 54, 34, 33, 59, 53, 54, 60, 59, 54, 14, 44, 13, 51, 44, 14, 42, 44, 51, 15, 51, 14, 43, 42, 51, 43, 51, 15, 18, 19, 42, 43, 18, 42, 17, 18, 43, 16, 43, 15, 17, 43, 16, 46, 60, 61, 45, 46, 61, 45, 61, 62, 41, 40, 46, 41, 46, 45, 20, 21, 40, 41, 20, 40, 44, 45, 62, 44, 62, 13, 42, 41, 45, 42, 45, 44, 19, 20, 41, 19, 41, 42, 47, 48, 59, 47, 59, 60, 39, 38, 48, 39, 48, 47, 23, 38, 39, 22, 23, 39, 46, 47, 60, 40, 39, 47, 40, 47, 46, 22, 39, 40, 21, 22, 40, 50, 26, 27, 36, 26, 50, 25, 26, 36, 49, 50, 58, 37, 36, 50, 37, 50, 49, 25, 36, 37, 24, 25, 37, 38, 37, 49, 38, 49, 48, 24, 37, 38, 23, 24, 38], "vertices": [1, 80, -0.88, 8.34, 1, 1, 80, 32.9, 16.85, 1, 2, 80, 60.02, 8.71, 0.41892, 81, 0.84, 8.74, 0.58108, 1, 81, 25.99, 16.83, 1, 2, 81, 50.26, 14.79, 0.38543, 82, 2.25, 14.67, 0.61457, 3, 81, 68.35, 19.17, 0.00022, 82, 20.6, 17.86, 0.90742, 83, -15.54, 19.22, 0.09235, 2, 82, 36.91, 26.18, 0.45942, 83, 1.41, 26.15, 0.54058, 1, 83, 26.77, 10.93, 1, 1, 83, 43.36, -17.87, 1, 2, 82, 51.87, -19.52, 0.03604, 83, 12.53, -20.63, 0.96396, 6, 77, 149.29, 97.66, 7e-05, 78, 76.86, 102.13, 0.00605, 79, 2.38, 105.79, 0.00125, 81, 79.41, -14.11, 0.00373, 82, 29.47, -16.06, 0.83758, 83, -9.51, -15.32, 0.15132, 7, 77, 137.42, 95.95, 0.00215, 78, 65.12, 99.71, 0.04952, 79, -8.44, 100.62, 0.0107, 81, 68.33, -18.7, 0.10942, 82, 18.12, -19.92, 0.82576, 83, -21.14, -18.23, 0.00243, 4, 116.34, -119.14, 2e-05, 6, 77, 134.21, 86.7, 0.00696, 78, 62.47, 90.29, 0.1348, 79, -8.75, 90.84, 0.03248, 81, 67.51, -28.45, 0.2444, 82, 16.66, -29.6, 0.58101, 4, 107.02, -122.11, 0.00035, 7, 77, 131.71, 55.75, 0.01084, 78, 61.83, 59.24, 0.37775, 79, -1.93, 60.54, 0.1881, 80, 129.45, -61.63, 0.00166, 81, 72.74, -59.07, 0.20898, 82, 19.88, -60.49, 0.21156, 4, 80.73, -138.64, 0.00112, 5, 77, 141.5, 32.02, 0.00018, 78, 73.02, 36.14, 0.29088, 79, 14.48, 40.8, 0.56592, 81, 88.09, -79.64, 0.07251, 82, 33.86, -82.02, 0.07051, 4, 78, 86.44, 14.11, 0.02609, 79, 32.79, 22.63, 0.95381, 81, 105.42, -98.74, 0.00946, 82, 49.91, -102.21, 0.01064, 1, 79, 52.06, 3.67, 1, 1, 79, 47.39, -9.52, 1, 2, 78, 69.14, -23.75, 0.00025, 79, 25.07, -18.27, 0.99975, 2, 78, 45.93, -16.59, 0.48765, 79, 0.82, -16.89, 0.51235, 1, 78, 22.02, -10.5, 1, 2, 77, 67.38, -14.74, 0.52611, 78, 1.82, -14.97, 0.47389, 1, 77, 34.91, -20.64, 1, 2, 76, 94.74, -16.89, 0.38919, 77, -0.12, -17.22, 0.61081, 1, 76, 49.52, -13.38, 1, 1, 76, 0.68, -8.3, 1, 1, 76, -14.24, -1.26, 1, 2, 80, -44.61, -40.47, 0.22551, 4, 27.72, 28.5, 0.77449, 4, 78, 88.43, 115.84, 2e-05, 79, 10.32, 121.88, 0, 82, 39.24, -1.01, 0.29315, 83, 1.48, -1.14, 0.70683, 2, 82, 53.37, 1.51, 0.00013, 83, 15.77, 0.2, 0.99987, 2, 82, 18.86, 0.69, 0.99971, 83, -18.69, 2.24, 0.00029, 2, 81, 48.96, 0.11, 0.49949, 82, 0.01, 0.11, 0.50051, 6, 77, 101.25, 123.56, 0.00022, 78, 27.37, 125.1, 0.00076, 79, -51.18, 116.22, 5e-05, 81, 26.47, -0.88, 0.99861, 82, -22.5, 0.58, 0.0003, 4, 116.8, -73.64, 6e-05, 2, 80, 58.4, 1.47, 0.57544, 81, -0.53, 1.46, 0.42456, 4, 77, 49.62, 136.26, 0.00018, 78, -24.93, 134.7, 8e-05, 80, 32.14, -0.37, 0.99942, 4, 96.06, -24.68, 0.00032, 3, 77, 18.74, 143.22, 2e-05, 78, -56.18, 139.8, 0, 80, 0.49, -0.13, 0.99998, 2, 76, 0.19, 0.31, 0.99964, 4, -81.42, 16.12, 0.00036, 2, 76, 44.6, 0.42, 0.99923, 4, -62.38, -24, 0.00077, 5, 76, 91.14, 0.27, 0.53229, 77, -0.22, 0.32, 0.46728, 80, 12.29, -143.79, 3e-05, 81, -41.41, -145.37, 1e-05, 4, -42.66, -66.16, 0.0004, 1, 77, 31.74, -0.65, 1, 2, 77, 66.49, -0.73, 0.48732, 78, 0.1, -1.04, 0.51268, 3, 78, 25.93, -0.14, 1, 80, 103.09, -125.8, 0, 4, 11.4, -141.3, 0, 2, 78, 48.76, -1.32, 0.53424, 79, -0.09, -1.38, 0.46576, 1, 79, 27.67, -0.38, 1, 7, 77, 121.74, 23.76, 0.00185, 78, 53.78, 26.71, 0.46367, 79, -1.94, 27.04, 0.41641, 80, 126.49, -95, 0.00111, 81, 70.97, -92.52, 0.0628, 82, 15.94, -93.76, 0.05374, 4, 49.14, -149.8, 0.00043, 7, 77, 96.65, 22.57, 0.03459, 78, 28.81, 24.02, 0.8132, 79, -25.54, 18.43, 0.04139, 80, 102.23, -101.49, 0.00946, 81, 46.96, -99.88, 0.06265, 82, -8.5, -99.54, 0.03387, 4, 33.16, -130.42, 0.00483, 7, 77, 70.8, 26.61, 0.34837, 78, 2.77, 26.51, 0.5124, 79, -51.42, 14.6, 0.0004, 80, 76.11, -103.03, 0.03255, 81, 20.91, -102.35, 0.06446, 82, -34.65, -100.3, 0.01686, 4, 20.92, -107.3, 0.02496, 7, 76, 124.43, 34.19, 0.01214, 77, 39.18, 26.9, 0.77448, 78, -28.81, 24.91, 0.05433, 80, 45.15, -109.46, 0.04581, 81, -9.8, -109.88, 0.03029, 82, -65.79, -105.82, 0.00213, 4, 2.22, -81.8, 0.08082, 6, 76, 93.52, 29.22, 0.27345, 77, 7.9, 28.21, 0.50731, 78, -60.12, 24.35, 0.00229, 80, 14.3, -114.81, 0.0276, 81, -40.44, -116.33, 0.00791, 4, -15.46, -55.96, 0.18144, 3, 76, 45.09, 25.37, 0.71526, 77, -40.32, 34.12, 0.0045, 4, -39.6, -13.8, 0.28024, 2, 76, 6.09, 21.59, 0.85746, 4, -59.66, 19.86, 0.14254, 4, 78, 69.27, 23.03, 0.19326, 79, 13.98, 27.18, 0.73169, 81, 86.88, -93.21, 0.03773, 82, 31.77, -95.49, 0.03732, 5, 77, 9.25, 119.74, 0.0169, 78, -64.24, 115.8, 0.00215, 80, -3.8, -25.08, 0.71144, 81, -61.74, -27.31, 0.00077, 4, 58.66, -2.24, 0.26874, 6, 77, 43.54, 111.09, 0.05174, 78, -29.5, 109.21, 0.02186, 80, 31.54, -26.26, 0.77598, 81, -26.38, -27.22, 0.05359, 82, -76.95, -22.26, 1e-05, 4, 72.26, -34.88, 0.09682, 7, 77, 68.64, 107.19, 0.04689, 78, -4.21, 106.81, 0.04726, 79, -77.45, 90.89, 0.00026, 80, 56.9, -24.75, 0.47172, 81, -1.09, -24.8, 0.39414, 82, -51.56, -21.49, 0.00421, 4, 84.16, -57.32, 0.03553, 7, 77, 94.42, 98.52, 0.0326, 78, 22.05, 99.71, 0.09527, 79, -50.26, 90.29, 0.00701, 80, 83.93, -27.74, 0.06042, 81, 26.04, -26.83, 0.71337, 82, -24.62, -25.28, 0.0795, 4, 92.66, -83.16, 0.01182, 7, 77, 113.94, 94.54, 0.01383, 78, 41.77, 96.9, 0.10894, 79, -30.43, 92.29, 0.01784, 80, 103.86, -27.49, 0.00646, 81, 45.94, -25.86, 0.50586, 82, -4.7, -25.61, 0.34431, 4, 101.16, -101.18, 0.00278, 6, 76, 80.24, 77.59, 0.0171, 77, 4.56, 78.25, 0.12411, 78, -66.45, 74.1, 0.0108, 80, 0.42, -66.62, 0.23568, 81, -56.04, -68.67, 0.01513, 4, 22.62, -23.32, 0.59718, 3, 76, 40.05, 72.86, 0.01364, 80, -39.72, -71.85, 0.02087, 4, 1.2, 11.02, 0.96549, 7, 76, 117.79, 78.29, 0.00437, 77, 41.49, 71.44, 0.26726, 78, -29.17, 69.51, 0.09244, 80, 37.96, -65.44, 0.29609, 81, -18.56, -66.14, 0.11659, 82, -71.69, -61.61, 0.00517, 4, 39.28, -56.98, 0.21808, 7, 77, 71.48, 68.97, 0.18736, 78, 0.92, 68.84, 0.23789, 79, -63.37, 55.25, 0.00494, 80, 67.79, -61.48, 0.17348, 81, 11.11, -61.13, 0.28269, 82, -41.75, -58.53, 0.04348, 4, 55.26, -82.48, 0.07017, 7, 77, 93.05, 64.32, 0.09423, 78, 22.72, 65.48, 0.34625, 79, -41.39, 57.22, 0.03392, 80, 89.85, -61.46, 0.06342, 81, 33.15, -60.31, 0.31836, 82, -19.7, -59.16, 0.11997, 4, 64.44, -102.54, 0.02386, 7, 77, 114.34, 61.56, 0.02924, 78, 44.13, 64, 0.36407, 79, -20.25, 60.92, 0.10043, 80, 111.24, -59.63, 0.01264, 81, 54.46, -57.73, 0.27107, 82, 1.73, -57.96, 0.2173, 4, 74.98, -121.24, 0.00526], "hull": 28}}, "shaseng_12": {"shaseng_12": {"type": "mesh", "uvs": [0.96113, 0.09059, 1, 0.14557, 1, 0.42818, 0.86409, 0.58163, 0.78285, 0.7479, 0.61496, 0.96119, 0.45068, 1, 0.2988, 1, 0.19322, 0.94907, 0.17987, 0.86666, 0.15643, 0.84698, 0.11852, 0.71935, 0.26294, 0.58163, 0.12935, 0.46239, 0, 0.31628, 0, 0.17521, 0.07158, 0.08116, 0.21059, 0, 0.44717, 0, 0.77317, 0, 0.46153, 0.84236, 0.46951, 0.70135, 0.47749, 0.55737, 0.46313, 0.30207, 0.22751, 0.34115, 0.79077, 0.29508, 0.67343, 0.54801, 0.34491, 0.56986, 0.35637, 0.69262, 0.32029, 0.81727, 0.61662, 0.69741, 0.56508, 0.82926], "triangles": [4, 26, 3, 2, 3, 25, 27, 12, 24, 23, 22, 27, 24, 12, 13, 27, 24, 23, 22, 23, 26, 3, 26, 25, 26, 23, 25, 13, 14, 24, 25, 1, 2, 24, 15, 16, 16, 17, 24, 23, 17, 18, 23, 24, 17, 24, 14, 15, 23, 19, 25, 23, 18, 19, 25, 0, 1, 25, 19, 0, 6, 20, 5, 20, 31, 5, 6, 7, 20, 7, 29, 20, 7, 8, 29, 4, 31, 30, 4, 5, 31, 8, 9, 29, 9, 10, 29, 10, 11, 29, 29, 28, 20, 20, 21, 31, 20, 28, 21, 31, 21, 30, 29, 11, 28, 30, 26, 4, 11, 12, 28, 30, 21, 22, 21, 28, 22, 30, 22, 26, 12, 27, 28, 28, 27, 22], "vertices": [1, 14, 125.63, -136.07, 1, 1, 14, 109.31, -145.56, 1, 2, 13, 129.75, -136.66, 0.05055, 14, 28.32, -141.12, 0.94945, 2, 13, 83.4, -103.37, 0.29299, 14, -13.67, -102.47, 0.70701, 2, 13, 34.35, -84.9, 0.75452, 14, -60.12, -78.2, 0.24548, 2, 13, -29.71, -44.24, 0.99917, 14, -118.79, -30.09, 0.00083, 1, 13, -43.74, -1.21, 1, 1, 13, -46.43, 39.26, 1, 1, 13, -33.72, 68.35, 1, 2, 13, -10.35, 73.48, 0.99574, 14, -85.34, 84.42, 0.00426, 2, 13, -5.13, 80.1, 0.99123, 14, -79.35, 90.36, 0.00877, 2, 13, 30.75, 92.63, 0.9513, 14, -42.22, 98.46, 0.0487, 2, 13, 72.74, 56.78, 0.49519, 14, -4.87, 57.79, 0.50481, 2, 13, 104.52, 94.64, 0.02929, 14, 31.25, 91.53, 0.97071, 1, 14, 75.02, 123.72, 1, 1, 14, 115.44, 121.5, 1, 1, 14, 141.35, 100.94, 1, 1, 14, 162.57, 62.6, 1, 1, 14, 159.11, -0.47, 1, 1, 14, 154.34, -87.38, 1, 2, 13, 1.6, -1.1, 1, 14, -82.49, 8.95, 0, 1, 13, 42.12, -0.53, 1, 2, 13, 83.49, 0.08, 0.62519, 14, -1.06, 0.21, 0.37481, 1, 14, 72.31, 0.03, 1, 1, 14, 64.56, 63.46, 1, 2, 13, 164.16, -78.38, 0.00952, 14, 69.52, -87.43, 0.99048, 2, 13, 89.65, -51.94, 0.39947, 14, -1.24, -52.17, 0.60053, 2, 13, 77.57, 35.17, 0.48849, 14, -2.7, 35.76, 0.51151, 2, 13, 42.62, 29.78, 0.85494, 14, -38.04, 34.63, 0.14506, 2, 13, 6.28, 37.01, 0.96419, 14, -73.24, 46.21, 0.03581, 2, 13, 45.86, -39.65, 0.82636, 14, -43.23, -34.68, 0.17364, 2, 13, 7.19, -28.43, 0.93861, 14, -80.25, -18.86, 0.06139], "hull": 20}}, "shaseng_13": {"shaseng_13": {"type": "mesh", "uvs": [0.82357, 0.29009, 0.87392, 0.44302, 0.87611, 0.62655, 0.88147, 0.66568, 1, 0.82126, 1, 1, 0.77391, 1, 0.62561, 0.91813, 0.56026, 0.6827, 0.50497, 0.66059, 0.19078, 0.57995, 0, 0.37834, 0, 0, 0.21591, 0, 0.56087, 0, 0.71861, 0.64108, 0.5678, 0.4837, 0.40694, 0.32371, 0.81161, 0.8726], "triangles": [2, 15, 1, 15, 2, 3, 8, 9, 15, 18, 15, 3, 18, 3, 4, 8, 15, 18, 7, 8, 18, 6, 7, 18, 18, 4, 5, 6, 18, 5, 16, 17, 0, 16, 0, 1, 10, 11, 17, 10, 17, 16, 15, 16, 1, 9, 10, 16, 9, 16, 15, 17, 13, 14, 17, 14, 0, 11, 12, 13, 11, 13, 17], "vertices": [2, 58, 198.53, 61.79, 0.31854, 59, 19.62, 59.45, 0.68146, 2, 58, 242.41, 55.28, 0.0151, 59, 61.95, 46.16, 0.9849, 2, 59, 108.83, 22.55, 0.36816, 61, 4, 24.4, 0.63184, 2, 59, 119.15, 18.15, 0.07043, 61, 15.11, 22.81, 0.92957, 1, 61, 62.3, 30.58, 1, 1, 61, 112.27, 19.8, 1, 1, 61, 105.22, -12.91, 1, 1, 61, 77.7, -29.42, 1, 2, 59, 101.83, -26.38, 0.4211, 61, 9.84, -24.68, 0.5789, 2, 59, 92.48, -30.79, 0.74841, 61, 1.94, -31.35, 0.25159, 2, 58, 248.21, -52.99, 0.03319, 59, 50.76, -61.68, 0.96681, 2, 58, 184.63, -61.91, 0.59962, 59, -13.43, -60.55, 0.40038, 1, 58, 81.79, -28.28, 1, 1, 58, 91.72, 2.09, 1, 2, 58, 107.59, 50.62, 0.99991, 59, -71.95, 62.63, 9e-05, 2, 59, 101.91, -0.1, 0.03775, 61, 3.15, 0.74, 0.96225, 1, 59, 51.67, 0.53, 1, 2, 58, 188.5, 0.19, 0.48863, 59, 0.09, 0.18, 0.51137, 1, 61, 70.78, 0.23, 1], "hull": 15}}, "shaseng_14": {"shaseng_14": {"type": "mesh", "uvs": [1, 0.35248, 0.94384, 0.54028, 0.84301, 0.56602, 0.84906, 0.68719, 0.76033, 0.72808, 0.78049, 0.81441, 0.77243, 0.88407, 0.65547, 0.9386, 0.5385, 0.94163, 0.38524, 1, 0, 1, 0, 0.90831, 0.1856, 0.84924, 0.33886, 0.7508, 0.31265, 0.72051, 0.33848, 0.68163, 0.15715, 0.51594, 0.16974, 0.45356, 0.18345, 0.38562, 0.19775, 0.3147, 0.2176, 0.21634, 0.58629, 0, 0.74073, 0, 1, 0, 0.58426, 0.82785, 0.39067, 0.4056, 0.58691, 0.17457, 0.49409, 0.64461, 0.35885, 0.90154], "triangles": [10, 12, 9, 12, 28, 9, 9, 28, 8, 10, 11, 12, 8, 24, 7, 8, 28, 24, 7, 24, 6, 12, 13, 28, 28, 13, 24, 6, 24, 5, 13, 27, 24, 24, 4, 5, 24, 27, 4, 14, 15, 13, 13, 15, 27, 3, 4, 2, 4, 27, 2, 15, 16, 25, 25, 16, 17, 15, 25, 27, 25, 17, 18, 27, 25, 2, 19, 20, 25, 1, 2, 0, 26, 0, 2, 26, 2, 25, 18, 19, 25, 25, 20, 26, 26, 22, 0, 22, 23, 0, 20, 21, 26, 26, 21, 22], "vertices": [2, 62, 59.59, 86.22, 0.75101, 63, 24.23, 108.99, 0.24899, 2, 62, 103.12, 102.24, 0.46329, 63, 63.66, 84.56, 0.53671, 2, 62, 118.24, 90.27, 0.31216, 63, 63.55, 65.28, 0.68784, 2, 62, 142.17, 107.04, 0.1034, 63, 91.49, 56.75, 0.8966, 3, 62, 159.16, 98.89, 0.04336, 63, 95.55, 38.35, 0.95589, 65, -39.05, -10.2, 0.00075, 3, 62, 174.66, 113.25, 0.00436, 63, 116.4, 34.99, 0.87451, 65, -34.42, 10.4, 0.12113, 3, 62, 189.55, 121.13, 0.0001, 63, 131.78, 28.11, 0.67004, 65, -26.61, 25.34, 0.32986, 2, 63, 137.27, 3.8, 0.18548, 65, -2.01, 29.33, 0.81452, 1, 65, 17.81, 21.86, 1, 1, 65, 48.82, 24.18, 1, 1, 65, 113.19, -2.64, 1, 1, 65, 104.69, -23.04, 1, 2, 63, 89.06, -69.5, 0.01424, 65, 68.21, -23.26, 0.98576, 2, 63, 75.73, -35.52, 0.53178, 65, 33.47, -34.49, 0.46822, 2, 63, 67.28, -37.61, 0.70647, 65, 35.05, -43.05, 0.29353, 2, 63, 59.96, -30.12, 0.86458, 65, 27.13, -49.9, 0.13542, 2, 62, 175.45, -20.55, 0.19, 63, 11.48, -48.05, 0.81, 2, 62, 161.82, -26.29, 0.37, 63, -2.02, -40.83, 0.63, 2, 62, 146.67, -33.24, 0.51, 63, -16.72, -32.95, 0.49, 2, 62, 130.85, -40.49, 0.7, 63, -32.06, -24.74, 0.3, 2, 62, 108.87, -50.54, 0.83, 63, -53.16, -14.07, 0.17, 1, 62, 28.86, -22.78, 1, 2, 62, 13.7, 0.7, 1, 63, -71.4, 92.47, 0, 2, 62, -11.77, 40.12, 0.99967, 63, -56.03, 136.81, 0.00033, 3, 62, 196.65, 85.17, 1e-05, 63, 107.82, 0.37, 0.56733, 65, -0.38, -0.27, 0.43266, 2, 62, 130.18, 0.52, 0.51846, 63, 0.2, 0.59, 0.48154, 1, 62, 64.14, 0.15, 1, 2, 63, 60.76, -0.59, 0.99909, 65, -2.3, -47.31, 0.00091, 1, 65, 44.11, 0.44, 1], "hull": 24}}, "shaseng_15": {"shaseng_15": {"type": "mesh", "uvs": [1, 0.30241, 0.94454, 0.51856, 0.77721, 0.67796, 0.75407, 0.80786, 0.79843, 0.86268, 0.7906, 0.92845, 0.64449, 0.95997, 0.42355, 0.98912, 0.29346, 1, 0.09093, 1, 0, 0.98238, 0, 0.96218, 0.06016, 0.91935, 0.19246, 0.89026, 0.334, 0.83046, 0.32631, 0.79895, 0.38631, 0.74238, 0.29246, 0.60097, 0.10905, 0.48412, 0.14214, 0.23295, 0.3245, 0, 1, 0, 0.58501, 0.86068, 0.2903, 0.95089, 0.42332, 0.91317, 0.19985, 0.95509, 0.51642, 0.49258, 0.59623, 0.27739, 0.54835, 0.65467], "triangles": [9, 25, 8, 25, 23, 8, 9, 11, 12, 9, 12, 25, 9, 10, 11, 12, 13, 25, 25, 13, 23, 8, 23, 7, 23, 24, 7, 7, 24, 6, 24, 22, 6, 24, 23, 14, 23, 13, 14, 24, 14, 22, 22, 14, 16, 14, 15, 16, 6, 22, 5, 5, 22, 4, 22, 3, 4, 28, 3, 22, 22, 16, 28, 3, 28, 2, 16, 17, 28, 1, 2, 26, 17, 26, 28, 2, 28, 26, 17, 18, 26, 26, 27, 1, 1, 27, 0, 27, 26, 19, 26, 18, 19, 27, 21, 0, 19, 20, 27, 27, 20, 21], "vertices": [2, 54, 67.43, 64.7, 0.97559, 55, -46.32, 83.31, 0.02441, 2, 54, 132.09, 68.62, 0.52189, 55, 16.34, 66.86, 0.47811, 2, 54, 183.58, 52.16, 0.02848, 55, 60.12, 35.15, 0.97152, 1, 55, 97.97, 26.84, 1, 1, 55, 114.97, 31.72, 1, 1, 55, 134.21, 28.11, 1, 2, 55, 140.71, 4.34, 0.88335, 56, 7.33, 29.82, 0.11665, 3, 55, 145.08, -30.92, 0.07773, 56, 41.49, 20.02, 0.91983, 57, -19.15, 14.1, 0.00244, 3, 55, 145.8, -51.46, 0.00101, 56, 60.67, 12.64, 0.4024, 57, 1.4, 14.08, 0.59659, 1, 57, 32.6, 9.09, 1, 1, 57, 45.78, 1.68, 1, 1, 57, 44.83, -4.24, 1, 2, 56, 80.15, -26.33, 0.00303, 57, 33.56, -15.32, 0.99697, 2, 56, 57.96, -23.46, 0.30581, 57, 11.81, -20.59, 0.69419, 3, 55, 96.6, -39.02, 0.18628, 56, 29.95, -27.75, 0.78997, 57, -12.8, -34.64, 0.02376, 3, 55, 87.17, -39.06, 0.38382, 56, 26.29, -36.45, 0.6145, 57, -13.09, -44.07, 0.00168, 2, 55, 71.64, -27.71, 0.79919, 56, 9.77, -46.29, 0.20081, 2, 54, 175.76, -26.46, 0.0828, 55, 28.16, -37.1, 0.9172, 2, 54, 147.24, -61.24, 0.65149, 55, -9.79, -61.24, 0.34851, 2, 54, 73.05, -70.59, 0.99956, 55, -83.19, -46.98, 0.00044, 1, 54, -0.33, -56.05, 1, 1, 54, -20.7, 47.34, 1, 1, 56, 0.57, -0.34, 1, 2, 56, 53.78, -0.22, 0.56344, 57, -0.42, -0.4, 0.43656, 2, 55, 122.69, -28.2, 0.00282, 56, 30.21, 0.49, 0.99718, 2, 56, 66.61, -6.22, 0.00589, 57, 13.71, -1.4, 0.99411, 2, 54, 137.42, 1.6, 0.4609, 55, 0.49, 1.52, 0.5391, 2, 54, 72.31, 1.47, 1, 55, -61.41, 21.71, 0, 2, 54, 183.69, 15.79, 0.00013, 55, 48.88, 0.57, 0.99987], "hull": 22}}, "shaseng_16": {"shaseng_16": {"type": "mesh", "uvs": [1, 0.2286, 0.89685, 0.47337, 0.77556, 0.528, 0.32946, 0.74795, 0.33404, 0.77152, 0.31389, 0.79508, 0.36704, 0.78532, 0.39599, 0.75618, 0.45917, 0.74761, 0.5434, 0.77418, 0.56051, 0.83761, 0.45654, 0.91646, 0.37625, 0.95846, 0.21963, 1, 0.19533, 1, 0.03835, 0.95304, 0, 0.88135, 0, 0.82111, 0.06844, 0.67535, 0.1301, 0.65452, 0.24201, 0.51471, 0.25343, 0.47455, 0.33793, 0.37638, 0.48867, 0.33473, 0.50237, 0.29903, 0.46866, 0.27878, 0.44734, 0.13714, 0.60513, 0, 0.79257, 0, 1, 0, 0.23731, 0.79988, 0.28781, 0.85874, 0.67754, 0.46434, 0.45012, 0.63378, 0.73482, 0.23163], "triangles": [12, 13, 31, 31, 13, 14, 14, 15, 31, 12, 31, 11, 6, 11, 31, 6, 31, 5, 31, 16, 30, 31, 15, 16, 11, 6, 10, 9, 10, 8, 8, 10, 7, 6, 7, 10, 16, 17, 30, 31, 30, 5, 17, 18, 30, 18, 19, 30, 5, 30, 3, 5, 3, 4, 3, 30, 19, 3, 19, 33, 2, 3, 33, 19, 20, 33, 20, 21, 33, 33, 21, 32, 33, 32, 2, 22, 32, 21, 32, 22, 23, 2, 32, 1, 32, 34, 1, 1, 34, 0, 23, 24, 32, 32, 24, 34, 24, 25, 34, 25, 26, 34, 26, 27, 34, 34, 28, 0, 34, 27, 28, 28, 29, 0], "vertices": [1, 50, 44.91, 40.01, 1, 2, 50, 105.01, 33.89, 0.93952, 51, -21.02, 26.89, 0.06048, 2, 50, 120.94, 17.5, 0.41856, 51, 1.1, 21.13, 0.58144, 2, 51, 85.89, 2.91, 0.99654, 52, -3.46, 18.66, 0.00346, 2, 51, 89.68, 7.11, 0.92462, 52, 1.75, 16.48, 0.07538, 2, 51, 95.97, 8.4, 0.52378, 52, 5.05, 10.97, 0.47622, 2, 51, 88.83, 13.13, 0.14978, 52, 7.15, 19.27, 0.85022, 2, 51, 80.64, 12, 0.07899, 52, 3.38, 26.62, 0.92101, 2, 51, 72.71, 18.09, 0.04832, 52, 6.5, 36.12, 0.95168, 2, 51, 68.98, 32.11, 0.02076, 52, 18.49, 44.28, 0.97924, 2, 51, 78.68, 43.97, 0.00806, 52, 32.9, 39.05, 0.99194, 2, 51, 103.42, 44.01, 0.00012, 52, 41.12, 15.72, 0.99988, 1, 52, 43.58, -0.06, 1, 1, 52, 40.03, -26.03, 1, 1, 52, 38.15, -29.29, 1, 2, 51, 152.32, 0.58, 0.00567, 52, 16.32, -44.8, 0.99433, 2, 51, 143.27, -15.06, 0.08999, 52, -1.43, -41.44, 0.91001, 2, 51, 132.41, -24.42, 0.27431, 52, -13.85, -34.28, 0.72569, 2, 51, 99.2, -39.02, 0.93318, 52, -38.63, -7.78, 0.06682, 2, 51, 89.21, -35.02, 0.98574, 52, -38.15, 2.97, 0.01426, 2, 50, 131.51, -64.58, 0.04362, 51, 52.68, -43.59, 0.95638, 2, 50, 121.8, -64.42, 0.08832, 51, 44.28, -48.49, 0.91168, 2, 50, 96.59, -55.37, 0.29475, 51, 18.03, -53.81, 0.70525, 2, 50, 82.94, -33.97, 0.72944, 51, -4.73, -42.58, 0.27056, 2, 50, 74.21, -33.28, 0.91173, 51, -12.55, -46.52, 0.08827, 2, 50, 70.32, -39.23, 0.96179, 51, -12.79, -53.62, 0.03821, 1, 50, 37.62, -48.07, 1, 1, 50, 1.39, -29.35, 1, 1, 50, -3.42, -0.7, 1, 1, 50, -8.75, 31.01, 1, 2, 51, 104.58, 0.15, 0.49362, 52, 0.12, 0.11, 0.50638, 1, 52, 16.17, -0.09, 1, 2, 50, 108.52, 0.01, 0.58578, 51, -0.46, -0.27, 0.41422, 2, 50, 154.12, -28.08, 3e-05, 51, 53.1, -0.66, 0.99997, 1, 50, 52.42, -0.4, 1], "hull": 30}}, "shaseng_17": {"shaseng_17": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-0.02, 28.45, 43.66, 11.1, 29.99, -23.29, -13.68, -5.94], "hull": 4}}, "shaseng_18": {"shaseng_18": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [47.98, 1, 24.52, -27.61, -13.37, 3.46, 10.09, 32.07], "hull": 4}}, "shaseng_19": {"shaseng_20": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [375.06, 106.45, 375.63, -95.55, -474.36, -97.97, -474.94, 104.03], "hull": 4}}, "shaseng_20": {"shaseng_15": {"type": "mesh", "uvs": [1, 0.30241, 0.94454, 0.51856, 0.77721, 0.67796, 0.75407, 0.80786, 0.79843, 0.86268, 0.7906, 0.92845, 0.64449, 0.95997, 0.42355, 0.98912, 0.29346, 1, 0.09093, 1, 0, 0.98238, 0, 0.96218, 0.06016, 0.91935, 0.19246, 0.89026, 0.334, 0.83046, 0.32631, 0.79895, 0.38631, 0.74238, 0.29246, 0.60097, 0.10905, 0.48412, 0.14214, 0.23295, 0.3245, 0, 1, 0, 0.58501, 0.86068, 0.2903, 0.95089, 0.42332, 0.91317, 0.19985, 0.95509, 0.51642, 0.49258, 0.59623, 0.27739, 0.54835, 0.65467], "triangles": [9, 25, 8, 25, 23, 8, 9, 11, 12, 9, 12, 25, 9, 10, 11, 12, 13, 25, 25, 13, 23, 8, 23, 7, 23, 24, 7, 7, 24, 6, 24, 22, 6, 24, 23, 14, 23, 13, 14, 24, 14, 22, 22, 14, 16, 14, 15, 16, 6, 22, 5, 5, 22, 4, 22, 3, 4, 28, 3, 22, 22, 16, 28, 3, 28, 2, 16, 17, 28, 1, 2, 26, 17, 26, 28, 2, 28, 26, 17, 18, 26, 26, 27, 1, 1, 27, 0, 27, 26, 19, 26, 18, 19, 27, 21, 0, 19, 20, 27, 27, 20, 21], "vertices": [2, 84, 67.43, 64.7, 0.97559, 85, -46.32, 83.31, 0.02441, 2, 84, 132.09, 68.62, 0.52189, 85, 16.34, 66.86, 0.47811, 2, 84, 183.58, 52.16, 0.02848, 85, 60.12, 35.15, 0.97152, 1, 85, 97.97, 26.84, 1, 1, 85, 114.97, 31.72, 1, 1, 85, 134.21, 28.11, 1, 2, 85, 140.71, 4.34, 0.88335, 86, 7.33, 29.82, 0.11665, 3, 85, 145.08, -30.92, 0.07773, 86, 41.49, 20.02, 0.91983, 87, -19.15, 14.1, 0.00244, 3, 85, 145.8, -51.46, 0.00101, 86, 60.67, 12.64, 0.4024, 87, 1.4, 14.08, 0.59659, 1, 87, 32.6, 9.09, 1, 1, 87, 45.78, 1.68, 1, 1, 87, 44.83, -4.24, 1, 2, 86, 80.15, -26.33, 0.00303, 87, 33.56, -15.32, 0.99697, 2, 86, 57.96, -23.46, 0.30581, 87, 11.81, -20.59, 0.69419, 3, 85, 96.6, -39.02, 0.18628, 86, 29.95, -27.75, 0.78997, 87, -12.8, -34.64, 0.02376, 3, 85, 87.17, -39.06, 0.38382, 86, 26.29, -36.45, 0.6145, 87, -13.09, -44.07, 0.00168, 2, 85, 71.64, -27.71, 0.79919, 86, 9.77, -46.29, 0.20081, 2, 84, 175.76, -26.46, 0.0828, 85, 28.16, -37.1, 0.9172, 2, 84, 147.24, -61.24, 0.65149, 85, -9.79, -61.24, 0.34851, 2, 84, 73.05, -70.59, 0.99956, 85, -83.19, -46.98, 0.00044, 1, 84, -0.33, -56.05, 1, 1, 84, -20.7, 47.34, 1, 1, 86, 0.57, -0.34, 1, 2, 86, 53.78, -0.22, 0.56344, 87, -0.42, -0.4, 0.43656, 2, 85, 122.69, -28.2, 0.00282, 86, 30.21, 0.49, 0.99718, 2, 86, 66.61, -6.22, 0.00589, 87, 13.71, -1.4, 0.99411, 2, 84, 137.42, 1.6, 0.4609, 85, 0.49, 1.52, 0.5391, 2, 84, 72.31, 1.47, 1, 85, -61.41, 21.71, 0, 2, 84, 183.69, 15.79, 0.00013, 85, 48.88, 0.57, 0.99987], "hull": 22}}, "shaseng_21": {"wuqi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [430.47, 119.79, 431.13, -108.57, -529.78, -111.31, -530.43, 117.05], "hull": 4}}, "xuewu7": {"xuewu": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [11, -12, -11, -12, -11, 12, 11, 12], "hull": 4}}, "xuewu8": {"xuewu": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [11, -12, -11, -12, -11, 12, 11, 12], "hull": 4}}, "xuewu9": {"xuewu": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [11, -12, -11, -12, -11, 12, 11, 12], "hull": 4}}, "xuewu10": {"xuewu": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [11, -12, -11, -12, -11, 12, 11, 12], "hull": 4}}, "xuewu11": {"xuewu": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [11, -12, -11, -12, -11, 12, 11, 12], "hull": 4}}, "xuewu12": {"xuewu": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [11, -12, -11, -12, -11, 12, 11, 12], "hull": 4}}}}], "events": {"tingzhi": {}, "yidong": {}}, "animations": {"1": {"slots": {"daoguang": {"color": [{"time": 0.1333, "color": "ffffffff", "curve": 0.458, "c2": 0.02, "c4": 0.27}, {"time": 0.3333, "color": "ffffff00"}], "attachment": [{"name": null}, {"time": 0.0333, "name": "<PERSON><PERSON><PERSON>"}, {"time": 0.1333, "name": null}]}, "daoguang5": {"attachment": [{"time": 0.1333, "name": "dao3_2"}, {"time": 0.1667, "name": "dao3_4"}, {"time": 0.2, "name": null}]}, "daoying1": {"color": [{"time": 0.3, "color": "ffffffff"}, {"time": 0.3333, "color": "ffffff00"}], "attachment": [{"name": null}, {"time": 0.0333, "name": "daoying1"}, {"time": 0.1333, "name": null}]}}, "bones": {"body": {"translate": [{"y": -1, "curve": "stepped"}, {"time": 0.0333, "y": -1, "curve": 0.213, "c2": 0.34, "c3": 0.542, "c4": 0.67}, {"time": 0.0667, "x": 5.65, "y": 21.08, "curve": 0.257, "c2": 0.34, "c3": 0.588, "c4": 0.68}, {"time": 0.1, "x": 3.5, "y": 9.56, "curve": 0.276, "c2": 0.35, "c3": 0.608, "c4": 0.68}, {"time": 0.1333, "x": 2.13, "y": -19.58, "curve": "stepped"}, {"time": 0.2333, "x": 2.13, "y": -19.58, "curve": 0.158, "c2": 0.54, "c3": 0.462, "c4": 0.94}, {"time": 0.3333, "y": -1}]}, "shaseng_13": {"rotate": [{"angle": -2.5, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": -0.47, "curve": 0.334, "c2": 0.34, "c3": 0.758}, {"time": 0.1333, "angle": -16.33, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.2667, "angle": -0.57, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.3333, "angle": -2.5}]}, "shaseng_14": {"rotate": [{"angle": -5.57, "curve": "stepped"}, {"time": 0.0333, "angle": -5.57, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.1333, "angle": -26.37, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.2667, "angle": -0.13, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.3333, "angle": -5.57}]}, "shaseng_15": {"rotate": [{"angle": -14.31, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": -15.52, "curve": 0.37, "c2": 0.48, "c3": 0.753}, {"time": 0.2, "angle": -48.02, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.3333, "angle": -14.31}]}, "shaseng_16": {"rotate": [{"angle": -10.28, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": -18.64, "curve": 0.334, "c2": 0.34, "c3": 0.758}, {"time": 0.1667, "angle": -18.05, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3, "angle": -1.2, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "angle": -10.28}]}, "shaseng_17": {"rotate": [{"angle": 2.32, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": 2.82, "curve": 0.358, "c2": 0.42, "c3": 0.756}, {"time": 0.1, "angle": -16.43, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.2333, "angle": 7.34, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.3333, "angle": 2.32}]}, "shaseng_18": {"rotate": [{"angle": 0.61, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": -1.4, "curve": 0.334, "c2": 0.34, "c3": 0.758}, {"time": 0.1333, "angle": -9.06, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.2667, "angle": 6.61, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.3333, "angle": 0.61}]}, "shaseng_19": {"rotate": [{"angle": -6.09, "curve": "stepped"}, {"time": 0.0333, "angle": -6.09, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.1667, "angle": -22.27, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.3, "angle": 13.02, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.3333, "angle": -6.09}]}, "shaseng_20": {"rotate": [{"angle": -9.23, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": -10.83, "curve": 0.37, "c2": 0.48, "c3": 0.753}, {"time": 0.2, "angle": -42.75, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.3333, "angle": -9.23}]}, "shaseng_22": {"rotate": [{"angle": 0.88, "curve": 0, "c2": 0.39, "c3": 0.246, "c4": 0.77}, {"time": 0.0333, "angle": 6.81, "curve": 0.244, "c2": 0.55, "c3": 0.57, "c4": 0.91}, {"time": 0.1, "angle": 2.08, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.1333, "angle": 17.89, "curve": 0, "c2": 0.19, "c3": 0.486}, {"time": 0.2667, "angle": 15.71, "curve": 0, "c2": 0.19, "c3": 0.486}, {"time": 0.3333, "angle": 0.88}], "translate": [{"curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "x": -6.23, "y": 4.66, "curve": "stepped"}, {"time": 0.1, "x": -6.23, "y": 4.66, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.1333, "y": -11.52, "curve": "stepped"}, {"time": 0.2667, "y": -11.52, "curve": 0, "c2": 0.19, "c3": 0.486}, {"time": 0.3333}]}, "shaseng_23": {"rotate": [{"angle": 0.91, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": -26.29, "curve": "stepped"}, {"time": 0.1, "angle": -26.29, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.1333, "angle": 29.28, "curve": 0, "c2": 0.19, "c3": 0.486}, {"time": 0.2667, "angle": 26.77, "curve": 0, "c2": 0.19, "c3": 0.486}, {"time": 0.3333, "angle": 0.91}], "translate": [{"x": -1.1, "y": -0.05, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "x": 24.53, "y": -19.12, "curve": "stepped"}, {"time": 0.1, "x": 24.53, "y": -19.12, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.1333, "x": -44.52, "y": 9.1, "curve": 0, "c2": 0.19, "c3": 0.486}, {"time": 0.2667, "x": -35.64, "y": 7.23, "curve": 0, "c2": 0.19, "c3": 0.486}, {"time": 0.3333, "x": -1.1, "y": -0.05}]}, "shaseng_6": {"rotate": [{"angle": 0.4, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": -23.95, "curve": "stepped"}, {"time": 0.1, "angle": -23.95, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.1333, "angle": 0.4}], "translate": [{"x": 0.11, "y": -0.01, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "x": 7.34, "y": -20.5, "curve": "stepped"}, {"time": 0.1, "x": 7.34, "y": -20.5, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.1333, "x": 0.11, "y": -0.01}]}, "shaseng_3": {"rotate": [{"angle": -1.22, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": 0.94, "curve": "stepped"}, {"time": 0.1, "angle": 0.94, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.1333, "angle": -1.22}]}, "shaseng_4": {"rotate": [{"angle": -3.22, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": 13.52, "curve": "stepped"}, {"time": 0.1, "angle": 13.52, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.1333, "angle": -3.22}]}, "shaseng_5": {"rotate": [{"angle": -11.17}]}, "shaseng_7": {"rotate": [{"angle": 0.32, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": 1.44, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1333, "angle": 0.32}]}, "shaseng_8": {"rotate": [{"angle": -0.85, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": 13.81, "curve": "stepped"}, {"time": 0.1, "angle": 13.81, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.1333, "angle": -0.85}]}, "shaseng_9": {"rotate": [{"angle": -19.64, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": 14.97, "curve": "stepped"}, {"time": 0.1, "angle": 14.97, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.1333, "angle": -19.64}]}, "shaseng_10": {"rotate": [{"angle": -2.78, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": 2.8, "curve": "stepped"}, {"time": 0.1, "angle": 2.8, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.1333, "angle": -2.78}]}, "shaseng_11": {"rotate": [{"angle": -7.55}]}, "shaseng_21": {"rotate": [{"angle": -6.53, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": -8.68, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.1333, "angle": -6.53, "curve": "stepped"}, {"time": 0.2667, "angle": -6.53, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.3, "angle": 3.84, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.3333, "angle": -6.53}]}, "shaseng_1": {"rotate": [{"angle": 0.65, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": -3.22, "curve": 0.334, "c2": 0.34, "c3": 0.758}, {"time": 0.1333, "angle": 0.65}]}, "shaseng_2": {"rotate": [{"angle": 5.3}]}, "shaseng_25": {"rotate": [{"angle": -1.32, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": 5.1, "curve": "stepped"}, {"time": 0.1, "angle": 5.1, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.1333, "angle": -1.32}]}, "shaseng_26": {"rotate": [{"angle": -5.94, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": -6.53, "curve": 0.37, "c2": 0.48, "c3": 0.753}, {"time": 0.1333, "angle": -5.94, "curve": "stepped"}, {"time": 0.2667, "angle": -5.94, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.3, "angle": 5.48, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 0.3333, "angle": -5.94}]}, "shaseng_27": {"rotate": [{"angle": 1.22, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": 3.77, "curve": 0.334, "c2": 0.34, "c3": 0.758}, {"time": 0.1333, "angle": 1.22}]}, "shaseng_28": {"rotate": [{"angle": -5.72, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": 7.78, "curve": "stepped"}, {"time": 0.1, "angle": 7.78, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.1333, "angle": -5.72}]}, "shaseng_29": {"rotate": [{"angle": -10.42, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": -10.9, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 0.1333, "angle": -10.42}]}, "shaseng_30": {"rotate": [{"angle": -1.53, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": -3.17, "curve": 0.358, "c2": 0.42, "c3": 0.756}, {"time": 0.1333, "angle": -1.53, "curve": "stepped"}, {"time": 0.2667, "angle": -1.53, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.3, "angle": 3.98, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.3333, "angle": -1.53}]}, "shaseng_31": {"rotate": [{"angle": 2.24, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": -6.42, "curve": "stepped"}, {"time": 0.1, "angle": -6.42, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.1333, "angle": 2.24}]}, "shaseng_32": {"rotate": [{"angle": 10.17, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": -10.05, "curve": "stepped"}, {"time": 0.1, "angle": -10.05, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.1333, "angle": 10.17}]}, "shaseng_24": {"rotate": [{"angle": -1.59, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": 0.4, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1333, "angle": -1.59}]}, "shaseng_33": {"rotate": [{"angle": -7.82, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": 3.13, "curve": "stepped"}, {"time": 0.1, "angle": 3.13, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.1333, "angle": -7.82}]}, "shaseng_34": {"rotate": [{"angle": -26.92}]}, "shaseng_35": {"rotate": [{"angle": -17.76, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": -25.45, "curve": 0.358, "c2": 0.42, "c3": 0.756}, {"time": 0.1333, "angle": -17.76}]}, "shaseng_36": {"rotate": [{"angle": 0.16, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": -1.22, "curve": 0.334, "c2": 0.34, "c3": 0.758}, {"time": 0.1333, "angle": 0.16}]}, "shaseng_37": {"rotate": [{"angle": 2.26, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": -4.6, "curve": "stepped"}, {"time": 0.1, "angle": -4.6, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.1333, "angle": 2.26}]}, "shaseng_38": {"rotate": [{"angle": 10.39, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": -22.74, "curve": "stepped"}, {"time": 0.1, "angle": -22.74, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.1333, "angle": 10.39}]}, "shaseng_39": {"rotate": [{"angle": 12.82, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": 13.53, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 0.1333, "angle": 12.82}]}, "shaseng_40": {"rotate": [{"angle": -3.61}]}, "shaseng_41": {"rotate": [{"angle": -5.03, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": -6.54, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.1333, "angle": -5.03, "curve": "stepped"}, {"time": 0.2667, "angle": -5.03, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.3, "angle": 11.8, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.3333, "angle": -5.03}]}, "shaseng_42": {"rotate": [{"angle": -3.16, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": -12.76, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1333, "angle": -3.16}]}, "shaseng_43": {"rotate": [{"angle": 13.07, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": -14.3, "curve": "stepped"}, {"time": 0.1, "angle": -14.3, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.1333, "angle": 13.07}]}, "shaseng_0": {"rotate": [{"angle": 0.31, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": -43.43, "curve": 0.311, "c2": 0.45, "c3": 0.649, "c4": 0.81}, {"time": 0.1, "angle": -59.56, "curve": 0.329, "c2": 0.64, "c3": 0.663}, {"time": 0.1333, "angle": -43.43, "curve": 0.304, "c2": 0.59, "c3": 0.649}, {"time": 0.3333, "angle": 0.31}], "translate": [{"curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "x": 3.54, "y": 15.26, "curve": 0.715, "c3": 0.891, "c4": 0.4}, {"time": 0.1, "x": -1.99, "y": 30.72, "curve": 0.715, "c3": 0.891, "c4": 0.4}, {"time": 0.1333, "x": -9.88, "y": 48.67, "curve": 0, "c2": 0.16, "c3": 0.422, "c4": 0.68}, {"time": 0.3333}]}, "shaseng_44": {"rotate": [{"angle": 1.4, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": 7.09, "curve": 0.715, "c3": 0.891, "c4": 0.4}, {"time": 0.1, "angle": -11.35, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.1333, "angle": 1.4, "curve": "stepped"}, {"time": 0.2667, "angle": 1.4, "curve": 0, "c2": 0.19, "c3": 0.486}, {"time": 0.3, "angle": -9.46, "curve": 0.25, "c4": 0.5}, {"time": 0.3333, "angle": 1.4}]}, "shaseng_45": {"rotate": [{"angle": 3.88, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": 3.58, "curve": "stepped"}, {"time": 0.1, "angle": 3.58, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.1333, "angle": 3.88}]}, "shaseng_47": {"rotate": [{"angle": -0.28, "curve": 0, "c2": 0.34, "c3": 0.306, "c4": 0.67}, {"time": 0.0333, "angle": -20.72, "curve": 0.244, "c2": 0.55, "c3": 0.57, "c4": 0.91}, {"time": 0.1, "angle": 161.89, "curve": 0.244, "c2": 0.55, "c3": 0.57, "c4": 0.91}, {"time": 0.1333, "angle": -48.04, "curve": "stepped"}, {"time": 0.2667, "angle": -48.04, "curve": 0, "c2": 0.19, "c3": 0.486}, {"time": 0.3333, "angle": -0.28}], "translate": [{"curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "x": -1.87, "y": -21.64, "curve": "stepped"}, {"time": 0.1, "x": -1.87, "y": -21.64, "curve": 0.639, "c2": 0.15, "c3": 0.854, "c4": 0.52}, {"time": 0.1333}], "scale": [{"curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "x": 0.969, "y": 0.969, "curve": "stepped"}, {"time": 0.1, "x": 0.969, "y": 0.969, "curve": 0.639, "c2": 0.15, "c3": 0.854, "c4": 0.52}, {"time": 0.1333}]}, "shaseng_48": {"rotate": [{"angle": -1.21, "curve": 0, "c2": 0.34, "c3": 0.294, "c4": 0.68}, {"time": 0.0333, "angle": -8.48, "curve": 0.538, "c2": 0.24, "c3": 0.8, "c4": 0.59}, {"time": 0.1, "angle": -13.55, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.1333, "angle": 19.68, "curve": "stepped"}, {"time": 0.2667, "angle": 19.68, "curve": 0, "c2": 0.19, "c3": 0.486}, {"time": 0.3333, "angle": -1.21}], "translate": [{"curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "x": -0.3, "y": -4.14, "curve": "stepped"}, {"time": 0.1, "x": -0.3, "y": -4.14, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.1333}]}, "shaseng_49": {"rotate": [{"angle": -0.94, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": -27.53, "curve": "stepped"}, {"time": 0.1, "angle": -27.53, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.1333, "angle": -36.63, "curve": "stepped"}, {"time": 0.2667, "angle": -36.63, "curve": 0, "c2": 0.19, "c3": 0.486}, {"time": 0.3333, "angle": -0.94}]}, "shaseng_46": {"rotate": [{"time": 0.1, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.1333, "angle": -12.58, "curve": "stepped"}, {"time": 0.2667, "angle": -12.58, "curve": 0, "c2": 0.19, "c3": 0.486}, {"time": 0.3333}], "translate": [{"curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "x": 30.96, "y": -197.73, "curve": "stepped"}, {"time": 0.1, "x": 30.96, "y": -197.73, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.1333, "x": -6.5, "y": -205.08, "curve": "stepped"}, {"time": 0.2667, "x": -6.5, "y": -205.08, "curve": 0, "c2": 0.23, "c3": 0.352, "c4": 0.58}, {"time": 0.3333}], "scale": [{"curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "x": 0.946, "y": 0.946, "curve": "stepped"}, {"time": 0.1, "x": 0.946, "y": 0.946, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.1333}]}, "shaseng_55": {"rotate": [{"angle": -1.93, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": 11.42, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "angle": -1.93, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 8.46, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -1.93}]}, "shaseng_56": {"rotate": [{"angle": 5.76, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": 33.52, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "angle": 5.76, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 47.19, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 5.76}]}, "shaseng_59": {"rotate": [{"angle": -1.03, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": -17.39, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "angle": -1.03, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -16.27, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -1.03}]}, "shaseng_60": {"rotate": [{"angle": 1.88, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": 12.69, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "angle": 1.88, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 26.4, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 1.88}]}, "shaseng_63": {"rotate": [{"angle": 0.48, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": -9.17, "curve": "stepped"}, {"time": 0.1, "angle": -9.17, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.1333, "angle": 0.48}], "translate": [{"curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "x": 19.58, "y": -12.73, "curve": "stepped"}, {"time": 0.1, "x": 19.58, "y": -12.73, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.1333}]}, "shaseng_63b": {"rotate": [{"angle": -11.96, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": 0.69, "curve": "stepped"}, {"time": 0.1, "angle": 0.69, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.1333, "angle": -11.96}]}, "shaseng_63c": {"rotate": [{"angle": -5.86, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": -14.12, "curve": "stepped"}, {"time": 0.1, "angle": -14.12, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.1333, "angle": -5.86}]}, "shaseng_50": {"rotate": [{"angle": 4.94, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": 8.56, "curve": 0.358, "c2": 0.42, "c3": 0.756}, {"time": 0.1333, "angle": 4.94}]}, "shaseng_50b": {"rotate": [{"angle": 3.36, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": -5.23, "curve": 0.334, "c2": 0.34, "c3": 0.758}, {"time": 0.1333, "angle": 3.36}]}, "shaseng_50c": {"rotate": [{"angle": 18.6}]}, "shaseng_64": {"rotate": [{"angle": -2.95, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": 2.61, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1333, "angle": -2.95}]}, "shaseng_65": {"rotate": [{"angle": -15.45, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": -36.8, "curve": 0.289, "c2": 0.18, "c3": 0.645, "c4": 0.59}, {"time": 0.1333, "angle": -15.45, "curve": "stepped"}, {"time": 0.2667, "angle": -15.45, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3, "angle": -113.38, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "angle": -15.45}]}, "shaseng_66": {"rotate": [{"angle": -0.06, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": -0.6, "curve": 0.343, "c2": 0.37, "c3": 0.757}, {"time": 0.1333, "angle": -0.06, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.2667, "angle": -1.62, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.3333, "angle": -0.06}]}, "shaseng_67": {"rotate": [{"angle": 1.41, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": -2.73, "curve": "stepped"}, {"time": 0.1, "angle": -2.73, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.1333, "angle": 1.41, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.2667, "angle": -2.85, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.3333, "angle": 1.41}]}, "shaseng_68": {"rotate": [{"angle": 4.47, "curve": "stepped"}, {"time": 0.1333, "angle": 4.47, "curve": 0.271, "c2": 0.09, "c3": 0.753}, {"time": 0.2667, "angle": -5.12, "curve": 0.271, "c2": 0.09, "c3": 0.753}, {"time": 0.3333, "angle": 4.47}]}, "shaseng_69": {"rotate": [{"angle": 7.4, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": 10.01, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1333, "angle": 7.4, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.2667, "angle": 0.5, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "angle": 7.4}]}, "shaseng_70": {"rotate": [{"angle": -0.55, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": -0.67, "curve": 0.37, "c2": 0.48, "c3": 0.753}, {"time": 0.1333, "angle": -4.02, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2667, "angle": -2.49, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.3333, "angle": -0.55}], "translate": [{"curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "x": -5.2, "y": -1.32, "curve": 0.342, "c2": 0.36, "c3": 0.678, "c4": 0.7}, {"time": 0.1333, "x": -3.65, "y": -22.81, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.2667}]}, "shaseng_71": {"rotate": [{"angle": -0.36, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": -3.95, "curve": 0.343, "c2": 0.37, "c3": 0.757}, {"time": 0.1333, "angle": 10.79, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.2667, "angle": -5.87, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.3333, "angle": -0.36}]}, "shaseng_72": {"rotate": [{"angle": 2.08, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": -10.54, "curve": "stepped"}, {"time": 0.1, "angle": -10.54, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.1333, "angle": 15.84, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.2667, "angle": -8.95, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.3333, "angle": 2.08}]}, "shaseng_73": {"rotate": [{"angle": 11.12, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": -18.49, "curve": "stepped"}, {"time": 0.1, "angle": -18.49, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.1333, "angle": 26.38, "curve": 0.263, "c2": 0.08, "c3": 0.645, "c4": 0.59}, {"time": 0.2667, "angle": 3.6, "curve": 0.263, "c2": 0.08, "c3": 0.645, "c4": 0.59}, {"time": 0.3333, "angle": 11.12}]}, "shaseng_58": {"rotate": [{"curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": 7.38, "curve": 0.25, "c4": 0.5}, {"time": 0.1333, "angle": 11.6, "curve": 0, "c2": 0.19, "c3": 0.486}, {"time": 0.2667, "angle": 9.09, "curve": 0, "c2": 0.19, "c3": 0.486}, {"time": 0.3333}], "translate": [{"y": 1.63, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "x": 6.02, "y": 3.94, "curve": 0.25, "c4": 0.5}, {"time": 0.1333, "y": 1.63}], "scale": [{"curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "x": 0.975, "curve": 0.25, "c4": 0.5}, {"time": 0.1333}]}, "shaseng_12": {"rotate": [{"angle": -6.08}]}, "daoguang": {"scale": [{"curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "x": 0.73, "y": 0.73, "curve": "stepped"}, {"time": 0.1, "x": 0.73, "y": 0.73, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 1.58, "y": 0.855}, {"time": 0.3667}]}, "shaseng_77": {"rotate": [{"curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": -4.76}, {"time": 0.1, "angle": -8.75}, {"time": 0.1333, "angle": -11.56}, {"time": 0.2667}]}, "daoguang1": {"rotate": [{"curve": "stepped"}, {"time": 0.1, "angle": 138.54, "curve": "stepped"}, {"time": 0.1333}], "translate": [{"curve": "stepped"}, {"time": 0.1, "x": 508.84, "y": 246.34, "curve": "stepped"}, {"time": 0.1333}], "scale": [{"curve": "stepped"}, {"time": 0.1, "x": 0.813, "y": 0.367, "curve": "stepped"}, {"time": 0.1333}]}, "daoguang2": {"rotate": [{"curve": "stepped"}, {"time": 0.1, "angle": 138.54, "curve": "stepped"}, {"time": 0.1333, "angle": 8.93}], "translate": [{"curve": "stepped"}, {"time": 0.1, "x": 508.84, "y": 246.34, "curve": "stepped"}, {"time": 0.1333, "x": 26.77}], "scale": [{"curve": "stepped"}, {"time": 0.1, "x": 0.813, "y": 0.367, "curve": "stepped"}, {"time": 0.1333, "x": 0.809, "y": 0.768}]}, "d1": {"rotate": [{"time": 0.3667, "angle": 30.78}]}, "shaseng_78": {"rotate": [{"time": 0.1, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.1333, "angle": -12.58, "curve": "stepped"}, {"time": 0.2667, "angle": -12.58, "curve": 0, "c2": 0.19, "c3": 0.486}, {"time": 0.3333}], "translate": [{"curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "x": 30.96, "y": -197.73, "curve": "stepped"}, {"time": 0.1, "x": 30.96, "y": -197.73, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.1333, "x": -6.5, "y": -205.08, "curve": "stepped"}, {"time": 0.2667, "x": -6.5, "y": -205.08, "curve": 0, "c2": 0.23, "c3": 0.352, "c4": 0.58}, {"time": 0.3333}], "scale": [{"curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "x": 0.946, "y": 0.946, "curve": "stepped"}, {"time": 0.1, "x": 0.946, "y": 0.946, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.1333}]}}, "deform": {"default": {"shaseng_0": {"shaseng_0": [{"offset": 54, "vertices": [-0.03646, -0.34388, 0.10032, -0.33095, -0.07283, -0.68767, -0.07283, -0.68767, 0, 0, 0, 0, 0.20083, 2.08921, 0.12687, 1.00017, 0.12687, 1.00017, -0.12721, 1.02398, -0.12721, 1.02398, -0.71556, 2.38928, -1.80434, 1.71917], "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "offset": 66, "vertices": [-0.00989, -1.19092, 1.76117, -2.99219, -3.01178, -4.1427, 1.3988, -4.92688, -0.46216, -3.44116, 0.28113, -2.51038, 1.66516, -1.89938], "curve": "stepped"}, {"time": 0.1, "offset": 66, "vertices": [-0.00989, -1.19092, 1.76117, -2.99219, -3.01178, -4.1427, 1.3988, -4.92688, -0.46216, -3.44116, 0.28113, -2.51038, 1.66516, -1.89938], "curve": 0.715, "c3": 0.891, "c4": 0.4}, {"time": 0.1333, "offset": 54, "vertices": [-0.03646, -0.34388, 0.10032, -0.33095, -0.07283, -0.68767, -0.07283, -0.68767, 0, 0, 0, 0, 0.20083, 2.08921, 0.12687, 1.00017, 0.12687, 1.00017, -0.12721, 1.02398, -0.12721, 1.02398, -0.71556, 2.38928, -1.80434, 1.71917]}]}, "shaseng_9": {"shaseng_9": [{"curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "offset": 6, "vertices": [-2.38416, 3.77466, -10.33405, 3.17566, -10.71179, 8.79553, -9.44229, 13.69397, -9.2923, 7.49048, -9.80225, 11.12439, -9.80225, 11.12439, -9.80225, 11.12439, -9.80225, 11.12439, -8.74341, 7.24951, -2.38416, 3.77466, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.38416, 3.77466, -8.74341, 7.24951, -10.61719, 7.39038, 0, 0, 0, 0, -2.38416, 3.77466, -8.74341, 7.24951, -10.9389, 8.21362, 0, 0, 0, 0, -2.38416, 3.77466, -8.74341, 7.24951, -9.80225, 11.12439], "curve": 0.25, "c4": 0.5}, {"time": 0.1333}]}, "shaseng_12": {"shaseng_12": [{"offset": 38, "vertices": [-0.15173, 2.96886, 0.10692, 2.97074, -0.10624, 2.07813, 0.07481, 2.07948, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.13476, -0.00691, -0.32353, -0.01655, -0.31803, 0.10331, 0.33301, -1.76637, 0.50324, 1.49926, -0.17594, -0.90179, -0.25176, -0.85232, 0.41558, 0.02124, 0.42157, 0.07574, -0.25341, -0.01296, -0.24782, 0.10075, 0.29612, 0.01514, 0.30013, 0.04975, 0, 0, 0, 0, 0.29615, 0.01514, 0.30013, 0.04971], "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "offset": 38, "vertices": [-0.15173, 2.96886, 0.10692, 2.97074, -0.10624, 2.07813, 0.07481, 2.07948, 7.15027, -19.98091, 7.15027, -19.98091, 7.15027, -19.98091, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.13476, -0.00691, -0.32353, -0.01655, -0.31803, 0.10331, 0.33301, -1.76637, 4.07734, -8.49119, -0.17594, -0.90179, -0.25176, -0.85232, 0.41558, 0.02124, 0.42157, 0.07574, -0.25341, -0.01296, -0.24782, 0.10075, 0.29612, 0.01514, 0.30013, 0.04975, 0, 0, 0, 0, 0.29615, 0.01514, 0.30013, 0.04971], "curve": "stepped"}, {"time": 0.1, "offset": 38, "vertices": [-0.15173, 2.96886, 0.10692, 2.97074, -0.10624, 2.07813, 0.07481, 2.07948, 7.15027, -19.98091, 7.15027, -19.98091, 7.15027, -19.98091, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.13476, -0.00691, -0.32353, -0.01655, -0.31803, 0.10331, 0.33301, -1.76637, 4.07734, -8.49119, -0.17594, -0.90179, -0.25176, -0.85232, 0.41558, 0.02124, 0.42157, 0.07574, -0.25341, -0.01296, -0.24782, 0.10075, 0.29612, 0.01514, 0.30013, 0.04975, 0, 0, 0, 0, 0.29615, 0.01514, 0.30013, 0.04971], "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.1333, "offset": 38, "vertices": [-0.15173, 2.96886, 0.10692, 2.97074, -0.10624, 2.07813, 0.07481, 2.07948, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.13476, -0.00691, -0.32353, -0.01655, -0.31803, 0.10331, 0.33301, -1.76637, 0.50324, 1.49926, -0.17594, -0.90179, -0.25176, -0.85232, 0.41558, 0.02124, 0.42157, 0.07574, -0.25341, -0.01296, -0.24782, 0.10075, 0.29612, 0.01514, 0.30013, 0.04975, 0, 0, 0, 0, 0.29615, 0.01514, 0.30013, 0.04971]}]}, "shaseng_13": {"shaseng_13": [{"offset": 40, "vertices": [-83.11365, -48.45935, -72.98035, -9.20032, -67.24121, 4.88055, -59.9043, 32.10095], "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "offset": 40, "vertices": [-85.29834, -54.31567, -75.16504, -15.05664, -69.4259, -0.97577, -64.56152, 27.9325], "curve": "stepped"}, {"time": 0.1, "offset": 40, "vertices": [-85.29834, -54.31567, -75.16504, -15.05664, -69.4259, -0.97577, -64.56152, 27.9325], "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.1333, "offset": 40, "vertices": [-83.11365, -48.45935, -72.98035, -9.20032, -67.24121, 4.88055, -59.9043, 32.10095]}]}, "shaseng_16": {"shaseng_16": [{"offset": 110, "vertices": [0.46622, -2.23144, 1.5949, -1.62874, 0.46622, -2.23144, 1.5949, -1.62874, 0.46622, -2.23144], "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "offset": 110, "vertices": [0.05134, -0.24571, 0.17562, -0.17935, 0.05134, -0.24571, 0.17562, -0.17935, 0.05134, -0.24571], "curve": "stepped"}, {"time": 0.1, "offset": 110, "vertices": [0.05134, -0.24571, 0.17562, -0.17935, 0.05134, -0.24571, 0.17562, -0.17935, 0.05134, -0.24571], "curve": 0.639, "c2": 0.15, "c3": 0.854, "c4": 0.52}, {"time": 0.1333, "offset": 110, "vertices": [0.46622, -2.23144, 1.5949, -1.62874, 0.46622, -2.23144, 1.5949, -1.62874, 0.46622, -2.23144]}]}}}}, "2": {"slots": {"d1": {"attachment": [{"time": 0.1527, "name": "d1"}, {"time": 0.2042, "name": "d3"}, {"time": 0.2557, "name": "d5"}, {"time": 0.3072, "name": "d7"}, {"time": 0.3587, "name": null}]}, "daoguang": {"attachment": [{"name": null}, {"time": 0.0667, "name": "<PERSON><PERSON><PERSON>"}, {"time": 0.1667, "name": null}]}, "daoying1": {"attachment": [{"name": null}, {"time": 0.0667, "name": "daoying1"}, {"time": 0.1667, "name": null}]}}, "bones": {"body": {"translate": [{"y": -1, "curve": "stepped"}, {"time": 0.0333, "y": -1, "curve": 0.213, "c2": 0.34, "c3": 0.542, "c4": 0.67}, {"time": 0.0667, "x": 5.65, "y": 21.08, "curve": 0.257, "c2": 0.34, "c3": 0.588, "c4": 0.68}, {"time": 0.1, "x": 3.5, "y": 9.56, "curve": "stepped"}, {"time": 0.1333, "x": 3.5, "y": 9.56, "curve": 0.276, "c2": 0.35, "c3": 0.608, "c4": 0.68}, {"time": 0.1667, "x": 2.13, "y": 15.82, "curve": 0.158, "c2": 0.54, "c3": 0.462, "c4": 0.94}, {"time": 0.2667, "x": 2.13, "y": -19.58, "curve": 0.268, "c2": 0.36, "c3": 0.598, "c4": 0.7}, {"time": 0.3, "x": 1.07, "y": -10.32, "curve": "stepped"}, {"time": 0.3333, "x": 0.46, "y": -5.05, "curve": "stepped"}, {"time": 0.3667, "x": 0.13, "y": -2.14, "curve": "stepped"}, {"time": 0.4, "y": -1}]}, "shaseng_13": {"rotate": [{"angle": -2.5, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": -0.47, "curve": 0.334, "c2": 0.34, "c3": 0.758}, {"time": 0.1667, "angle": -16.33, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.3, "angle": -0.57, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.4, "angle": -2.5}]}, "shaseng_14": {"rotate": [{"angle": -5.57, "curve": "stepped"}, {"time": 0.0333, "angle": -5.57, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.1667, "angle": -26.37, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.3, "angle": -0.13, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.4, "angle": -5.57}]}, "shaseng_15": {"rotate": [{"angle": -14.31, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": -15.52, "curve": 0.37, "c2": 0.48, "c3": 0.753}, {"time": 0.2333, "angle": -48.02, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.4, "angle": -14.31}]}, "shaseng_16": {"rotate": [{"angle": -10.28, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": -18.64, "curve": 0.334, "c2": 0.34, "c3": 0.758}, {"time": 0.2, "angle": -18.05, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "angle": -1.2, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4, "angle": -10.28}]}, "shaseng_17": {"rotate": [{"angle": 2.32, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": 2.82, "curve": 0.358, "c2": 0.42, "c3": 0.756}, {"time": 0.1, "angle": -16.43, "curve": "stepped"}, {"time": 0.1333, "angle": -16.43, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.2667, "angle": 7.34, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.4, "angle": 2.32}]}, "shaseng_18": {"rotate": [{"angle": 0.61, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": -1.4, "curve": 0.334, "c2": 0.34, "c3": 0.758}, {"time": 0.1667, "angle": -9.06, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.3, "angle": 6.61, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.4, "angle": 0.61}]}, "shaseng_19": {"rotate": [{"angle": -6.09, "curve": "stepped"}, {"time": 0.0333, "angle": -6.09, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.2, "angle": -22.27, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.3333, "angle": 13.02, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.4, "angle": -6.09}]}, "shaseng_20": {"rotate": [{"angle": -9.23, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": -10.83, "curve": 0.37, "c2": 0.48, "c3": 0.753}, {"time": 0.2333, "angle": -42.75, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.4, "angle": -9.23}]}, "shaseng_22": {"rotate": [{"angle": 0.88, "curve": 0, "c2": 0.39, "c3": 0.246, "c4": 0.77}, {"time": 0.0333, "angle": 6.81, "curve": 0.244, "c2": 0.55, "c3": 0.57, "c4": 0.91}, {"time": 0.1, "angle": 2.08, "curve": "stepped"}, {"time": 0.1333, "angle": 2.08, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.1667, "angle": 17.89, "curve": 0, "c2": 0.19, "c3": 0.486}, {"time": 0.3, "angle": 15.71, "curve": "stepped"}, {"time": 0.3333, "angle": 7.16, "curve": "stepped"}, {"time": 0.3667, "angle": 2.47, "curve": "stepped"}, {"time": 0.4, "angle": 0.88}], "translate": [{"curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "x": -6.23, "y": 4.66, "curve": "stepped"}, {"time": 0.1333, "x": -6.23, "y": 4.66, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.1667, "y": -11.52, "curve": "stepped"}, {"time": 0.3, "y": -11.52, "curve": "stepped"}, {"time": 0.3333, "y": -4.88, "curve": "stepped"}, {"time": 0.3667, "y": -1.23, "curve": "stepped"}, {"time": 0.4}]}, "shaseng_23": {"rotate": [{"angle": 0.91, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": -26.29, "curve": "stepped"}, {"time": 0.1333, "angle": -26.29, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.1667, "angle": -3.81, "curve": 0, "c2": 0.19, "c3": 0.486}, {"time": 0.3, "angle": 13.91, "curve": "stepped"}, {"time": 0.3333, "angle": 6.42, "curve": "stepped"}, {"time": 0.3667, "angle": 2.31, "curve": "stepped"}, {"time": 0.4, "angle": 0.91}], "translate": [{"x": -1.1, "y": -0.05, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "x": 24.53, "y": -19.12, "curve": "stepped"}, {"time": 0.1333, "x": 24.53, "y": -19.12, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.1667, "x": 0.83, "y": -2.28, "curve": 0, "c2": 0.19, "c3": 0.486}, {"time": 0.3, "x": 4.64, "y": -1.26, "curve": "stepped"}, {"time": 0.3333, "x": 1.33, "y": -0.56, "curve": "stepped"}, {"time": 0.3667, "x": -0.48, "y": -0.18, "curve": "stepped"}, {"time": 0.4, "x": -1.1, "y": -0.05}]}, "shaseng_6": {"rotate": [{"angle": 0.4, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": -23.95, "curve": "stepped"}, {"time": 0.1333, "angle": -23.95, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.1667, "angle": 0.4}], "translate": [{"x": 0.11, "y": -0.01, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "x": 7.34, "y": -20.5, "curve": "stepped"}, {"time": 0.1333, "x": 7.34, "y": -20.5, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.1667, "x": 0.11, "y": -0.01}]}, "shaseng_3": {"rotate": [{"angle": -1.22, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": 0.94, "curve": "stepped"}, {"time": 0.1333, "angle": 0.94, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.1667, "angle": -1.22}]}, "shaseng_4": {"rotate": [{"angle": -3.22, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": 13.52, "curve": "stepped"}, {"time": 0.1333, "angle": 13.52, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.1667, "angle": -3.22}]}, "shaseng_5": {"rotate": [{"angle": -11.17}]}, "shaseng_7": {"rotate": [{"angle": 0.32, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": 1.44, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1667, "angle": 0.32}]}, "shaseng_8": {"rotate": [{"angle": -0.85, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": 13.81, "curve": "stepped"}, {"time": 0.1333, "angle": 13.81, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.1667, "angle": -0.85}]}, "shaseng_9": {"rotate": [{"angle": -19.64, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": 14.97, "curve": "stepped"}, {"time": 0.1333, "angle": 14.97, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.1667, "angle": -19.64}]}, "shaseng_10": {"rotate": [{"angle": -2.78, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": 2.8, "curve": "stepped"}, {"time": 0.1333, "angle": 2.8, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.1667, "angle": -2.78}]}, "shaseng_11": {"rotate": [{"angle": -7.55}]}, "shaseng_21": {"rotate": [{"angle": -6.53, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": -8.68, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.1667, "angle": -6.53, "curve": "stepped"}, {"time": 0.3, "angle": -6.53, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.3333, "angle": 3.84, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.4, "angle": -6.53}]}, "shaseng_1": {"rotate": [{"angle": 0.65, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": -3.22, "curve": 0.334, "c2": 0.34, "c3": 0.758}, {"time": 0.1667, "angle": 0.65}]}, "shaseng_2": {"rotate": [{"angle": 5.3}]}, "shaseng_25": {"rotate": [{"angle": -1.32, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": 5.1, "curve": "stepped"}, {"time": 0.1333, "angle": 5.1, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.1667, "angle": -1.32}]}, "shaseng_26": {"rotate": [{"angle": -5.94, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": -6.53, "curve": 0.37, "c2": 0.48, "c3": 0.753}, {"time": 0.1667, "angle": -5.94, "curve": "stepped"}, {"time": 0.3, "angle": -5.94, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.3333, "angle": 5.48, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 0.4, "angle": -5.94}]}, "shaseng_27": {"rotate": [{"angle": 1.22, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": 3.77, "curve": 0.334, "c2": 0.34, "c3": 0.758}, {"time": 0.1667, "angle": 1.22}]}, "shaseng_28": {"rotate": [{"angle": -5.72, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": 7.78, "curve": "stepped"}, {"time": 0.1333, "angle": 7.78, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.1667, "angle": -5.72}]}, "shaseng_29": {"rotate": [{"angle": -10.42, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": -10.9, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 0.1667, "angle": -10.42}]}, "shaseng_30": {"rotate": [{"angle": -1.53, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": -3.17, "curve": 0.358, "c2": 0.42, "c3": 0.756}, {"time": 0.1667, "angle": -1.53, "curve": "stepped"}, {"time": 0.3, "angle": -1.53, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.3333, "angle": 3.98, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.4, "angle": -1.53}]}, "shaseng_31": {"rotate": [{"angle": 2.24, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": -6.42, "curve": "stepped"}, {"time": 0.1333, "angle": -6.42, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.1667, "angle": 2.24}]}, "shaseng_32": {"rotate": [{"angle": 10.17, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": -10.05, "curve": "stepped"}, {"time": 0.1333, "angle": -10.05, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.1667, "angle": 10.17}]}, "shaseng_24": {"rotate": [{"angle": -1.59, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": 0.4, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1667, "angle": -1.59}]}, "shaseng_33": {"rotate": [{"angle": -7.82, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": 3.13, "curve": "stepped"}, {"time": 0.1333, "angle": 3.13, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.1667, "angle": -7.82}]}, "shaseng_34": {"rotate": [{"angle": -26.92}]}, "shaseng_35": {"rotate": [{"angle": -17.76, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": -25.45, "curve": 0.358, "c2": 0.42, "c3": 0.756}, {"time": 0.1667, "angle": -17.76}]}, "shaseng_36": {"rotate": [{"angle": 0.16, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": -1.22, "curve": 0.334, "c2": 0.34, "c3": 0.758}, {"time": 0.1667, "angle": 0.16}]}, "shaseng_37": {"rotate": [{"angle": 2.26, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": -4.6, "curve": "stepped"}, {"time": 0.1333, "angle": -4.6, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.1667, "angle": 2.26}]}, "shaseng_38": {"rotate": [{"angle": 10.39, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": -22.74, "curve": "stepped"}, {"time": 0.1333, "angle": -22.74, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.1667, "angle": 10.39}]}, "shaseng_39": {"rotate": [{"angle": 12.82, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": 13.53, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 0.1667, "angle": 12.82}]}, "shaseng_40": {"rotate": [{"angle": -3.61}]}, "shaseng_41": {"rotate": [{"angle": -5.03, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": -6.54, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.1667, "angle": -5.03, "curve": "stepped"}, {"time": 0.3, "angle": -5.03, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.3333, "angle": 11.8, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.4, "angle": -5.03}]}, "shaseng_42": {"rotate": [{"angle": -3.16, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": -12.76, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1667, "angle": -3.16}]}, "shaseng_43": {"rotate": [{"angle": 13.07, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": -14.3, "curve": "stepped"}, {"time": 0.1333, "angle": -14.3, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.1667, "angle": 13.07}]}, "shaseng_0": {"rotate": [{"angle": 0.31, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": -43.43, "curve": 0.311, "c2": 0.45, "c3": 0.649, "c4": 0.81}, {"time": 0.1, "angle": -59.56, "curve": "stepped"}, {"time": 0.1333, "angle": -59.56, "curve": 0.329, "c2": 0.64, "c3": 0.663}, {"time": 0.1667, "angle": 6.56, "curve": 0.304, "c2": 0.59, "c3": 0.649}, {"time": 0.4, "angle": 0.31}], "translate": [{"curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "x": 3.54, "y": 15.26, "curve": 0.715, "c3": 0.891, "c4": 0.4}, {"time": 0.1, "x": -1.99, "y": 30.72, "curve": "stepped"}, {"time": 0.1333, "x": -1.99, "y": 30.72, "curve": 0.715, "c3": 0.891, "c4": 0.4}, {"time": 0.1667, "x": 34.18, "y": 16.37, "curve": 0, "c2": 0.16, "c3": 0.422, "c4": 0.68}, {"time": 0.4}]}, "shaseng_44": {"rotate": [{"angle": 1.4, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": 7.09, "curve": 0.715, "c3": 0.891, "c4": 0.4}, {"time": 0.1, "angle": -11.35, "curve": "stepped"}, {"time": 0.1333, "angle": -11.35, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.1667, "angle": 1.4, "curve": "stepped"}, {"time": 0.3, "angle": 1.4, "curve": 0, "c2": 0.19, "c3": 0.486}, {"time": 0.3333, "angle": -9.46, "curve": 0.25, "c4": 0.5}, {"time": 0.4, "angle": 1.4}]}, "shaseng_45": {"rotate": [{"angle": 3.88, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": 3.58, "curve": "stepped"}, {"time": 0.1333, "angle": 3.58, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.1667, "angle": 3.88}]}, "shaseng_47": {"rotate": [{"angle": -0.28, "curve": 0, "c2": 0.34, "c3": 0.306, "c4": 0.67}, {"time": 0.0333, "angle": -20.72, "curve": 0.244, "c2": 0.55, "c3": 0.57, "c4": 0.91}, {"time": 0.1, "angle": 159.61, "curve": "stepped"}, {"time": 0.1333, "angle": 159.61, "curve": "stepped"}, {"time": 0.1667, "angle": 33.51, "curve": "stepped"}, {"time": 0.3333, "angle": 33.51, "curve": "stepped"}, {"time": 0.3667, "angle": -9.68, "curve": "stepped"}, {"time": 0.4, "angle": -0.28}], "translate": [{"curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "x": -1.87, "y": -21.64, "curve": "stepped"}, {"time": 0.1333, "x": -1.87, "y": -21.64, "curve": "stepped"}, {"time": 0.1667, "x": -13.92, "y": 38.98, "curve": "stepped"}, {"time": 0.3333, "x": -13.92, "y": 38.98, "curve": "stepped"}, {"time": 0.3667, "x": -5.21, "y": 14.6, "curve": "stepped"}, {"time": 0.4}], "scale": [{"curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "x": 0.969, "y": 0.969, "curve": "stepped"}, {"time": 0.1333, "x": 0.969, "y": 0.969, "curve": "stepped"}, {"time": 0.1667}]}, "shaseng_48": {"rotate": [{"angle": -1.21, "curve": 0, "c2": 0.34, "c3": 0.294, "c4": 0.68}, {"time": 0.0333, "angle": -8.48, "curve": 0.429, "c2": 0.28, "c3": 0.748, "c4": 0.61}, {"time": 0.0667, "angle": -34.4, "curve": 0.423, "c2": 0.3, "c3": 0.732, "c4": 0.63}, {"time": 0.1, "angle": -0.97, "curve": "stepped"}, {"time": 0.1333, "angle": -0.97, "curve": "stepped"}, {"time": 0.1667, "angle": 90.02, "curve": 0, "c2": 0.2, "c3": 0.362, "c4": 0.57}, {"time": 0.2, "angle": 105.06, "curve": 0, "c2": 0.22, "c3": 0.354, "c4": 0.57}, {"time": 0.3, "angle": 117.32, "curve": "stepped"}, {"time": 0.3333, "angle": 101.32, "curve": "stepped"}, {"time": 0.3667, "angle": -1.79, "curve": "stepped"}, {"time": 0.4, "angle": -1.21}], "translate": [{"curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "x": -0.3, "y": -4.14, "curve": "stepped"}, {"time": 0.1333, "x": -0.3, "y": -4.14, "curve": "stepped"}, {"time": 0.1667}]}, "shaseng_49": {"rotate": [{"angle": -0.94, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": -27.53, "curve": "stepped"}, {"time": 0.1333, "angle": -27.53, "curve": "stepped"}, {"time": 0.1667, "angle": -74.38, "curve": "stepped"}, {"time": 0.3, "angle": -74.38, "curve": "stepped"}, {"time": 0.3333, "angle": -71.56, "curve": "stepped"}, {"time": 0.3667, "angle": -50.14, "curve": "stepped"}, {"time": 0.4, "angle": -0.94}], "translate": [{"time": 0.1333, "curve": "stepped"}, {"time": 0.1667, "x": 16.4, "y": 9.73, "curve": "stepped"}, {"time": 0.3, "x": 16.4, "y": 9.73, "curve": "stepped"}, {"time": 0.3333, "x": 6.56, "y": 3.89, "curve": "stepped"}, {"time": 0.3667, "x": 1.7, "y": 1.01, "curve": "stepped"}, {"time": 0.4}]}, "shaseng_46": {"rotate": [{"time": 0.0333, "curve": 0.492, "c3": 0.798, "c4": 0.36}, {"time": 0.0667, "angle": -8.22, "curve": 0.54, "c2": 0.24, "c3": 0.801, "c4": 0.59}, {"time": 0.1, "angle": -12.67, "curve": "stepped"}, {"time": 0.1333, "angle": -12.67, "curve": "stepped"}, {"time": 0.1667, "angle": 84.48, "curve": "stepped"}, {"time": 0.3, "angle": 84.48, "curve": "stepped"}, {"time": 0.3333, "angle": 101.67, "curve": "stepped"}, {"time": 0.3667, "angle": 81.44, "curve": "stepped"}, {"time": 0.4}], "translate": [{"curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "x": 30.96, "y": -197.73, "curve": 0.17, "c2": 0.27, "c3": 0.533, "c4": 0.67}, {"time": 0.0667, "x": 32.94, "y": -186, "curve": 0.288, "c2": 0.56, "c3": 0.64}, {"time": 0.1, "x": 33.59, "y": -182.17, "curve": "stepped"}, {"time": 0.1333, "x": 33.59, "y": -182.17, "curve": "stepped"}, {"time": 0.1667, "x": 117.29, "y": 37.86, "curve": "stepped"}, {"time": 0.3, "x": 117.29, "y": 37.86, "curve": "stepped"}, {"time": 0.3333, "x": 114.36, "y": 33.07, "curve": "stepped"}, {"time": 0.3667, "x": -18.06, "y": -8.67, "curve": "stepped"}, {"time": 0.4}], "scale": [{"curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "x": 0.946, "y": 0.946, "curve": "stepped"}, {"time": 0.1333, "x": 0.946, "y": 0.946, "curve": "stepped"}, {"time": 0.1667}], "shear": [{"time": 0.0333, "curve": 0.278, "c2": 0.43, "c3": 0.627, "c4": 0.83}, {"time": 0.1333, "x": 16.58, "curve": "stepped"}, {"time": 0.1667, "x": 17.84, "curve": "stepped"}, {"time": 0.3, "x": 17.84, "curve": "stepped"}, {"time": 0.3333, "x": 4.13, "curve": "stepped"}, {"time": 0.3667, "x": 2.06, "curve": "stepped"}, {"time": 0.4}]}, "shaseng_55": {"rotate": [{"angle": -1.93, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": 11.42, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "angle": -1.93, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 8.46, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -1.93}]}, "shaseng_56": {"rotate": [{"angle": 5.76, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": 33.52, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "angle": 5.76, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 47.19, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 5.76}]}, "shaseng_59": {"rotate": [{"angle": -1.03, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": -17.39, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "angle": -1.03, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -16.27, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -1.03}]}, "shaseng_60": {"rotate": [{"angle": 1.88, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": 12.69, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "angle": 1.88, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 26.4, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 1.88}]}, "shaseng_63": {"rotate": [{"angle": 0.48, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": -9.17, "curve": "stepped"}, {"time": 0.1333, "angle": -9.17, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.1667, "angle": 0.48}], "translate": [{"curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "x": 19.58, "y": -12.73, "curve": "stepped"}, {"time": 0.1333, "x": 19.58, "y": -12.73, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.1667}]}, "shaseng_63b": {"rotate": [{"angle": -11.96, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": 0.69, "curve": "stepped"}, {"time": 0.1333, "angle": 0.69, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.1667, "angle": -11.96}]}, "shaseng_63c": {"rotate": [{"angle": -5.86, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": -14.12, "curve": "stepped"}, {"time": 0.1333, "angle": -14.12, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.1667, "angle": -5.86}]}, "shaseng_50": {"rotate": [{"angle": 4.94, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": 8.56, "curve": 0.358, "c2": 0.42, "c3": 0.756}, {"time": 0.1667, "angle": 4.94}]}, "shaseng_50b": {"rotate": [{"angle": 3.36, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": -5.23, "curve": 0.334, "c2": 0.34, "c3": 0.758}, {"time": 0.1667, "angle": 3.36}]}, "shaseng_50c": {"rotate": [{"angle": 18.6}]}, "shaseng_64": {"rotate": [{"angle": -2.95, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": 2.61, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1667, "angle": -2.95}]}, "shaseng_65": {"rotate": [{"angle": -15.45, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": -36.8, "curve": 0.289, "c2": 0.18, "c3": 0.645, "c4": 0.59}, {"time": 0.1667, "angle": -15.45, "curve": "stepped"}, {"time": 0.3, "angle": -15.45, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "angle": -113.38, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4, "angle": -15.45}]}, "shaseng_66": {"rotate": [{"angle": -0.06, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": -0.6, "curve": 0.343, "c2": 0.37, "c3": 0.757}, {"time": 0.1667, "angle": -0.06, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.3, "angle": -1.62, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.4, "angle": -0.06}]}, "shaseng_67": {"rotate": [{"angle": 1.41, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": -2.73, "curve": "stepped"}, {"time": 0.1333, "angle": -2.73, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.1667, "angle": 1.41, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.3, "angle": -2.85, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.4, "angle": 1.41}]}, "shaseng_68": {"rotate": [{"angle": 4.47, "curve": "stepped"}, {"time": 0.1667, "angle": 4.47, "curve": 0.271, "c2": 0.09, "c3": 0.753}, {"time": 0.3, "angle": -5.12, "curve": 0.271, "c2": 0.09, "c3": 0.753}, {"time": 0.4, "angle": 4.47}]}, "shaseng_69": {"rotate": [{"angle": 7.4, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": 10.01, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1667, "angle": 7.4, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3, "angle": 0.5, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.4, "angle": 7.4}]}, "shaseng_70": {"rotate": [{"angle": -0.55, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": -0.67, "curve": 0.37, "c2": 0.48, "c3": 0.753}, {"time": 0.1667, "angle": -4.02, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.3, "angle": -2.49, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.4, "angle": -0.55}], "translate": [{"curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "x": -5.2, "y": -1.32, "curve": 0.342, "c2": 0.36, "c3": 0.678, "c4": 0.7}, {"time": 0.1667, "x": -3.65, "y": -22.81, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3}]}, "shaseng_71": {"rotate": [{"angle": -0.36, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": -3.95, "curve": 0.343, "c2": 0.37, "c3": 0.757}, {"time": 0.1667, "angle": 10.79, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.3, "angle": -5.87, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.4, "angle": -0.36}]}, "shaseng_72": {"rotate": [{"angle": 2.08, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": -10.54, "curve": "stepped"}, {"time": 0.1333, "angle": -10.54, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.1667, "angle": 15.84, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.3, "angle": -8.95, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.4, "angle": 2.08}]}, "shaseng_73": {"rotate": [{"angle": 11.12, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": -18.49, "curve": "stepped"}, {"time": 0.1333, "angle": -18.49, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.1667, "angle": 26.38, "curve": 0.263, "c2": 0.08, "c3": 0.645, "c4": 0.59}, {"time": 0.3, "angle": 3.6, "curve": 0.263, "c2": 0.08, "c3": 0.645, "c4": 0.59}, {"time": 0.4, "angle": 11.12}]}, "shaseng_58": {"rotate": [{"curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": 7.38, "curve": 0.25, "c4": 0.5}, {"time": 0.1667, "angle": 11.6, "curve": 0, "c2": 0.19, "c3": 0.486}, {"time": 0.3, "angle": 9.09, "curve": 0, "c2": 0.19, "c3": 0.486}, {"time": 0.4}], "translate": [{"y": 1.63, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "x": 6.02, "y": 3.94, "curve": 0.25, "c4": 0.5}, {"time": 0.1667, "y": 1.63}], "scale": [{"curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "x": 0.975, "curve": 0.25, "c4": 0.5}, {"time": 0.1667}]}, "shaseng_12": {"rotate": [{"angle": -6.08}]}, "daoguang": {"scale": [{"curve": "stepped"}, {"time": 0.0667, "x": 0.919, "y": 0.919, "curve": 0.279, "c2": 0.37, "c3": 0.618, "c4": 0.7}, {"time": 0.1333, "x": 1.093, "y": 1.093, "curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 1.58, "y": 0.855}]}, "shaseng_77": {"rotate": [{"curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": -4.76}, {"time": 0.1, "angle": -8.75, "curve": "stepped"}, {"time": 0.1333, "angle": -8.75}, {"time": 0.1667, "angle": -11.56}, {"time": 0.3}]}, "daoguang1": {"rotate": [{"curve": "stepped"}, {"time": 0.1, "angle": 138.54, "curve": "stepped"}, {"time": 0.1333, "angle": 138.54, "curve": "stepped"}, {"time": 0.1667}], "translate": [{"curve": "stepped"}, {"time": 0.1, "x": 508.84, "y": 246.34, "curve": "stepped"}, {"time": 0.1333, "x": 508.84, "y": 246.34, "curve": "stepped"}, {"time": 0.1667}], "scale": [{"curve": "stepped"}, {"time": 0.1, "x": 0.813, "y": 0.367, "curve": "stepped"}, {"time": 0.1333, "x": 0.813, "y": 0.367, "curve": "stepped"}, {"time": 0.1667}]}, "daoguang2": {"rotate": [{"curve": "stepped"}, {"time": 0.1, "angle": 138.54, "curve": "stepped"}, {"time": 0.1333, "angle": 138.54, "curve": "stepped"}, {"time": 0.1667}], "translate": [{"curve": "stepped"}, {"time": 0.1, "x": 508.84, "y": 246.34, "curve": "stepped"}, {"time": 0.1333, "x": 508.84, "y": 246.34, "curve": "stepped"}, {"time": 0.1667}], "scale": [{"curve": "stepped"}, {"time": 0.1, "x": 0.813, "y": 0.367, "curve": "stepped"}, {"time": 0.1333, "x": 0.813, "y": 0.367, "curve": "stepped"}, {"time": 0.1667}]}, "d1": {"rotate": [{"angle": 30.78}], "translate": [{"time": 0.1527}, {"time": 0.3587, "y": 8.87}]}, "shaseng_78": {"rotate": [{"time": 0.0333, "curve": 0.492, "c3": 0.798, "c4": 0.36}, {"time": 0.0667, "angle": -8.22, "curve": 0.54, "c2": 0.24, "c3": 0.801, "c4": 0.59}, {"time": 0.1, "angle": -12.67, "curve": "stepped"}, {"time": 0.1333, "angle": -12.67, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.1667, "angle": 84.48, "curve": "stepped"}, {"time": 0.3, "angle": 84.48, "curve": "stepped"}, {"time": 0.3333, "angle": 101.67, "curve": "stepped"}, {"time": 0.3667, "angle": 95.62, "curve": "stepped"}, {"time": 0.4}], "translate": [{"curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "x": 30.96, "y": -197.73, "curve": 0.17, "c2": 0.27, "c3": 0.533, "c4": 0.67}, {"time": 0.0667, "x": 32.94, "y": -186, "curve": 0.288, "c2": 0.56, "c3": 0.64}, {"time": 0.1, "x": 33.59, "y": -182.17, "curve": "stepped"}, {"time": 0.1333, "x": 33.59, "y": -182.17, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.1667, "x": 117.29, "y": 37.86, "curve": "stepped"}, {"time": 0.3, "x": 117.29, "y": 37.86, "curve": "stepped"}, {"time": 0.3333, "x": 114.36, "y": 33.07, "curve": "stepped"}, {"time": 0.3667, "x": 20.22, "y": 6.21, "curve": "stepped"}, {"time": 0.4}], "scale": [{"curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "x": 0.946, "y": 0.946, "curve": "stepped"}, {"time": 0.1333, "x": 0.946, "y": 0.946, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.1667}], "shear": [{"time": 0.0333, "curve": 0.268, "c2": 0.53, "c3": 0.629}, {"time": 0.1667, "x": 17.84, "curve": "stepped"}, {"time": 0.3, "x": 17.84, "curve": "stepped"}, {"time": 0.3333, "x": 4.13, "curve": "stepped"}, {"time": 0.3667, "x": 2.06}, {"time": 0.4}]}}, "deform": {"default": {"shaseng_0": {"shaseng_0": [{"offset": 54, "vertices": [-0.03646, -0.34388, 0.10032, -0.33095, -0.07283, -0.68767, -0.07283, -0.68767, 0, 0, 0, 0, 0.20083, 2.08921, 0.12687, 1.00017, 0.12687, 1.00017, -0.12721, 1.02398, -0.12721, 1.02398, -0.71556, 2.38928, -1.80434, 1.71917], "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "offset": 66, "vertices": [-0.00989, -1.19092, 1.76117, -2.99219, -3.01178, -4.1427, 1.3988, -4.92688, -0.46216, -3.44116, 0.28113, -2.51038, 1.66516, -1.89938], "curve": "stepped"}, {"time": 0.1333, "offset": 66, "vertices": [-0.00989, -1.19092, 1.76117, -2.99219, -3.01178, -4.1427, 1.3988, -4.92688, -0.46216, -3.44116, 0.28113, -2.51038, 1.66516, -1.89938], "curve": 0.715, "c3": 0.891, "c4": 0.4}, {"time": 0.1667, "offset": 54, "vertices": [-0.03646, -0.34388, 0.10032, -0.33095, -0.07283, -0.68767, -0.07283, -0.68767, 0, 0, 0, 0, 0.20083, 2.08921, 0.12687, 1.00017, 0.12687, 1.00017, -0.12721, 1.02398, -0.12721, 1.02398, -0.71556, 2.38928, -1.80434, 1.71917]}]}, "shaseng_9": {"shaseng_9": [{"curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "offset": 6, "vertices": [-2.38416, 3.77466, -10.33405, 3.17566, -10.71179, 8.79553, -9.44229, 13.69397, -9.2923, 7.49048, -9.80225, 11.12439, -9.80225, 11.12439, -9.80225, 11.12439, -9.80225, 11.12439, -8.74341, 7.24951, -2.38416, 3.77466, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.38416, 3.77466, -8.74341, 7.24951, -10.61719, 7.39038, 0, 0, 0, 0, -2.38416, 3.77466, -8.74341, 7.24951, -10.9389, 8.21362, 0, 0, 0, 0, -2.38416, 3.77466, -8.74341, 7.24951, -9.80225, 11.12439], "curve": 0.25, "c4": 0.5}, {"time": 0.1667}]}, "shaseng_12": {"shaseng_12": [{"offset": 38, "vertices": [-0.15173, 2.96886, 0.10692, 2.97074, -0.10624, 2.07813, 0.07481, 2.07948, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.13476, -0.00691, -0.32353, -0.01655, -0.31803, 0.10331, 0.33301, -1.76637, 0.50324, 1.49926, -0.17594, -0.90179, -0.25176, -0.85232, 0.41558, 0.02124, 0.42157, 0.07574, -0.25341, -0.01296, -0.24782, 0.10075, 0.29612, 0.01514, 0.30013, 0.04975, 0, 0, 0, 0, 0.29615, 0.01514, 0.30013, 0.04971], "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "offset": 38, "vertices": [-0.15173, 2.96886, 0.10692, 2.97074, -0.10624, 2.07813, 0.07481, 2.07948, 7.15027, -19.98091, 7.15027, -19.98091, 7.15027, -19.98091, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.13476, -0.00691, -0.32353, -0.01655, -0.31803, 0.10331, 0.33301, -1.76637, 4.07734, -8.49119, -0.17594, -0.90179, -0.25176, -0.85232, 0.41558, 0.02124, 0.42157, 0.07574, -0.25341, -0.01296, -0.24782, 0.10075, 0.29612, 0.01514, 0.30013, 0.04975, 0, 0, 0, 0, 0.29615, 0.01514, 0.30013, 0.04971], "curve": "stepped"}, {"time": 0.1333, "offset": 38, "vertices": [-0.15173, 2.96886, 0.10692, 2.97074, -0.10624, 2.07813, 0.07481, 2.07948, 7.15027, -19.98091, 7.15027, -19.98091, 7.15027, -19.98091, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.13476, -0.00691, -0.32353, -0.01655, -0.31803, 0.10331, 0.33301, -1.76637, 4.07734, -8.49119, -0.17594, -0.90179, -0.25176, -0.85232, 0.41558, 0.02124, 0.42157, 0.07574, -0.25341, -0.01296, -0.24782, 0.10075, 0.29612, 0.01514, 0.30013, 0.04975, 0, 0, 0, 0, 0.29615, 0.01514, 0.30013, 0.04971], "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.1667, "offset": 38, "vertices": [-0.15173, 2.96886, 0.10692, 2.97074, -0.10624, 2.07813, 0.07481, 2.07948, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.13476, -0.00691, -0.32353, -0.01655, -0.31803, 0.10331, 0.33301, -1.76637, 0.50324, 1.49926, -0.17594, -0.90179, -0.25176, -0.85232, 0.41558, 0.02124, 0.42157, 0.07574, -0.25341, -0.01296, -0.24782, 0.10075, 0.29612, 0.01514, 0.30013, 0.04975, 0, 0, 0, 0, 0.29615, 0.01514, 0.30013, 0.04971]}]}, "shaseng_13": {"shaseng_13": [{"offset": 40, "vertices": [-83.11365, -48.45935, -72.98035, -9.20032, -67.24121, 4.88055, -59.9043, 32.10095], "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "offset": 40, "vertices": [-85.29834, -54.31567, -75.16504, -15.05664, -69.4259, -0.97577, -64.56152, 27.9325], "curve": "stepped"}, {"time": 0.1333, "offset": 40, "vertices": [-85.29834, -54.31567, -75.16504, -15.05664, -69.4259, -0.97577, -64.56152, 27.9325], "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.1667, "offset": 40, "vertices": [-83.11365, -48.45935, -72.98035, -9.20032, -67.24121, 4.88055, -59.9043, 32.10095]}]}, "shaseng_16": {"shaseng_16": [{"offset": 110, "vertices": [0.46622, -2.23144, 1.5949, -1.62874, 0.46622, -2.23144, 1.5949, -1.62874, 0.46622, -2.23144], "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "offset": 110, "vertices": [0.05134, -0.24571, 0.17562, -0.17935, 0.05134, -0.24571, 0.17562, -0.17935, 0.05134, -0.24571], "curve": "stepped"}, {"time": 0.1333, "offset": 110, "vertices": [0.05134, -0.24571, 0.17562, -0.17935, 0.05134, -0.24571, 0.17562, -0.17935, 0.05134, -0.24571], "curve": "stepped"}, {"time": 0.1667, "offset": 2, "vertices": [-4.31296, 6.79123, 5.97882, 5.38338, 2.42563, 30.9559, 30.92819, 2.76205, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.46622, -2.23144, 1.5949, -1.62874, 0.46622, -2.23144, 1.5949, -1.62874, 0.46622, -2.23144], "curve": "stepped"}, {"time": 0.3333, "offset": 2, "vertices": [-4.31296, 6.79123, 5.97882, 5.38338, 2.42563, 30.9559, 30.92819, 2.76205, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.46622, -2.23144, 1.5949, -1.62874, 0.46622, -2.23144, 1.5949, -1.62874, 0.46622, -2.23144], "curve": "stepped"}, {"time": 0.3667, "offset": 2, "vertices": [-0.99745, 1.5706, 1.38271, 1.24501, 0.56097, 7.15914, 7.15273, 0.63878, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.46622, -2.23144, 1.5949, -1.62874, 0.46622, -2.23144, 1.5949, -1.62874, 0.46622, -2.23144], "curve": "stepped"}, {"time": 0.4, "offset": 110, "vertices": [0.46622, -2.23144, 1.5949, -1.62874, 0.46622, -2.23144, 1.5949, -1.62874, 0.46622, -2.23144]}]}}}, "drawOrder": [{"time": 0.1667, "offsets": [{"slot": "shaseng_19", "offset": 18}, {"slot": "shaseng_18", "offset": 17}, {"slot": "shaseng_17", "offset": 17}, {"slot": "shaseng_16", "offset": 17}]}, {"time": 0.3667}]}, "3": {"slots": {"daoguang": {"color": [{"time": 0.2667, "color": "ffffffff", "curve": 0.458, "c2": 0.02, "c4": 0.27}, {"time": 0.4667, "color": "ffffff00"}], "attachment": [{"name": null}, {"time": 0.1667, "name": "<PERSON><PERSON><PERSON>"}, {"time": 0.2667, "name": null}]}, "daoguang3": {"attachment": [{"time": 0.2667, "name": "1"}, {"time": 0.3, "name": "3"}, {"time": 0.3333, "name": "5"}, {"time": 0.3667, "name": null}]}, "daoying1": {"color": [{"time": 0.4333, "color": "ffffffff"}, {"time": 0.4667, "color": "ffffff00"}], "attachment": [{"name": null}, {"time": 0.1667, "name": "daoying1"}, {"time": 0.2667, "name": null}]}, "diguang_0": {"attachment": [{"time": 0.3, "name": "diguang_2"}, {"time": 0.4, "name": "diguang_4"}, {"time": 0.4667, "name": null}]}}, "bones": {"body": {"rotate": [{"curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": 2.06, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1667, "angle": 1.37, "curve": 0.25, "c4": 0.5}, {"time": 0.2333, "angle": -8.56, "curve": "stepped"}, {"time": 0.2667}], "translate": [{"y": -1, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "y": -5.71, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1667, "x": 27.82, "y": 284.78, "curve": 0.25, "c4": 0.5}, {"time": 0.2333, "x": 17.44, "y": 241.94, "curve": "stepped"}, {"time": 0.2667, "x": -38.72, "y": -67.92, "curve": 0, "c2": 0.19, "c3": 0.486}, {"time": 0.4, "x": -13.72, "y": -67.92, "curve": 0, "c2": 0.19, "c3": 0.486}, {"time": 0.4667, "y": -1}]}, "shaseng_13": {"rotate": [{"angle": -2.5, "curve": "stepped"}, {"time": 0.0333, "angle": -2.5, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1667, "angle": -6.6, "curve": 0.334, "c2": 0.34, "c3": 0.758}, {"time": 0.2667, "angle": -16.33, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.4, "angle": -0.57, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.4667, "angle": -2.5}]}, "shaseng_14": {"rotate": [{"angle": -5.57, "curve": "stepped"}, {"time": 0.0333, "angle": -5.57, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1667, "angle": -11.71, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.2667, "angle": -26.37, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.4, "angle": -0.13, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.4667, "angle": -5.57}]}, "shaseng_15": {"rotate": [{"angle": -14.31, "curve": "stepped"}, {"time": 0.0333, "angle": -14.31, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1667, "angle": -21.66, "curve": 0.37, "c2": 0.48, "c3": 0.753}, {"time": 0.2667, "angle": -48.02, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.4, "angle": -6.64, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.4667, "angle": -14.31}]}, "shaseng_16": {"rotate": [{"angle": -10.28, "curve": "stepped"}, {"time": 0.0333, "angle": -10.28, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1667, "angle": 6.32, "curve": 0.334, "c2": 0.34, "c3": 0.758}, {"time": 0.2667, "angle": -18.05, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4, "angle": -1.2, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4667, "angle": -10.28}]}, "shaseng_17": {"rotate": [{"angle": 2.32, "curve": "stepped"}, {"time": 0.0333, "angle": 2.32, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1667, "angle": -3.32, "curve": 0.358, "c2": 0.42, "c3": 0.756}, {"time": 0.2667, "angle": -16.43, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.4, "angle": 7.34, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.4667, "angle": 2.32}]}, "shaseng_18": {"rotate": [{"angle": 0.61, "curve": "stepped"}, {"time": 0.0333, "angle": 0.61, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1667, "angle": 4.71, "curve": 0.334, "c2": 0.34, "c3": 0.758}, {"time": 0.2667, "angle": -9.06, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.4, "angle": 6.61, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.4667, "angle": 0.61}]}, "shaseng_19": {"rotate": [{"angle": -6.09, "curve": "stepped"}, {"time": 0.0333, "angle": -6.09, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1667, "angle": -6.12, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.2667, "angle": -22.27, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.4, "angle": 13.02, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.4667, "angle": -6.09}]}, "shaseng_20": {"rotate": [{"angle": -9.23, "curve": "stepped"}, {"time": 0.0333, "angle": -9.23, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1667, "angle": -1.41, "curve": 0.37, "c2": 0.48, "c3": 0.753}, {"time": 0.2667, "angle": -42.75, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.4, "angle": -3.25, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.4667, "angle": -9.23}]}, "shaseng_22": {"rotate": [{"angle": 0.88, "curve": 0, "c2": 0.39, "c3": 0.246, "c4": 0.77}, {"time": 0.0333, "angle": 6.42, "curve": 0.142, "c2": 0.4, "c3": 0.447, "c4": 0.75}, {"time": 0.1667, "angle": 6.81, "curve": 0.244, "c2": 0.55, "c3": 0.57, "c4": 0.91}, {"time": 0.2333, "angle": 2.08, "curve": "stepped"}, {"time": 0.2667, "angle": 17.89, "curve": 0, "c2": 0.19, "c3": 0.486}, {"time": 0.4, "angle": 15.71, "curve": 0, "c2": 0.19, "c3": 0.486}, {"time": 0.4667, "angle": 0.88}], "translate": [{"time": 0.0333, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1667, "x": -6.23, "y": 4.66, "curve": "stepped"}, {"time": 0.2333, "x": -6.23, "y": 4.66, "curve": "stepped"}, {"time": 0.2667, "y": -11.52, "curve": "stepped"}, {"time": 0.4, "y": -11.52, "curve": 0, "c2": 0.19, "c3": 0.486}, {"time": 0.4667}]}, "shaseng_23": {"rotate": [{"angle": 0.91, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": 3.62, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1667, "angle": -26.29, "curve": "stepped"}, {"time": 0.2333, "angle": -26.29, "curve": "stepped"}, {"time": 0.2667, "angle": 29.28, "curve": 0, "c2": 0.19, "c3": 0.486}, {"time": 0.4, "angle": 26.77, "curve": 0, "c2": 0.19, "c3": 0.486}, {"time": 0.4667, "angle": 0.91}], "translate": [{"x": -1.1, "y": -0.05, "curve": "stepped"}, {"time": 0.0333, "x": -1.1, "y": -0.05, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1667, "x": 24.53, "y": -19.12, "curve": "stepped"}, {"time": 0.2333, "x": 24.53, "y": -19.12, "curve": "stepped"}, {"time": 0.2667, "x": -44.52, "y": 9.1, "curve": 0, "c2": 0.19, "c3": 0.486}, {"time": 0.4, "x": -35.64, "y": 7.23, "curve": 0, "c2": 0.19, "c3": 0.486}, {"time": 0.4667, "x": -1.1, "y": -0.05}]}, "shaseng_6": {"rotate": [{"angle": 0.4, "curve": "stepped"}, {"time": 0.0333, "angle": 0.4, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1667, "angle": -23.95, "curve": "stepped"}, {"time": 0.2333, "angle": -23.95, "curve": "stepped"}, {"time": 0.2667, "angle": 0.4}], "translate": [{"x": 0.11, "y": -0.01, "curve": "stepped"}, {"time": 0.0333, "x": 0.11, "y": -0.01, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1667, "x": 7.34, "y": -20.5, "curve": "stepped"}, {"time": 0.2333, "x": 7.34, "y": -20.5, "curve": "stepped"}, {"time": 0.2667, "x": 0.11, "y": -0.01}]}, "shaseng_3": {"rotate": [{"angle": -1.22, "curve": "stepped"}, {"time": 0.0333, "angle": -1.22, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1667, "angle": 0.94, "curve": "stepped"}, {"time": 0.2333, "angle": 0.94, "curve": "stepped"}, {"time": 0.2667, "angle": -1.22}]}, "shaseng_4": {"rotate": [{"angle": -3.22, "curve": "stepped"}, {"time": 0.0333, "angle": -3.22, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1667, "angle": 13.52, "curve": "stepped"}, {"time": 0.2333, "angle": 13.52, "curve": "stepped"}, {"time": 0.2667, "angle": -3.22}]}, "shaseng_5": {"rotate": [{"angle": -11.17}]}, "shaseng_7": {"rotate": [{"angle": 0.32, "curve": "stepped"}, {"time": 0.0333, "angle": 0.32, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1667, "angle": 1.44, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2667, "angle": 0.32}]}, "shaseng_8": {"rotate": [{"angle": -0.85, "curve": "stepped"}, {"time": 0.0333, "angle": -0.85, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1667, "angle": 13.81, "curve": "stepped"}, {"time": 0.2333, "angle": 13.81, "curve": "stepped"}, {"time": 0.2667, "angle": -0.85}]}, "shaseng_9": {"rotate": [{"angle": -19.64, "curve": "stepped"}, {"time": 0.0333, "angle": -19.64, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1667, "angle": 14.97, "curve": "stepped"}, {"time": 0.2333, "angle": 14.97, "curve": "stepped"}, {"time": 0.2667, "angle": -19.64}]}, "shaseng_10": {"rotate": [{"angle": -2.78, "curve": "stepped"}, {"time": 0.0333, "angle": -2.78, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1667, "angle": 2.8, "curve": "stepped"}, {"time": 0.2333, "angle": 2.8, "curve": "stepped"}, {"time": 0.2667, "angle": -2.78}]}, "shaseng_11": {"rotate": [{"angle": -7.55}]}, "shaseng_21": {"rotate": [{"angle": -6.53, "curve": "stepped"}, {"time": 0.0333, "angle": -6.53, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1667, "angle": -8.68, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.2667, "angle": -6.53, "curve": "stepped"}, {"time": 0.4, "angle": -6.53, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.4333, "angle": 3.84, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.4667, "angle": -6.53}]}, "shaseng_1": {"rotate": [{"angle": 0.65, "curve": "stepped"}, {"time": 0.0333, "angle": 0.65, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1667, "angle": -3.22, "curve": 0.334, "c2": 0.34, "c3": 0.758}, {"time": 0.2667, "angle": 0.65}]}, "shaseng_2": {"rotate": [{"angle": 5.3}]}, "shaseng_25": {"rotate": [{"angle": -1.32, "curve": "stepped"}, {"time": 0.0333, "angle": -1.32, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1667, "angle": 5.1, "curve": "stepped"}, {"time": 0.2333, "angle": 5.1, "curve": "stepped"}, {"time": 0.2667, "angle": -1.32}]}, "shaseng_26": {"rotate": [{"angle": -5.94, "curve": "stepped"}, {"time": 0.0333, "angle": -5.94, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1667, "angle": -6.53, "curve": 0.37, "c2": 0.48, "c3": 0.753}, {"time": 0.2667, "angle": -5.94, "curve": "stepped"}, {"time": 0.4, "angle": -5.94, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.4333, "angle": 5.48, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 0.4667, "angle": -5.94}]}, "shaseng_27": {"rotate": [{"angle": 1.22, "curve": "stepped"}, {"time": 0.0333, "angle": 1.22, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1667, "angle": 3.77, "curve": 0.334, "c2": 0.34, "c3": 0.758}, {"time": 0.2667, "angle": 1.22}]}, "shaseng_28": {"rotate": [{"angle": -5.72, "curve": "stepped"}, {"time": 0.0333, "angle": -5.72, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1667, "angle": 7.78, "curve": "stepped"}, {"time": 0.2333, "angle": 7.78, "curve": "stepped"}, {"time": 0.2667, "angle": -5.72}]}, "shaseng_29": {"rotate": [{"angle": -10.42, "curve": "stepped"}, {"time": 0.0333, "angle": -10.42, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1667, "angle": -10.9, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 0.2667, "angle": -10.42}]}, "shaseng_30": {"rotate": [{"angle": -1.53, "curve": "stepped"}, {"time": 0.0333, "angle": -1.53, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1667, "angle": -3.17, "curve": 0.358, "c2": 0.42, "c3": 0.756}, {"time": 0.2667, "angle": -1.53, "curve": "stepped"}, {"time": 0.4, "angle": -1.53, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.4333, "angle": 3.98, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.4667, "angle": -1.53}]}, "shaseng_31": {"rotate": [{"angle": 2.24, "curve": "stepped"}, {"time": 0.0333, "angle": 2.24, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1667, "angle": -6.42, "curve": "stepped"}, {"time": 0.2333, "angle": -6.42, "curve": "stepped"}, {"time": 0.2667, "angle": 2.24}]}, "shaseng_32": {"rotate": [{"angle": 10.17, "curve": "stepped"}, {"time": 0.0333, "angle": 10.17, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1667, "angle": -10.05, "curve": "stepped"}, {"time": 0.2333, "angle": -10.05, "curve": "stepped"}, {"time": 0.2667, "angle": 10.17}]}, "shaseng_24": {"rotate": [{"angle": -1.59, "curve": "stepped"}, {"time": 0.0333, "angle": -1.59, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1667, "angle": 0.4, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2667, "angle": -1.59}]}, "shaseng_33": {"rotate": [{"angle": -7.82, "curve": "stepped"}, {"time": 0.0333, "angle": -7.82, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1667, "angle": 3.13, "curve": "stepped"}, {"time": 0.2333, "angle": 3.13, "curve": "stepped"}, {"time": 0.2667, "angle": -7.82}]}, "shaseng_34": {"rotate": [{"angle": -26.92}]}, "shaseng_35": {"rotate": [{"angle": -17.76, "curve": "stepped"}, {"time": 0.0333, "angle": -17.76, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1667, "angle": -25.45, "curve": 0.358, "c2": 0.42, "c3": 0.756}, {"time": 0.2667, "angle": -17.76}]}, "shaseng_36": {"rotate": [{"angle": 0.16, "curve": "stepped"}, {"time": 0.0333, "angle": 0.16, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1667, "angle": -1.22, "curve": 0.334, "c2": 0.34, "c3": 0.758}, {"time": 0.2667, "angle": 0.16}]}, "shaseng_37": {"rotate": [{"angle": 2.26, "curve": "stepped"}, {"time": 0.0333, "angle": 2.26, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1667, "angle": -4.6, "curve": "stepped"}, {"time": 0.2333, "angle": -4.6, "curve": "stepped"}, {"time": 0.2667, "angle": 2.26}]}, "shaseng_38": {"rotate": [{"angle": 10.39, "curve": "stepped"}, {"time": 0.0333, "angle": 10.39, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1667, "angle": -22.74, "curve": "stepped"}, {"time": 0.2333, "angle": -22.74, "curve": "stepped"}, {"time": 0.2667, "angle": 10.39}]}, "shaseng_39": {"rotate": [{"angle": 12.82, "curve": "stepped"}, {"time": 0.0333, "angle": 12.82, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1667, "angle": 13.53, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 0.2667, "angle": 12.82}]}, "shaseng_40": {"rotate": [{"angle": -3.61}]}, "shaseng_41": {"rotate": [{"angle": -5.03, "curve": "stepped"}, {"time": 0.0333, "angle": -5.03, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1667, "angle": -6.54, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.2667, "angle": -5.03, "curve": "stepped"}, {"time": 0.4, "angle": -5.03, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.4333, "angle": 11.8, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.4667, "angle": -5.03}]}, "shaseng_42": {"rotate": [{"angle": -3.16, "curve": "stepped"}, {"time": 0.0333, "angle": -3.16, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1667, "angle": -12.76, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2667, "angle": -3.16}]}, "shaseng_43": {"rotate": [{"angle": 13.07, "curve": "stepped"}, {"time": 0.0333, "angle": 13.07, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1667, "angle": -14.3, "curve": "stepped"}, {"time": 0.2333, "angle": -14.3, "curve": "stepped"}, {"time": 0.2667, "angle": 13.07}]}, "shaseng_0": {"rotate": [{"angle": 0.31, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": -43.73, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1, "angle": -144.3, "curve": 0.302, "c2": 0.36, "c3": 0.639, "c4": 0.7}, {"time": 0.1667, "angle": 130.33, "curve": "stepped"}, {"time": 0.2333, "angle": 130.33, "curve": "stepped"}, {"time": 0.2667, "angle": -71.59, "curve": "stepped"}, {"time": 0.4, "angle": -71.59, "curve": 0, "c2": 0.19, "c3": 0.486}, {"time": 0.4667, "angle": 0.31}], "translate": [{"curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "x": 4.3, "y": 15.79, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1667, "x": 3.54, "y": 15.26, "curve": 0.715, "c3": 0.891, "c4": 0.4}, {"time": 0.2333, "x": -1.99, "y": 30.72, "curve": "stepped"}, {"time": 0.2667, "x": -9.88, "y": 48.67, "curve": "stepped"}, {"time": 0.4, "x": -9.88, "y": 48.67, "curve": 0, "c2": 0.16, "c3": 0.422, "c4": 0.68}, {"time": 0.4667}]}, "shaseng_44": {"rotate": [{"angle": 1.4, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": -3.96, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1667, "angle": 7.09, "curve": 0.715, "c3": 0.891, "c4": 0.4}, {"time": 0.2333, "angle": -11.35, "curve": "stepped"}, {"time": 0.2667, "angle": 1.4, "curve": "stepped"}, {"time": 0.4, "angle": 1.4, "curve": 0, "c2": 0.19, "c3": 0.486}, {"time": 0.4333, "angle": -9.46, "curve": 0.25, "c4": 0.5}, {"time": 0.4667, "angle": 1.4}]}, "shaseng_45": {"rotate": [{"angle": 3.88, "curve": "stepped"}, {"time": 0.0333, "angle": 3.88, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1667, "angle": 3.58, "curve": "stepped"}, {"time": 0.2333, "angle": 3.58, "curve": "stepped"}, {"time": 0.2667, "angle": 3.88}]}, "shaseng_47": {"rotate": [{"angle": -0.28, "curve": 0, "c2": 0.34, "c3": 0.306, "c4": 0.67}, {"time": 0.0333, "angle": 0.44, "curve": 0.246, "c2": 0.33, "c3": 0.578, "c4": 0.67}, {"time": 0.0667, "angle": -144.25, "curve": 0.231, "c2": 0.34, "c3": 0.561, "c4": 0.67}, {"time": 0.1667, "angle": 164.37, "curve": 0.244, "c2": 0.55, "c3": 0.57, "c4": 0.91}, {"time": 0.2333, "angle": 161.89, "curve": "stepped"}, {"time": 0.2667, "angle": -48.04, "curve": "stepped"}, {"time": 0.4, "angle": -48.04, "curve": 0, "c2": 0.19, "c3": 0.486}, {"time": 0.4667, "angle": -0.28}], "translate": [{"time": 0.0333, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1667, "x": -1.87, "y": -21.64, "curve": "stepped"}, {"time": 0.2333, "x": -1.87, "y": -21.64, "curve": "stepped"}, {"time": 0.2667, "x": 11.93, "y": -14.38, "curve": "stepped"}, {"time": 0.4, "x": 11.93, "y": -14.38, "curve": 0, "c2": 0.19, "c3": 0.486}, {"time": 0.4667}], "scale": [{"time": 0.0333, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1667, "x": 0.969, "y": 0.969, "curve": "stepped"}, {"time": 0.2333, "x": 0.969, "y": 0.969, "curve": "stepped"}, {"time": 0.2667}]}, "shaseng_48": {"rotate": [{"angle": -1.21, "curve": "stepped"}, {"time": 0.0333, "angle": -1.21, "curve": 0.155, "c2": 0.34, "c3": 0.476, "c4": 0.68}, {"time": 0.1667, "angle": -8.48, "curve": 0.538, "c2": 0.24, "c3": 0.8, "c4": 0.59}, {"time": 0.2333, "angle": -13.55, "curve": "stepped"}, {"time": 0.2667, "angle": 19.68, "curve": "stepped"}, {"time": 0.4, "angle": 19.68, "curve": 0, "c2": 0.19, "c3": 0.486}, {"time": 0.4667, "angle": -1.21}], "translate": [{"time": 0.0333, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1667, "x": -0.3, "y": -4.14, "curve": "stepped"}, {"time": 0.2333, "x": -0.3, "y": -4.14, "curve": "stepped"}, {"time": 0.2667, "x": -11.35, "y": -2.08, "curve": 0, "c2": 0.19, "c3": 0.486}, {"time": 0.4}]}, "shaseng_49": {"rotate": [{"angle": -0.94, "curve": "stepped"}, {"time": 0.0333, "angle": -0.94, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1667, "angle": -27.53, "curve": "stepped"}, {"time": 0.2333, "angle": -27.53, "curve": "stepped"}, {"time": 0.2667, "angle": -36.63, "curve": "stepped"}, {"time": 0.4, "angle": -36.63, "curve": 0, "c2": 0.19, "c3": 0.486}, {"time": 0.4667, "angle": -0.94}]}, "shaseng_46": {"rotate": [{"time": 0.2333, "curve": "stepped"}, {"time": 0.2667, "angle": -12.58, "curve": 0, "c2": 0.19, "c3": 0.486}, {"time": 0.4, "angle": -11.43, "curve": 0, "c2": 0.19, "c3": 0.486}, {"time": 0.4667}], "translate": [{"time": 0.0333, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1667, "x": 30.96, "y": -197.73, "curve": 0.129, "c2": 0.35, "c3": 0.555}, {"time": 0.2333, "x": 37.68, "y": -240.67, "curve": "stepped"}, {"time": 0.2667, "x": -13.11, "y": -262.55, "curve": 0, "c2": 0.23, "c3": 0.352, "c4": 0.58}, {"time": 0.4, "x": -4.56, "y": -263.67, "curve": 0, "c2": 0.23, "c3": 0.352, "c4": 0.58}, {"time": 0.4667}], "scale": [{"time": 0.0333, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1667, "x": 0.946, "y": 0.946, "curve": "stepped"}, {"time": 0.2333, "x": 0.946, "y": 0.946, "curve": "stepped"}, {"time": 0.2667}]}, "shaseng_55": {"rotate": [{"angle": -1.93, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": -6.64, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1667, "angle": 11.42, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667, "angle": -1.93, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 8.46, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -1.93}]}, "shaseng_56": {"rotate": [{"angle": 5.76, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": 14.37, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1667, "angle": 33.52, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667, "angle": 5.76, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 47.19, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 5.76}]}, "shaseng_59": {"rotate": [{"angle": -1.03, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": -6.06, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1667, "angle": -17.39, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667, "angle": -1.03, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -16.27, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -1.03}]}, "shaseng_60": {"rotate": [{"angle": 1.88, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": 8.02, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1667, "angle": 12.69, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667, "angle": 1.88, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 26.4, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 1.88}]}, "shaseng_63": {"rotate": [{"angle": 0.48, "curve": "stepped"}, {"time": 0.0333, "angle": 0.48, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1667, "angle": -9.17, "curve": "stepped"}, {"time": 0.2333, "angle": -9.17, "curve": "stepped"}, {"time": 0.2667, "angle": 0.48}], "translate": [{"time": 0.0333, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1667, "x": 19.58, "y": -12.73, "curve": "stepped"}, {"time": 0.2333, "x": 19.58, "y": -12.73, "curve": "stepped"}, {"time": 0.2667}]}, "shaseng_63b": {"rotate": [{"angle": -11.96, "curve": "stepped"}, {"time": 0.0333, "angle": -11.96, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1667, "angle": 0.69, "curve": "stepped"}, {"time": 0.2333, "angle": 0.69, "curve": "stepped"}, {"time": 0.2667, "angle": -11.96}]}, "shaseng_63c": {"rotate": [{"angle": -5.86, "curve": "stepped"}, {"time": 0.0333, "angle": -5.86, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1667, "angle": -14.12, "curve": "stepped"}, {"time": 0.2333, "angle": -14.12, "curve": "stepped"}, {"time": 0.2667, "angle": -5.86}]}, "shaseng_50": {"rotate": [{"angle": 4.94, "curve": "stepped"}, {"time": 0.0333, "angle": 4.94, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1667, "angle": 8.56, "curve": 0.358, "c2": 0.42, "c3": 0.756}, {"time": 0.2667, "angle": 4.94}]}, "shaseng_50b": {"rotate": [{"angle": 3.36, "curve": "stepped"}, {"time": 0.0333, "angle": 3.36, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1667, "angle": -5.23, "curve": 0.334, "c2": 0.34, "c3": 0.758}, {"time": 0.2667, "angle": 3.36}]}, "shaseng_50c": {"rotate": [{"angle": 18.6}]}, "shaseng_64": {"rotate": [{"angle": -2.95, "curve": "stepped"}, {"time": 0.0333, "angle": -2.95, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1667, "angle": 2.61, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.2667, "angle": -2.95}]}, "shaseng_65": {"rotate": [{"angle": -15.45, "curve": "stepped"}, {"time": 0.0333, "angle": -15.45, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1667, "angle": -36.8, "curve": 0.289, "c2": 0.18, "c3": 0.645, "c4": 0.59}, {"time": 0.2667, "angle": -15.45, "curve": "stepped"}, {"time": 0.4, "angle": -15.45, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4333, "angle": -113.38, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4667, "angle": -15.45}]}, "shaseng_66": {"rotate": [{"angle": -0.06, "curve": "stepped"}, {"time": 0.0333, "angle": -0.06, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1667, "angle": -0.6, "curve": 0.343, "c2": 0.37, "c3": 0.757}, {"time": 0.2667, "angle": -0.06, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.4, "angle": -1.62, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.4667, "angle": -0.06}]}, "shaseng_67": {"rotate": [{"angle": 1.41, "curve": "stepped"}, {"time": 0.0333, "angle": 1.41, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1667, "angle": -2.73, "curve": "stepped"}, {"time": 0.2333, "angle": -2.73, "curve": "stepped"}, {"time": 0.2667, "angle": 1.41, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.4, "angle": -2.85, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.4667, "angle": 1.41}]}, "shaseng_68": {"rotate": [{"angle": 4.47, "curve": "stepped"}, {"time": 0.2667, "angle": 4.47, "curve": 0.271, "c2": 0.09, "c3": 0.753}, {"time": 0.4, "angle": -5.12, "curve": 0.271, "c2": 0.09, "c3": 0.753}, {"time": 0.4667, "angle": 4.47}]}, "shaseng_69": {"rotate": [{"angle": 7.4, "curve": "stepped"}, {"time": 0.0333, "angle": 7.4, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1667, "angle": 10.01, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.2667, "angle": 7.4, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.4, "angle": 0.5, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.4667, "angle": 7.4}]}, "shaseng_70": {"rotate": [{"angle": -0.55, "curve": "stepped"}, {"time": 0.0333, "angle": -0.55, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1667, "angle": -0.67, "curve": 0.346, "c2": 0.38, "c3": 0.7, "c4": 0.78}, {"time": 0.2333, "angle": 19.71, "curve": "stepped"}, {"time": 0.2667, "angle": 16.24, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.4, "angle": -2.49, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.4667, "angle": -0.55}], "translate": [{"time": 0.0333, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1667, "x": -5.2, "y": -1.32, "curve": "stepped"}, {"time": 0.2333, "x": -5.2, "y": -1.32, "curve": "stepped"}, {"time": 0.2667}]}, "shaseng_71": {"rotate": [{"angle": -0.36, "curve": "stepped"}, {"time": 0.0333, "angle": -0.36, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1667, "angle": -3.95, "curve": 0.323, "c2": 0.3, "c3": 0.69, "c4": 0.74}, {"time": 0.2333, "angle": 25.92, "curve": "stepped"}, {"time": 0.2667, "angle": 10.79, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.4, "angle": -5.87, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.4667, "angle": -0.36}]}, "shaseng_72": {"rotate": [{"angle": 2.08, "curve": "stepped"}, {"time": 0.0333, "angle": 2.08, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1667, "angle": -10.54, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -4.14, "curve": "stepped"}, {"time": 0.2667, "angle": 15.84, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.4, "angle": -8.95, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.4667, "angle": 2.08}]}, "shaseng_73": {"rotate": [{"angle": 11.12, "curve": "stepped"}, {"time": 0.0333, "angle": 11.12, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1667, "angle": -18.49, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2333, "angle": -14.95, "curve": "stepped"}, {"time": 0.2667, "angle": 26.38, "curve": 0.263, "c2": 0.08, "c3": 0.645, "c4": 0.59}, {"time": 0.4, "angle": 3.6, "curve": 0.263, "c2": 0.08, "c3": 0.645, "c4": 0.59}, {"time": 0.4667, "angle": 11.12}]}, "shaseng_58": {"rotate": [{"time": 0.0333, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1667, "angle": 7.38, "curve": 0.25, "c4": 0.5}, {"time": 0.2667, "angle": 9.09, "curve": "stepped"}, {"time": 0.4, "angle": 9.09, "curve": 0, "c2": 0.19, "c3": 0.486}, {"time": 0.4667}], "translate": [{"y": 1.63, "curve": "stepped"}, {"time": 0.0333, "y": 1.63, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1667, "x": 6.02, "y": 3.94, "curve": 0.25, "c4": 0.5}, {"time": 0.2667, "y": 1.63}], "scale": [{"time": 0.0333, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1667, "x": 1.06, "curve": 0.25, "c4": 0.5}, {"time": 0.2667}]}, "shaseng_12": {"rotate": [{"angle": -6.08}]}, "daoguang": {"scale": [{"time": 0.0333, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1667, "x": 0.73, "y": 0.73, "curve": "stepped"}, {"time": 0.2333, "x": 0.73, "y": 0.73, "curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": 1.58, "y": 0.855}]}, "leg1": {"rotate": [{"time": 0.0333, "curve": 0.283, "c2": 0.35, "c3": 0.615, "c4": 0.68}, {"time": 0.0667, "angle": 15.03, "curve": 0.168, "c2": 0.54, "c3": 0.476, "c4": 0.93}, {"time": 0.1667}], "translate": [{"time": 0.0333, "curve": 0.253, "c2": 0.36, "c3": 0.581, "c4": 0.69}, {"time": 0.0667, "x": -6.67, "y": 167.16, "curve": 0.192, "c2": 0.55, "c3": 0.507, "c4": 0.93}, {"time": 0.1667, "x": -25.73, "y": 351.86}, {"time": 0.2333, "x": -42.56, "y": 340.19, "curve": "stepped"}, {"time": 0.2667, "x": -23.74, "y": -36.69, "curve": "stepped"}, {"time": 0.4, "x": -23.74, "y": -36.69, "curve": 0.127, "c2": 0.6, "c3": 0.75}, {"time": 0.4667}]}, "leg2": {"translate": [{"time": 0.0333, "curve": 0.253, "c2": 0.36, "c3": 0.581, "c4": 0.69}, {"time": 0.0667, "x": -10.2, "y": 182.16, "curve": 0.192, "c2": 0.55, "c3": 0.507, "c4": 0.93}, {"time": 0.1667, "x": 92.36, "y": 346.23}, {"time": 0.2333, "x": 59.88, "y": 285.31, "curve": "stepped"}, {"time": 0.2667, "x": 65.21, "y": 17.69, "curve": "stepped"}, {"time": 0.4, "x": 65.21, "y": 17.69, "curve": 0.25, "c3": 0.75}, {"time": 0.4667}]}, "shaseng_77": {"rotate": [{"time": 0.1667}, {"time": 0.2333, "angle": -15.98, "curve": "stepped"}, {"time": 0.2667}]}, "daoguang1": {"rotate": [{"curve": "stepped"}, {"time": 0.2667, "angle": 6.96, "curve": "stepped"}, {"time": 0.3, "angle": -0.77, "curve": "stepped"}, {"time": 0.3333}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "x": 10.78, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.3333, "x": -25.83}]}, "diguang_0": {"translate": [{"time": 0.3}, {"time": 0.4667, "x": -24.6}], "scale": [{"time": 0.3, "x": 0.533, "y": 0.582}]}, "shaseng_78": {"rotate": [{"time": 0.2333, "curve": "stepped"}, {"time": 0.2667, "angle": -12.58, "curve": 0, "c2": 0.19, "c3": 0.486}, {"time": 0.4, "angle": -11.43, "curve": 0, "c2": 0.19, "c3": 0.486}, {"time": 0.4667}], "translate": [{"time": 0.0333, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1667, "x": 30.96, "y": -197.73, "curve": 0.129, "c2": 0.35, "c3": 0.555}, {"time": 0.2333, "x": 37.68, "y": -240.67, "curve": "stepped"}, {"time": 0.2667, "x": -13.11, "y": -262.55, "curve": 0, "c2": 0.23, "c3": 0.352, "c4": 0.58}, {"time": 0.4, "x": -4.56, "y": -263.67, "curve": 0, "c2": 0.23, "c3": 0.352, "c4": 0.58}, {"time": 0.4667}], "scale": [{"time": 0.0333, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1667, "x": 0.946, "y": 0.946, "curve": "stepped"}, {"time": 0.2333, "x": 0.946, "y": 0.946, "curve": "stepped"}, {"time": 0.2667}]}}, "deform": {"default": {"shaseng_0": {"shaseng_0": [{"offset": 54, "vertices": [-0.03646, -0.34388, 0.10032, -0.33095, -0.07283, -0.68767, -0.07283, -0.68767, 0, 0, 0, 0, 0.20083, 2.08921, 0.12687, 1.00017, 0.12687, 1.00017, -0.12721, 1.02398, -0.12721, 1.02398, -0.71556, 2.38928, -1.80434, 1.71917], "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.1667, "offset": 66, "vertices": [-0.00989, -1.19092, 1.76117, -2.99219, -3.01178, -4.1427, 1.3988, -4.92688, -0.46216, -3.44116, 0.28113, -2.51038, 1.66516, -1.89938], "curve": "stepped"}, {"time": 0.2333, "offset": 66, "vertices": [-0.00989, -1.19092, 1.76117, -2.99219, -3.01178, -4.1427, 1.3988, -4.92688, -0.46216, -3.44116, 0.28113, -2.51038, 1.66516, -1.89938], "curve": "stepped"}, {"time": 0.2667, "offset": 54, "vertices": [-0.03646, -0.34388, 0.10032, -0.33095, -0.07283, -0.68767, -0.07283, -0.68767, 0, 0, 0, 0, 0.20083, 2.08921, 0.12687, 1.00017, 0.12687, 1.00017, -0.12721, 1.02398, -0.12721, 1.02398, -0.71556, 2.38928, -1.80434, 1.71917]}]}, "shaseng_9": {"shaseng_9": [{"curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.1667, "offset": 6, "vertices": [-2.38416, 3.77466, -10.33405, 3.17566, -10.71179, 8.79553, -9.44229, 13.69397, -9.2923, 7.49048, -9.80225, 11.12439, -9.80225, 11.12439, -9.80225, 11.12439, -9.80225, 11.12439, -8.74341, 7.24951, -2.38416, 3.77466, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.38416, 3.77466, -8.74341, 7.24951, -10.61719, 7.39038, 0, 0, 0, 0, -2.38416, 3.77466, -8.74341, 7.24951, -10.9389, 8.21362, 0, 0, 0, 0, -2.38416, 3.77466, -8.74341, 7.24951, -9.80225, 11.12439], "curve": 0.25, "c4": 0.5}, {"time": 0.2667}]}, "shaseng_12": {"shaseng_12": [{"offset": 38, "vertices": [-0.15173, 2.96886, 0.10692, 2.97074, -0.10624, 2.07813, 0.07481, 2.07948, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.13476, -0.00691, -0.32353, -0.01655, -0.31803, 0.10331, 0.33301, -1.76637, 0.50324, 1.49926, -0.17594, -0.90179, -0.25176, -0.85232, 0.41558, 0.02124, 0.42157, 0.07574, -0.25341, -0.01296, -0.24782, 0.10075, 0.29612, 0.01514, 0.30013, 0.04975, 0, 0, 0, 0, 0.29615, 0.01514, 0.30013, 0.04971], "curve": "stepped"}, {"time": 0.0333, "offset": 38, "vertices": [-0.15173, 2.96886, 0.10692, 2.97074, -0.10624, 2.07813, 0.07481, 2.07948, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.13476, -0.00691, -0.32353, -0.01655, -0.31803, 0.10331, 0.33301, -1.76637, 0.50324, 1.49926, -0.17594, -0.90179, -0.25176, -0.85232, 0.41558, 0.02124, 0.42157, 0.07574, -0.25341, -0.01296, -0.24782, 0.10075, 0.29612, 0.01514, 0.30013, 0.04975, 0, 0, 0, 0, 0.29615, 0.01514, 0.30013, 0.04971], "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.1667, "offset": 38, "vertices": [-0.15173, 2.96886, 0.10692, 2.97074, -0.10624, 2.07813, 0.07481, 2.07948, 7.15027, -19.98091, 7.15027, -19.98091, 7.15027, -19.98091, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.13476, -0.00691, -0.32353, -0.01655, -0.31803, 0.10331, 0.33301, -1.76637, 4.07734, -8.49119, -0.17594, -0.90179, -0.25176, -0.85232, 0.41558, 0.02124, 0.42157, 0.07574, -0.25341, -0.01296, -0.24782, 0.10075, 0.29612, 0.01514, 0.30013, 0.04975, 0, 0, 0, 0, 0.29615, 0.01514, 0.30013, 0.04971], "curve": "stepped"}, {"time": 0.2333, "offset": 38, "vertices": [-0.15173, 2.96886, 0.10692, 2.97074, -0.10624, 2.07813, 0.07481, 2.07948, 7.15027, -19.98091, 7.15027, -19.98091, 7.15027, -19.98091, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.13476, -0.00691, -0.32353, -0.01655, -0.31803, 0.10331, 0.33301, -1.76637, 4.07734, -8.49119, -0.17594, -0.90179, -0.25176, -0.85232, 0.41558, 0.02124, 0.42157, 0.07574, -0.25341, -0.01296, -0.24782, 0.10075, 0.29612, 0.01514, 0.30013, 0.04975, 0, 0, 0, 0, 0.29615, 0.01514, 0.30013, 0.04971], "curve": "stepped"}, {"time": 0.2667, "offset": 38, "vertices": [-0.15173, 2.96886, 0.10692, 2.97074, -0.10624, 2.07813, 0.07481, 2.07948, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.13476, -0.00691, -0.32353, -0.01655, -0.31803, 0.10331, 0.33301, -1.76637, 0.50324, 1.49926, -0.17594, -0.90179, -0.25176, -0.85232, 0.41558, 0.02124, 0.42157, 0.07574, -0.25341, -0.01296, -0.24782, 0.10075, 0.29612, 0.01514, 0.30013, 0.04975, 0, 0, 0, 0, 0.29615, 0.01514, 0.30013, 0.04971]}]}, "shaseng_13": {"shaseng_13": [{"offset": 40, "vertices": [-83.11365, -48.45935, -72.98035, -9.20032, -67.24121, 4.88055, -59.9043, 32.10095], "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.1667, "offset": 40, "vertices": [-85.29834, -54.31567, -75.16504, -15.05664, -69.4259, -0.97577, -64.56152, 27.9325], "curve": "stepped"}, {"time": 0.2333, "offset": 40, "vertices": [-85.29834, -54.31567, -75.16504, -15.05664, -69.4259, -0.97577, -64.56152, 27.9325], "curve": "stepped"}, {"time": 0.2667, "offset": 40, "vertices": [-83.11365, -48.45935, -72.98035, -9.20032, -67.24121, 4.88055, -59.9043, 32.10095]}]}, "shaseng_16": {"shaseng_16": [{"offset": 110, "vertices": [0.46622, -2.23144, 1.5949, -1.62874, 0.46622, -2.23144, 1.5949, -1.62874, 0.46622, -2.23144], "curve": "stepped"}, {"time": 0.0333, "offset": 110, "vertices": [0.46622, -2.23144, 1.5949, -1.62874, 0.46622, -2.23144, 1.5949, -1.62874, 0.46622, -2.23144], "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.1667, "offset": 110, "vertices": [0.05134, -0.24571, 0.17562, -0.17935, 0.05134, -0.24571, 0.17562, -0.17935, 0.05134, -0.24571], "curve": "stepped"}, {"time": 0.2333, "offset": 110, "vertices": [0.05134, -0.24571, 0.17562, -0.17935, 0.05134, -0.24571, 0.17562, -0.17935, 0.05134, -0.24571], "curve": "stepped"}, {"time": 0.2667, "offset": 110, "vertices": [0.46622, -2.23144, 1.5949, -1.62874, 0.46622, -2.23144, 1.5949, -1.62874, 0.46622, -2.23144]}]}}}}, "4": {"slots": {"01_0": {"attachment": [{"time": 0.2667, "name": "01_00"}, {"time": 0.3333, "name": "01_02"}, {"time": 0.4, "name": "01_04"}, {"time": 0.4667, "name": "01_06"}, {"time": 0.5333, "name": "01_08"}, {"time": 0.5667, "name": "01_00"}, {"time": 0.6333, "name": "01_02"}, {"time": 0.7, "name": "01_04"}, {"time": 0.7333, "name": null}]}, "01_00": {"attachment": [{"time": 0.2667, "name": "01_00"}, {"time": 0.3333, "name": "01_02"}, {"time": 0.4, "name": "01_04"}, {"time": 0.4667, "name": "01_06"}, {"time": 0.5333, "name": "01_08"}, {"time": 0.5667, "name": "01_00"}, {"time": 0.6333, "name": "01_02"}, {"time": 0.7, "name": "01_04"}, {"time": 0.7333, "name": null}]}, "01_1": {"attachment": [{"time": 0.2667, "name": "01_00"}, {"time": 0.3333, "name": "01_02"}, {"time": 0.4, "name": "01_04"}, {"time": 0.4667, "name": "01_06"}, {"time": 0.5333, "name": "01_08"}, {"time": 0.5667, "name": "01_00"}, {"time": 0.6333, "name": "01_02"}, {"time": 0.7, "name": "01_04"}, {"time": 0.7333, "name": null}]}, "6_00000": {"attachment": [{"time": 0.2333, "name": "6_00000"}, {"time": 0.3, "name": "6_00002"}, {"time": 0.3667, "name": "6_00004"}, {"time": 0.4333, "name": "6_00006"}, {"time": 0.5, "name": "6_00008"}, {"time": 0.5667, "name": "6_00010"}, {"time": 0.6333, "name": "6_00000"}, {"time": 0.7, "name": "6_00002"}, {"time": 0.7333, "name": null}]}, "a1": {"color": [{"time": 0.2333, "color": "ffffffff"}, {"time": 0.4667, "color": "ffffff00"}], "attachment": [{"time": 0.2333, "name": "a1"}, {"time": 0.8, "name": null}]}, "ag1": {"color": [{"time": 0.2667, "color": "ffffffff"}, {"time": 0.3, "color": "ffffff00"}], "attachment": [{"time": 0.2333, "name": "ag2"}, {"time": 0.3, "name": null}]}, "asansheguang222": {"color": [{"time": 0.3333, "color": "ffffffff"}, {"time": 0.3667, "color": "ffffff00"}], "attachment": [{"time": 0.2333, "name": "asansheguang222"}, {"time": 0.8, "name": null}]}, "atu2": {"color": [{"time": 0.3667, "color": "ffffffff"}, {"time": 0.4333, "color": "ffffff00"}], "attachment": [{"time": 0.2333, "name": "atu2"}, {"time": 0.4333, "name": null}]}, "b": {"color": [{"time": 0.3, "color": "ffffffff"}, {"time": 0.6, "color": "ffffff00"}], "attachment": [{"time": 0.2667, "name": "b1"}, {"time": 0.3, "name": "b2"}, {"time": 0.4, "name": "b1"}, {"time": 0.5, "name": "b2"}, {"time": 0.6, "name": null}]}, "bing": {"color": [{"time": 0.2333, "color": "ffffffa9", "curve": "stepped"}, {"time": 0.5333, "color": "ffffffa9"}, {"time": 0.8, "color": "ffffff00"}], "attachment": [{"time": 0.2333, "name": "sanguanga2"}, {"time": 0.8, "name": null}]}, "bing2": {"color": [{"time": 0.2333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.5, "color": "ffffffff"}, {"time": 0.7, "color": "ffffff00"}], "attachment": [{"time": 0.2333, "name": "sanguanga2"}, {"time": 0.7, "name": null}]}, "bing3": {"color": [{"time": 0.2333, "color": "ffffff92", "curve": "stepped"}, {"time": 0.6333, "color": "ffffff92"}, {"time": 0.8, "color": "ffffff00"}], "attachment": [{"time": 0.2333, "name": "sanguanga2"}, {"time": 0.8, "name": null}]}, "daoguang": {"color": [{"time": 0.2, "color": "ffffffff", "curve": 0.318, "c2": 1.18, "c3": 0.654, "c4": 0}, {"time": 0.2333, "color": "ffffff00"}], "attachment": [{"name": null}, {"time": 0.1333, "name": "<PERSON><PERSON><PERSON>"}, {"time": 0.8, "name": null}]}, "daoying1": {"color": [{"time": 0.2, "color": "ffffffff", "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.2333, "color": "ffffff00"}], "attachment": [{"name": null}, {"time": 0.1333, "name": "daoying1"}, {"time": 0.8, "name": null}]}, "diguang_0": {"color": [{"time": 0.2333, "color": "ffffffb6", "curve": "stepped"}, {"time": 0.3333, "color": "ffffffb6"}, {"time": 0.7, "color": "ffffff00"}], "attachment": [{"time": 0.2333, "name": "diguang_2"}, {"time": 0.6333, "name": "diguang_4"}, {"time": 0.7, "name": null}]}, "dilie1s": {"color": [{"time": 0.6333, "color": "ffffffff"}, {"time": 0.7333, "color": "ffffff00"}], "attachment": [{"time": 0.2333, "name": "dilie1s"}, {"time": 0.8, "name": null}]}, "fasan": {"color": [{"time": 0.5333, "color": "ffffffff"}, {"time": 0.6, "color": "ffffff00"}], "attachment": [{"time": 0.2333, "name": "fasan"}, {"time": 0.2667, "name": "fasan2"}, {"time": 0.3, "name": "fasan"}, {"time": 0.3333, "name": "fasan2"}, {"time": 0.3667, "name": "fasan"}, {"time": 0.4, "name": "fasan2"}, {"time": 0.4333, "name": "fasan"}, {"time": 0.4667, "name": "fasan2"}, {"time": 0.5, "name": "fasan"}, {"time": 0.5333, "name": "fasan2"}, {"time": 0.5667, "name": "fasan"}, {"time": 0.6, "name": null}]}, "lanse": {"color": [{"time": 0.2333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.4333, "color": "ffffffff"}, {"time": 0.5333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.5667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6333, "color": "ffffffff", "curve": 0, "c2": 0.21, "c3": 0.75}, {"time": 0.7, "color": "ffffff00"}], "attachment": [{"time": 0.2333, "name": "lanse"}, {"time": 0.5333, "name": null}, {"time": 0.5667, "name": "lanse"}, {"time": 0.7, "name": null}]}, "shaseng_17": {"attachment": [{"time": 0.2333, "name": null}, {"time": 0.7333, "name": "shaseng_17"}]}, "shaseng_18": {"attachment": [{"time": 0.2333, "name": null}, {"time": 0.7333, "name": "shaseng_18"}]}, "shaseng_21": {"color": [{"time": 0.1, "color": "ffffff00"}, {"time": 0.1333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.3333, "color": "ffffffff", "curve": 0.188, "c2": 0.41, "c3": 0.75}, {"time": 0.5667, "color": "ffffff00"}], "attachment": [{"time": 0.1, "name": "wuqi"}, {"time": 0.5667, "name": null}]}, "xuewu7": {"color": [{"time": 0.4667, "color": "ffffffff"}, {"time": 0.5667, "color": "ffffff00"}], "attachment": [{"time": 0.3, "name": "<PERSON><PERSON><PERSON>"}, {"time": 0.8, "name": null}]}, "xuewu8": {"color": [{"time": 0.3, "color": "ffffffff", "curve": "stepped"}, {"time": 0.4333, "color": "ffffffff"}, {"time": 0.5333, "color": "ffffff00"}], "attachment": [{"time": 0.3, "name": "<PERSON><PERSON><PERSON>"}, {"time": 0.8, "name": null}]}, "xuewu9": {"color": [{"time": 0.3, "color": "ffffffff", "curve": "stepped"}, {"time": 0.4, "color": "ffffffff"}, {"time": 0.5333, "color": "ffffff00"}], "attachment": [{"time": 0.3, "name": "<PERSON><PERSON><PERSON>"}, {"time": 0.8, "name": null}]}, "xuewu10": {"color": [{"time": 0.5, "color": "ffffffff"}, {"time": 0.6, "color": "ffffff00"}], "attachment": [{"time": 0.3, "name": "<PERSON><PERSON><PERSON>"}, {"time": 0.8, "name": null}]}, "xuewu11": {"color": [{"time": 0.3, "color": "ffffffff", "curve": "stepped"}, {"time": 0.5667, "color": "ffffffff"}, {"time": 0.7, "color": "ffffff00"}], "attachment": [{"time": 0.3, "name": "<PERSON><PERSON><PERSON>"}, {"time": 0.8, "name": null}]}, "xuewu12": {"color": [{"time": 0.5667, "color": "ffffffff"}, {"time": 0.7, "color": "ffffff00"}], "attachment": [{"time": 0.3, "name": "<PERSON><PERSON><PERSON>"}, {"time": 0.8, "name": null}]}}, "bones": {"body": {"rotate": [{"curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": 2.06, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1333, "angle": 1.37, "curve": 0.25, "c4": 0.5}, {"time": 0.2, "angle": -8.56, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.2333}], "translate": [{"y": -1, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "y": -5.71, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1333, "x": 27.82, "y": 583.6, "curve": 0.25, "c4": 0.5}, {"time": 0.2, "x": 22.22, "y": 575.22, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.2333, "x": 33.61, "y": -46.56, "curve": 0, "c2": 0.33, "c3": 0.331, "c4": 0.67}, {"time": 0.3333, "x": 33.61, "y": -44.22, "curve": 0.155, "c2": 0.33, "c3": 0.485, "c4": 0.67}, {"time": 0.4333, "x": 33.61, "y": -46.56, "curve": 0, "c2": 0.33, "c3": 0.331, "c4": 0.67}, {"time": 0.5333, "x": 33.61, "y": -44.22, "curve": 0.155, "c2": 0.33, "c3": 0.485, "c4": 0.67}, {"time": 0.6667, "x": 36.11, "y": -22, "curve": 0, "c2": 0.33, "c3": 0.325, "c4": 0.67}, {"time": 0.8, "y": -1}]}, "shaseng_13": {"rotate": [{"angle": -2.5, "curve": "stepped"}, {"time": 0.0333, "angle": -2.5, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1333, "angle": -5.56, "curve": 0.334, "c2": 0.34, "c3": 0.758}, {"time": 0.2333, "angle": -5.16, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.3333, "angle": 1.48, "curve": 0.089, "c2": 0.52, "c3": 0.364, "c4": 0.95}, {"time": 0.4333, "angle": -5.16, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.5333, "angle": 1.48, "curve": 0.089, "c2": 0.52, "c3": 0.364, "c4": 0.95}, {"time": 0.6667, "angle": -5.16, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.8, "angle": -2.5}]}, "shaseng_14": {"rotate": [{"angle": -5.57, "curve": "stepped"}, {"time": 0.0333, "angle": -5.57, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1333, "angle": -4.87, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.2333, "angle": -10.92, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.3333, "angle": 0.53, "curve": 0.089, "c2": 0.52, "c3": 0.364, "c4": 0.95}, {"time": 0.4333, "angle": -10.92, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.5333, "angle": 0.53, "curve": 0.089, "c2": 0.52, "c3": 0.364, "c4": 0.95}, {"time": 0.7, "angle": -10.92, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.8, "angle": -5.57}]}, "shaseng_15": {"rotate": [{"angle": -14.31, "curve": "stepped"}, {"time": 0.0333, "angle": -14.31, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1667, "angle": -7.24, "curve": 0.37, "c2": 0.48, "c3": 0.753}, {"time": 0.2667, "angle": -23.88, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.3667, "angle": -5.62, "curve": 0.089, "c2": 0.52, "c3": 0.364, "c4": 0.95}, {"time": 0.4667, "angle": -23.88, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.5667, "angle": -5.62, "curve": 0.089, "c2": 0.52, "c3": 0.364, "c4": 0.95}, {"time": 0.7, "angle": -23.88, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.8, "angle": -14.31}]}, "shaseng_16": {"rotate": [{"angle": -10.28, "curve": "stepped"}, {"time": 0.0333, "angle": -10.28, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.2, "angle": 28.34, "curve": 0.334, "c2": 0.34, "c3": 0.758}, {"time": 0.3, "angle": -22.76, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.4, "angle": 9.9, "curve": 0.089, "c2": 0.52, "c3": 0.364, "c4": 0.95}, {"time": 0.5, "angle": -22.76, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.6, "angle": 9.9, "curve": 0.089, "c2": 0.52, "c3": 0.364, "c4": 0.95}, {"time": 0.7333, "angle": -22.76, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.8, "angle": -10.28}]}, "shaseng_17": {"rotate": [{"angle": 2.32, "curve": "stepped"}, {"time": 0.0333, "angle": 2.32, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1333, "angle": -3.32, "curve": 0.358, "c2": 0.42, "c3": 0.756}, {"time": 0.2333, "angle": -0.34, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.3333, "angle": 6.3, "curve": 0.089, "c2": 0.52, "c3": 0.364, "c4": 0.95}, {"time": 0.4333, "angle": -0.34, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.5333, "angle": 6.3, "curve": 0.089, "c2": 0.52, "c3": 0.364, "c4": 0.95}, {"time": 0.6667, "angle": -0.34, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.8, "angle": 2.32}]}, "shaseng_18": {"rotate": [{"angle": 0.61, "curve": "stepped"}, {"time": 0.0333, "angle": 0.61, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1667, "angle": 6.73, "curve": 0.334, "c2": 0.34, "c3": 0.758}, {"time": 0.2667, "angle": -3.48, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.3667, "angle": 7.12, "curve": 0.089, "c2": 0.52, "c3": 0.364, "c4": 0.95}, {"time": 0.4667, "angle": -3.48, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.5667, "angle": 7.12, "curve": 0.089, "c2": 0.52, "c3": 0.364, "c4": 0.95}, {"time": 0.7, "angle": -3.48, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.8, "angle": 0.61}]}, "shaseng_19": {"rotate": [{"angle": -6.09, "curve": "stepped"}, {"time": 0.0333, "angle": -6.09, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.2, "angle": -2.11, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.3, "angle": -14.88, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.4, "angle": 2.85, "curve": 0.089, "c2": 0.52, "c3": 0.364, "c4": 0.95}, {"time": 0.5, "angle": -14.88, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.6, "angle": 2.85, "curve": 0.089, "c2": 0.52, "c3": 0.364, "c4": 0.95}, {"time": 0.7333, "angle": -14.88, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.8, "angle": -6.09}]}, "shaseng_20": {"rotate": [{"angle": -9.23, "curve": "stepped"}, {"time": 0.0333, "angle": -9.23, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.2333, "angle": 5.37, "curve": 0.37, "c2": 0.48, "c3": 0.753}, {"time": 0.3333, "angle": -24.73, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.4333, "angle": 9.96, "curve": 0.089, "c2": 0.52, "c3": 0.364, "c4": 0.95}, {"time": 0.5333, "angle": -24.73, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.6333, "angle": 9.96, "curve": 0.089, "c2": 0.52, "c3": 0.364, "c4": 0.95}, {"time": 0.7667, "angle": -24.73, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.8, "angle": -9.23}]}, "shaseng_22": {"rotate": [{"angle": 0.88, "curve": 0, "c2": 0.39, "c3": 0.246, "c4": 0.77}, {"time": 0.0333, "angle": 6.42, "curve": 0.142, "c2": 0.4, "c3": 0.447, "c4": 0.75}, {"time": 0.1333, "angle": 6.81, "curve": 0.244, "c2": 0.55, "c3": 0.57, "c4": 0.91}, {"time": 0.2, "angle": 2.08, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.2333, "angle": 5.36, "curve": 0, "c2": 0.34, "c3": 0.31, "c4": 0.67}, {"time": 0.4, "angle": 4.81, "curve": 0.096, "c2": 0.4, "c3": 0.385, "c4": 0.76}, {"time": 0.7, "angle": 5.36, "curve": 0, "c2": 0.39, "c3": 0.246, "c4": 0.77}, {"time": 0.8, "angle": 0.88}], "translate": [{"time": 0.0333, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1333, "x": -6.23, "y": 4.66, "curve": "stepped"}, {"time": 0.2, "x": -6.23, "y": 4.66, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.2333}]}, "shaseng_23": {"rotate": [{"angle": 0.91, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": 3.62, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1333, "angle": -26.29, "curve": "stepped"}, {"time": 0.2, "angle": -26.29, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.2333, "angle": 20.06, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.4, "angle": 19.09, "curve": 0.089, "c2": 0.52, "c3": 0.364, "c4": 0.95}, {"time": 0.7, "angle": 20.06, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.8, "angle": 0.91}], "translate": [{"x": -1.1, "y": -0.05, "curve": "stepped"}, {"time": 0.0333, "x": -1.1, "y": -0.05, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1333, "x": 24.53, "y": -19.12, "curve": "stepped"}, {"time": 0.2, "x": 24.53, "y": -19.12, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.2333, "x": -1.1, "y": -0.05}]}, "shaseng_6": {"rotate": [{"angle": 0.4, "curve": "stepped"}, {"time": 0.0333, "angle": 0.4, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1333, "angle": -23.95, "curve": "stepped"}, {"time": 0.2, "angle": -23.95, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.2333, "angle": 0.4}], "translate": [{"x": 0.11, "y": -0.01, "curve": "stepped"}, {"time": 0.0333, "x": 0.11, "y": -0.01, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1333, "x": 7.34, "y": -20.5, "curve": "stepped"}, {"time": 0.2, "x": 7.34, "y": -20.5, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.2333, "x": 0.11, "y": -0.01}]}, "shaseng_3": {"rotate": [{"angle": -1.22, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": 0.16, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.1333, "angle": -5.67, "curve": 0.089, "c2": 0.52, "c3": 0.364, "c4": 0.95}, {"time": 0.2, "angle": 0.94, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.2333, "angle": 0.16, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.3333, "angle": -5.67, "curve": 0.089, "c2": 0.52, "c3": 0.364, "c4": 0.95}, {"time": 0.4333, "angle": 0.16, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.5333, "angle": -5.67, "curve": 0.089, "c2": 0.52, "c3": 0.364, "c4": 0.95}, {"time": 0.7, "angle": 0.16, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.8, "angle": -1.22}]}, "shaseng_4": {"rotate": [{"angle": -3.22, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": -0.52, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.1667, "angle": -11.8, "curve": 0.089, "c2": 0.52, "c3": 0.364, "c4": 0.95}, {"time": 0.2333, "angle": 13.52, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.2667, "angle": -0.52, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.3667, "angle": -11.8, "curve": 0.089, "c2": 0.52, "c3": 0.364, "c4": 0.95}, {"time": 0.4667, "angle": -0.52, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.5667, "angle": -11.8, "curve": 0.089, "c2": 0.52, "c3": 0.364, "c4": 0.95}, {"time": 0.7333, "angle": -0.52, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.8, "angle": -3.22}]}, "shaseng_5": {"rotate": [{"angle": -11.17, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": -4.6, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.2, "angle": -29.78, "curve": 0.089, "c2": 0.52, "c3": 0.364, "c4": 0.95}, {"time": 0.3, "angle": -4.6, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.4, "angle": -29.78, "curve": 0.089, "c2": 0.52, "c3": 0.364, "c4": 0.95}, {"time": 0.5, "angle": -4.6, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.6, "angle": -29.78, "curve": 0.089, "c2": 0.52, "c3": 0.364, "c4": 0.95}, {"time": 0.7667, "angle": -4.6, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.8, "angle": -11.17}]}, "shaseng_7": {"rotate": [{"angle": 0.32, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": 1.61, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.1333, "angle": -4.13, "curve": 0.089, "c2": 0.52, "c3": 0.364, "c4": 0.95}, {"time": 0.2333, "angle": 1.61, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.3333, "angle": -4.13, "curve": 0.089, "c2": 0.52, "c3": 0.364, "c4": 0.95}, {"time": 0.4333, "angle": 1.61, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.5333, "angle": -4.13, "curve": 0.089, "c2": 0.52, "c3": 0.364, "c4": 0.95}, {"time": 0.7, "angle": 1.61, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.8, "angle": 0.32}]}, "shaseng_8": {"rotate": [{"angle": -0.85, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": 3.53, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.1333, "angle": -11.62, "curve": 0.089, "c2": 0.52, "c3": 0.364, "c4": 0.95}, {"time": 0.2, "angle": 13.81, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.2333, "angle": 3.53, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.3333, "angle": -11.62, "curve": 0.089, "c2": 0.52, "c3": 0.364, "c4": 0.95}, {"time": 0.4333, "angle": 3.53, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.5333, "angle": -11.62, "curve": 0.089, "c2": 0.52, "c3": 0.364, "c4": 0.95}, {"time": 0.7, "angle": 3.53, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.8, "angle": -0.85}]}, "shaseng_9": {"rotate": [{"angle": -19.64, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": -7.52, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.1667, "angle": -45.03, "curve": 0.089, "c2": 0.52, "c3": 0.364, "c4": 0.95}, {"time": 0.2333, "angle": 14.97, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.2667, "angle": -7.52, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.3667, "angle": -45.03, "curve": 0.089, "c2": 0.52, "c3": 0.364, "c4": 0.95}, {"time": 0.4667, "angle": -7.52, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.5667, "angle": -45.03, "curve": 0.089, "c2": 0.52, "c3": 0.364, "c4": 0.95}, {"time": 0.7333, "angle": -7.52, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.8, "angle": -19.64}]}, "shaseng_10": {"rotate": [{"angle": -2.78, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": -1.32, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.1333, "angle": -9.88, "curve": 0.089, "c2": 0.52, "c3": 0.364, "c4": 0.95}, {"time": 0.2, "angle": 2.8, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.2333, "angle": -1.32, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.3333, "angle": -9.88, "curve": 0.089, "c2": 0.52, "c3": 0.364, "c4": 0.95}, {"time": 0.4333, "angle": -1.32, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.5333, "angle": -9.88, "curve": 0.089, "c2": 0.52, "c3": 0.364, "c4": 0.95}, {"time": 0.7, "angle": -1.32, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.8, "angle": -2.78}]}, "shaseng_11": {"rotate": [{"angle": -7.55, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": -0.54, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.1333, "angle": -19.7, "curve": 0.089, "c2": 0.52, "c3": 0.364, "c4": 0.95}, {"time": 0.2333, "angle": -0.54, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.3333, "angle": -19.7, "curve": 0.089, "c2": 0.52, "c3": 0.364, "c4": 0.95}, {"time": 0.4333, "angle": -0.54, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.5333, "angle": -19.7, "curve": 0.089, "c2": 0.52, "c3": 0.364, "c4": 0.95}, {"time": 0.7, "angle": -0.54, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.8, "angle": -7.55}]}, "shaseng_21": {"rotate": [{"angle": -6.53, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": 10.07, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.1333, "angle": -24.09, "curve": 0.089, "c2": 0.52, "c3": 0.364, "c4": 0.95}, {"time": 0.2333, "angle": 10.07, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.3333, "angle": -24.09, "curve": 0.089, "c2": 0.52, "c3": 0.364, "c4": 0.95}, {"time": 0.4333, "angle": 10.07, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.5333, "angle": -24.09, "curve": 0.089, "c2": 0.52, "c3": 0.364, "c4": 0.95}, {"time": 0.7, "angle": 10.07, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.8, "angle": -6.53}]}, "shaseng_1": {"rotate": [{"angle": 0.65, "curve": "stepped"}, {"time": 0.0333, "angle": 0.65, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1333, "angle": -3.22, "curve": 0.334, "c2": 0.34, "c3": 0.758}, {"time": 0.2333, "angle": 0.65}]}, "shaseng_2": {"rotate": [{"angle": 5.3}]}, "shaseng_25": {"rotate": [{"angle": -1.32, "curve": "stepped"}, {"time": 0.0333, "angle": -1.32, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1333, "angle": 5.1, "curve": "stepped"}, {"time": 0.2, "angle": 5.1, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.2333, "angle": -1.32}]}, "shaseng_26": {"rotate": [{"angle": -5.94, "curve": "stepped"}, {"time": 0.0333, "angle": -5.94, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1333, "angle": -6.53, "curve": 0.37, "c2": 0.48, "c3": 0.753}, {"time": 0.2333, "angle": -5.94, "curve": "stepped"}, {"time": 0.7, "angle": -5.94, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.7667, "angle": 5.48, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 0.8, "angle": -5.94}]}, "shaseng_27": {"rotate": [{"angle": 1.22, "curve": "stepped"}, {"time": 0.0333, "angle": 1.22, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1333, "angle": 3.77, "curve": 0.334, "c2": 0.34, "c3": 0.758}, {"time": 0.2333, "angle": 1.22}]}, "shaseng_28": {"rotate": [{"angle": -5.72, "curve": "stepped"}, {"time": 0.0333, "angle": -5.72, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1333, "angle": 7.78, "curve": "stepped"}, {"time": 0.2, "angle": 7.78, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.2333, "angle": -5.72}]}, "shaseng_29": {"rotate": [{"angle": -10.42, "curve": "stepped"}, {"time": 0.0333, "angle": -10.42, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1333, "angle": -10.9, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 0.2333, "angle": -10.42}]}, "shaseng_30": {"rotate": [{"angle": -1.53, "curve": "stepped"}, {"time": 0.0333, "angle": -1.53, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1333, "angle": -3.17, "curve": 0.358, "c2": 0.42, "c3": 0.756}, {"time": 0.2333, "angle": -1.53, "curve": "stepped"}, {"time": 0.7, "angle": -1.53, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.7667, "angle": 3.98, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.8, "angle": -1.53}]}, "shaseng_31": {"rotate": [{"angle": 2.24, "curve": "stepped"}, {"time": 0.0333, "angle": 2.24, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1333, "angle": -6.42, "curve": "stepped"}, {"time": 0.2, "angle": -6.42, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.2333, "angle": 2.24}]}, "shaseng_32": {"rotate": [{"angle": 10.17, "curve": "stepped"}, {"time": 0.0333, "angle": 10.17, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1333, "angle": -10.05, "curve": "stepped"}, {"time": 0.2, "angle": -10.05, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.2333, "angle": 10.17}]}, "shaseng_24": {"rotate": [{"angle": -1.59, "curve": "stepped"}, {"time": 0.0333, "angle": -1.59, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1333, "angle": 0.4, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2333, "angle": -1.59}]}, "shaseng_33": {"rotate": [{"angle": -7.82, "curve": "stepped"}, {"time": 0.0333, "angle": -7.82, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1333, "angle": 3.13, "curve": "stepped"}, {"time": 0.2, "angle": 3.13, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.2333, "angle": -7.82}]}, "shaseng_34": {"rotate": [{"angle": -26.92}]}, "shaseng_35": {"rotate": [{"angle": -17.76, "curve": "stepped"}, {"time": 0.0333, "angle": -17.76, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1333, "angle": -25.45, "curve": 0.358, "c2": 0.42, "c3": 0.756}, {"time": 0.2333, "angle": -17.76}]}, "shaseng_36": {"rotate": [{"angle": 0.16, "curve": "stepped"}, {"time": 0.0333, "angle": 0.16, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.1333, "angle": -4.29, "curve": 0.089, "c2": 0.52, "c3": 0.364, "c4": 0.95}, {"time": 0.2333, "angle": 0.16, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.3333, "angle": -4.29, "curve": 0.089, "c2": 0.52, "c3": 0.364, "c4": 0.95}, {"time": 0.4333, "angle": 0.16, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.5333, "angle": -4.29, "curve": 0.089, "c2": 0.52, "c3": 0.364, "c4": 0.95}, {"time": 0.7, "angle": 0.16}]}, "shaseng_37": {"rotate": [{"angle": 2.26, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": 4.84, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.1333, "angle": -5.09, "curve": 0.089, "c2": 0.52, "c3": 0.364, "c4": 0.95}, {"time": 0.2, "angle": -4.6, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.2333, "angle": 4.84, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.3333, "angle": -5.09, "curve": 0.089, "c2": 0.52, "c3": 0.364, "c4": 0.95}, {"time": 0.4333, "angle": 4.84, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.5333, "angle": -5.09, "curve": 0.089, "c2": 0.52, "c3": 0.364, "c4": 0.95}, {"time": 0.7, "angle": 4.84, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.8, "angle": 2.26}]}, "shaseng_38": {"rotate": [{"angle": 10.39, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": 14.45, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.1333, "angle": -10.1, "curve": 0.089, "c2": 0.52, "c3": 0.364, "c4": 0.95}, {"time": 0.2, "angle": -22.74, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.2333, "angle": 14.45, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.3333, "angle": -10.1, "curve": 0.089, "c2": 0.52, "c3": 0.364, "c4": 0.95}, {"time": 0.4333, "angle": 14.45, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.5333, "angle": -10.1, "curve": 0.089, "c2": 0.52, "c3": 0.364, "c4": 0.95}, {"time": 0.7, "angle": 14.45, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.8, "angle": 10.39}]}, "shaseng_39": {"rotate": [{"angle": 12.82, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": 20.61, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.1667, "angle": -21.1, "curve": 0.089, "c2": 0.52, "c3": 0.364, "c4": 0.95}, {"time": 0.2667, "angle": 20.61, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.3667, "angle": -21.1, "curve": 0.089, "c2": 0.52, "c3": 0.364, "c4": 0.95}, {"time": 0.4667, "angle": 20.61, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.5667, "angle": -21.1, "curve": 0.089, "c2": 0.52, "c3": 0.364, "c4": 0.95}, {"time": 0.7333, "angle": 20.61, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.8, "angle": 12.82}]}, "shaseng_40": {"rotate": [{"angle": -3.61, "curve": "stepped"}, {"time": 0.0333, "angle": -3.61, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.1333, "angle": -8.59, "curve": 0.089, "c2": 0.52, "c3": 0.364, "c4": 0.95}, {"time": 0.2333, "angle": -3.61, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.3333, "angle": -8.59, "curve": 0.089, "c2": 0.52, "c3": 0.364, "c4": 0.95}, {"time": 0.4333, "angle": -3.61, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.5333, "angle": -8.59, "curve": 0.089, "c2": 0.52, "c3": 0.364, "c4": 0.95}, {"time": 0.7, "angle": -3.61}]}, "shaseng_41": {"rotate": [{"angle": -5.03, "curve": "stepped"}, {"time": 0.0333, "angle": -5.03, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.1333, "angle": -16.05, "curve": 0.089, "c2": 0.52, "c3": 0.364, "c4": 0.95}, {"time": 0.2333, "angle": -5.03, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.3333, "angle": -16.05, "curve": 0.089, "c2": 0.52, "c3": 0.364, "c4": 0.95}, {"time": 0.4333, "angle": -5.03, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.5333, "angle": -16.05, "curve": 0.089, "c2": 0.52, "c3": 0.364, "c4": 0.95}, {"time": 0.7, "angle": -5.03, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.7667, "angle": 11.8, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.8, "angle": -5.03}]}, "shaseng_42": {"rotate": [{"angle": -3.16, "curve": "stepped"}, {"time": 0.0333, "angle": -3.16, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.1333, "angle": -24.04, "curve": 0.089, "c2": 0.52, "c3": 0.364, "c4": 0.95}, {"time": 0.2333, "angle": -3.16, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.3333, "angle": -24.04, "curve": 0.089, "c2": 0.52, "c3": 0.364, "c4": 0.95}, {"time": 0.4333, "angle": -3.16, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.5333, "angle": -24.04, "curve": 0.089, "c2": 0.52, "c3": 0.364, "c4": 0.95}, {"time": 0.7, "angle": -3.16}]}, "shaseng_43": {"rotate": [{"angle": 13.07, "curve": "stepped"}, {"time": 0.0333, "angle": 13.07, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.1333, "angle": -15.11, "curve": 0.089, "c2": 0.52, "c3": 0.364, "c4": 0.95}, {"time": 0.2, "angle": -14.3, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.2333, "angle": 13.07, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.3333, "angle": -15.11, "curve": 0.089, "c2": 0.52, "c3": 0.364, "c4": 0.95}, {"time": 0.4333, "angle": 13.07, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.5333, "angle": -15.11, "curve": 0.089, "c2": 0.52, "c3": 0.364, "c4": 0.95}, {"time": 0.7, "angle": 13.07}]}, "shaseng_0": {"rotate": [{"angle": 0.31, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": -43.73, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1, "angle": -144.3, "curve": 0.302, "c2": 0.36, "c3": 0.639, "c4": 0.7}, {"time": 0.1333, "angle": 130.33, "curve": "stepped"}, {"time": 0.2, "angle": 130.33, "curve": 0.304, "c2": 0.59, "c3": 0.649}, {"time": 0.2333, "angle": -59.85, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.4, "angle": -60.24, "curve": 0.089, "c2": 0.52, "c3": 0.364, "c4": 0.95}, {"time": 0.7, "angle": -59.85, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.8, "angle": 0.31}], "translate": [{"curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "x": 4.3, "y": 15.79, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1333, "x": 3.54, "y": 15.26, "curve": 0.715, "c3": 0.891, "c4": 0.4}, {"time": 0.2, "x": -1.99, "y": 30.72, "curve": 0.715, "c3": 0.891, "c4": 0.4}, {"time": 0.2333, "x": 5.51, "y": 34.31, "curve": "stepped"}, {"time": 0.7, "x": 5.51, "y": 34.31, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.8}], "scale": [{"time": 0.2, "curve": 0.715, "c3": 0.891, "c4": 0.4}, {"time": 0.2333, "x": 1.033, "y": 1.071, "curve": "stepped"}, {"time": 0.7, "x": 1.033, "y": 1.071, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.8}]}, "shaseng_44": {"rotate": [{"angle": 1.4, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": -3.96, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1333, "angle": 7.09, "curve": 0.715, "c3": 0.891, "c4": 0.4}, {"time": 0.2, "angle": -11.35, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.2333, "angle": -17.34, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.4, "angle": -17.64, "curve": 0.089, "c2": 0.52, "c3": 0.364, "c4": 0.95}, {"time": 0.7, "angle": -17.34, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.8, "angle": 1.4}], "translate": [{"time": 0.1333, "curve": 0.268, "c2": 0.53, "c3": 0.629}, {"time": 0.2333, "x": 34.99, "y": -37.5, "curve": "stepped"}, {"time": 0.7, "x": 34.99, "y": -37.5, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.8}], "scale": [{"time": 0.1333, "curve": 0.268, "c2": 0.53, "c3": 0.629}, {"time": 0.2333, "x": 1.185, "curve": "stepped"}, {"time": 0.7, "x": 1.185, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.8}]}, "shaseng_45": {"rotate": [{"angle": 3.88, "curve": "stepped"}, {"time": 0.0333, "angle": 3.88, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1333, "angle": 3.58, "curve": "stepped"}, {"time": 0.2, "angle": 3.58, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.2333, "angle": -13, "curve": "stepped"}, {"time": 0.7, "angle": -13, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.8, "angle": 3.88}], "translate": [{"time": 0.1333, "curve": 0.372, "c2": 0.64, "c3": 0.768}, {"time": 0.2333, "x": 6.84, "y": -1.4, "curve": "stepped"}, {"time": 0.7, "x": 6.84, "y": -1.4, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.8}]}, "shaseng_47": {"rotate": [{"angle": -0.28, "curve": 0, "c2": 0.34, "c3": 0.306, "c4": 0.67}, {"time": 0.0333, "angle": 0.44, "curve": 0.246, "c2": 0.33, "c3": 0.578, "c4": 0.67}, {"time": 0.0667, "angle": -144.25, "curve": 0.231, "c2": 0.34, "c3": 0.561, "c4": 0.67}, {"time": 0.1333, "angle": 164.37, "curve": 0.244, "c2": 0.55, "c3": 0.57, "c4": 0.91}, {"time": 0.2, "angle": 161.89, "curve": 0.244, "c2": 0.55, "c3": 0.57, "c4": 0.91}, {"time": 0.2333, "angle": -43.46, "curve": "stepped"}, {"time": 0.7, "angle": -43.46, "curve": 0, "c2": 0.34, "c3": 0.306, "c4": 0.67}, {"time": 0.8, "angle": -0.28}], "translate": [{"time": 0.0333, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1333, "x": -1.87, "y": -21.64, "curve": "stepped"}, {"time": 0.2, "x": -1.87, "y": -21.64, "curve": 0.639, "c2": 0.15, "c3": 0.854, "c4": 0.52}, {"time": 0.2333, "x": -9.81, "y": -48.41, "curve": "stepped"}, {"time": 0.7, "x": -9.81, "y": -48.41, "curve": 0.639, "c2": 0.15, "c3": 0.854, "c4": 0.52}, {"time": 0.8}], "scale": [{"time": 0.0333, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1333, "x": 0.969, "y": 0.969, "curve": "stepped"}, {"time": 0.2, "x": 0.969, "y": 0.969, "curve": 0.639, "c2": 0.15, "c3": 0.854, "c4": 0.52}, {"time": 0.2333}]}, "shaseng_48": {"rotate": [{"angle": -1.21, "curve": "stepped"}, {"time": 0.0333, "angle": -1.21, "curve": 0.155, "c2": 0.34, "c3": 0.476, "c4": 0.68}, {"time": 0.1333, "angle": -8.48, "curve": 0.538, "c2": 0.24, "c3": 0.8, "c4": 0.59}, {"time": 0.2, "angle": -13.55, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.2333, "angle": -6.06, "curve": "stepped"}, {"time": 0.7, "angle": -6.06, "curve": 0, "c2": 0.34, "c3": 0.294, "c4": 0.68}, {"time": 0.8, "angle": -1.21}], "translate": [{"time": 0.0333, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1333, "x": -0.3, "y": -4.14, "curve": "stepped"}, {"time": 0.2, "x": -0.3, "y": -4.14, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.2333, "x": -3.2, "y": -1.34, "curve": "stepped"}, {"time": 0.7, "x": -3.2, "y": -1.34, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.8}], "scale": [{"time": 0.1333, "curve": 0.268, "c2": 0.53, "c3": 0.629}, {"time": 0.2333, "x": 1.042, "curve": "stepped"}, {"time": 0.7, "x": 1.042, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.8}]}, "shaseng_49": {"rotate": [{"angle": -0.94, "curve": "stepped"}, {"time": 0.0333, "angle": -0.94, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1333, "angle": -27.53, "curve": "stepped"}, {"time": 0.2, "angle": -27.53, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.2333, "angle": -62.69, "curve": "stepped"}, {"time": 0.7, "angle": -62.69, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.8, "angle": -0.94}], "translate": [{"time": 0.1333, "curve": 0.268, "c2": 0.53, "c3": 0.629}, {"time": 0.2333, "x": 10.63, "y": -4.43, "curve": "stepped"}, {"time": 0.7, "x": 10.63, "y": -4.43, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.8}]}, "shaseng_46": {"rotate": [{"time": 0.2, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.2333, "angle": -40.57, "curve": "stepped"}, {"time": 0.7, "angle": -40.57, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.8}], "translate": [{"time": 0.0333, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1333, "x": 30.96, "y": -197.73, "curve": 0.129, "c2": 0.35, "c3": 0.555}, {"time": 0.2, "x": 37.68, "y": -240.67, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.2333, "x": -34.29, "y": -80.3, "curve": "stepped"}, {"time": 0.7, "x": -34.29, "y": -80.3, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.8}], "scale": [{"time": 0.0333, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1333, "x": 0.946, "y": 0.946, "curve": "stepped"}, {"time": 0.2, "x": 0.946, "y": 0.946, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.2333, "x": 1.04, "curve": "stepped"}, {"time": 0.7, "x": 1.04, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.8}]}, "shaseng_55": {"rotate": [{"angle": -1.93, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": -6.64, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1333, "angle": 11.42, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2333, "angle": -1.93}]}, "shaseng_56": {"rotate": [{"angle": 5.76, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": 14.37, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1333, "angle": 33.52, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2333, "angle": 5.76}]}, "shaseng_59": {"rotate": [{"angle": -1.03, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": -6.06, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1333, "angle": -17.39, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2333, "angle": -1.03}]}, "shaseng_60": {"rotate": [{"angle": 1.88, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.0333, "angle": 8.02, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1333, "angle": 12.69, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2333, "angle": 1.88}]}, "shaseng_63": {"rotate": [{"angle": 0.48, "curve": "stepped"}, {"time": 0.0333, "angle": 0.48, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1333, "angle": -9.17, "curve": "stepped"}, {"time": 0.2, "angle": -9.17, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.2333, "angle": 0.48}], "translate": [{"time": 0.0333, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1333, "x": 19.58, "y": -12.73, "curve": "stepped"}, {"time": 0.2, "x": 19.58, "y": -12.73, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.2333}]}, "shaseng_63b": {"rotate": [{"angle": -11.96, "curve": "stepped"}, {"time": 0.0333, "angle": -11.96, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1333, "angle": 0.69, "curve": "stepped"}, {"time": 0.2, "angle": 0.69, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.2333, "angle": -11.96}]}, "shaseng_63c": {"rotate": [{"angle": -5.86, "curve": "stepped"}, {"time": 0.0333, "angle": -5.86, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1333, "angle": -14.12, "curve": "stepped"}, {"time": 0.2, "angle": -14.12, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.2333, "angle": -5.86}]}, "shaseng_50": {"rotate": [{"angle": 4.94, "curve": "stepped"}, {"time": 0.0333, "angle": 4.94, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1333, "angle": 8.56, "curve": 0.358, "c2": 0.42, "c3": 0.756}, {"time": 0.2333, "angle": 4.94}]}, "shaseng_50b": {"rotate": [{"angle": 3.36, "curve": "stepped"}, {"time": 0.0333, "angle": 3.36, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1333, "angle": -5.23, "curve": 0.334, "c2": 0.34, "c3": 0.758}, {"time": 0.2333, "angle": 3.36}]}, "shaseng_50c": {"rotate": [{"angle": 18.6}]}, "shaseng_64": {"rotate": [{"angle": -2.95, "curve": "stepped"}, {"time": 0.0333, "angle": -2.95, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1333, "angle": 2.61, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.2333, "angle": -2.95}]}, "shaseng_65": {"rotate": [{"angle": -15.45, "curve": "stepped"}, {"time": 0.0333, "angle": -15.45, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1333, "angle": -36.8, "curve": 0.289, "c2": 0.18, "c3": 0.645, "c4": 0.59}, {"time": 0.2333, "angle": -15.45}]}, "shaseng_66": {"rotate": [{"angle": -0.06, "curve": "stepped"}, {"time": 0.0333, "angle": -0.06, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1667, "angle": -0.6, "curve": 0.343, "c2": 0.37, "c3": 0.757}, {"time": 0.2667, "angle": -1.83, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.3667, "angle": 2.53, "curve": 0.089, "c2": 0.52, "c3": 0.364, "c4": 0.95}, {"time": 0.4667, "angle": -1.83, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.5667, "angle": 2.53, "curve": 0.089, "c2": 0.52, "c3": 0.364, "c4": 0.95}, {"time": 0.7, "angle": -1.83, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.8, "angle": -0.06}]}, "shaseng_67": {"rotate": [{"angle": 1.41, "curve": "stepped"}, {"time": 0.0333, "angle": 1.41, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1333, "angle": -2.73, "curve": "stepped"}, {"time": 0.2, "angle": -2.73, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.3, "angle": -2.95, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.4, "angle": 6.1, "curve": 0.089, "c2": 0.52, "c3": 0.364, "c4": 0.95}, {"time": 0.5, "angle": -2.95, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.6, "angle": 6.1, "curve": 0.089, "c2": 0.52, "c3": 0.364, "c4": 0.95}, {"time": 0.7333, "angle": -2.95, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.8, "angle": 1.41}]}, "shaseng_68": {"rotate": [{"angle": 4.47, "curve": "stepped"}, {"time": 0.1333, "angle": 4.47, "curve": 0.377, "c2": 0.51, "c3": 0.748}, {"time": 0.3, "angle": -2.55, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.4, "angle": 12.03, "curve": 0.089, "c2": 0.52, "c3": 0.364, "c4": 0.95}, {"time": 0.5, "angle": -2.55, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.6, "angle": 12.03, "curve": 0.089, "c2": 0.52, "c3": 0.364, "c4": 0.95}, {"time": 0.7333, "angle": -2.55, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.8, "angle": 4.47}]}, "shaseng_69": {"rotate": [{"angle": 7.4, "curve": "stepped"}, {"time": 0.0333, "angle": 7.4, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1333, "angle": 10.01, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.3, "angle": -11.55, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.4, "angle": 22.58, "curve": 0.089, "c2": 0.52, "c3": 0.364, "c4": 0.95}, {"time": 0.5, "angle": -11.55, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.6, "angle": 22.58, "curve": 0.089, "c2": 0.52, "c3": 0.364, "c4": 0.95}, {"time": 0.7667, "angle": -11.55, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.8, "angle": 7.4}]}, "shaseng_70": {"rotate": [{"angle": -0.55, "curve": "stepped"}, {"time": 0.0333, "angle": -0.55, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1333, "angle": -0.67, "curve": 0.346, "c2": 0.38, "c3": 0.7, "c4": 0.78}, {"time": 0.2, "angle": 19.71, "curve": 0.373, "c2": 0.62, "c3": 0.713}, {"time": 0.2333, "angle": -2.32, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.3333, "angle": 2.04, "curve": 0.089, "c2": 0.52, "c3": 0.364, "c4": 0.95}, {"time": 0.4333, "angle": -2.32, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.5333, "angle": 2.04, "curve": 0.089, "c2": 0.52, "c3": 0.364, "c4": 0.95}, {"time": 0.6333, "angle": -2.32, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.8, "angle": -0.55}], "translate": [{"time": 0.0333, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1333, "x": -5.2, "y": -1.32, "curve": "stepped"}, {"time": 0.2, "x": -5.2, "y": -1.32, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.2333}]}, "shaseng_71": {"rotate": [{"angle": -0.36, "curve": "stepped"}, {"time": 0.0333, "angle": -0.36, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1333, "angle": -3.95, "curve": 0.323, "c2": 0.3, "c3": 0.69, "c4": 0.74}, {"time": 0.2, "angle": 25.92, "curve": 0.378, "c2": 0.6, "c3": 0.722}, {"time": 0.2333, "angle": -4.72, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.3333, "angle": 4.33, "curve": 0.089, "c2": 0.52, "c3": 0.364, "c4": 0.95}, {"time": 0.4333, "angle": -4.72, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.5333, "angle": 4.33, "curve": 0.089, "c2": 0.52, "c3": 0.364, "c4": 0.95}, {"time": 0.6667, "angle": -4.72, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.8, "angle": -0.36}]}, "shaseng_72": {"rotate": [{"angle": 2.08, "curve": "stepped"}, {"time": 0.0333, "angle": 2.08, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1333, "angle": -10.54, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -4.14, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.2667, "angle": -4.93, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.3667, "angle": 21.2, "curve": 0.089, "c2": 0.52, "c3": 0.364, "c4": 0.95}, {"time": 0.4667, "angle": -4.93, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.5667, "angle": 21.2, "curve": 0.089, "c2": 0.52, "c3": 0.364, "c4": 0.95}, {"time": 0.7, "angle": -4.93, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.8, "angle": 2.08}]}, "shaseng_73": {"rotate": [{"angle": 11.12, "curve": "stepped"}, {"time": 0.0333, "angle": 11.12, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1333, "angle": -18.49, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2, "angle": -14.95, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.3, "angle": 5.17, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.4, "angle": 27.55, "curve": 0.089, "c2": 0.52, "c3": 0.364, "c4": 0.95}, {"time": 0.5, "angle": 5.17, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.6, "angle": 27.55, "curve": 0.089, "c2": 0.52, "c3": 0.364, "c4": 0.95}, {"time": 0.7333, "angle": 5.17, "curve": 0, "c2": 0.34, "c3": 0.299, "c4": 0.68}, {"time": 0.8, "angle": 11.12}]}, "shaseng_58": {"rotate": [{"time": 0.0333, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1333, "angle": 7.38, "curve": 0.25, "c4": 0.5}, {"time": 0.2333}], "translate": [{"y": 1.63, "curve": "stepped"}, {"time": 0.0333, "y": 1.63, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1333, "x": 6.02, "y": 3.94, "curve": 0.25, "c4": 0.5}, {"time": 0.2333, "y": 1.63}], "scale": [{"time": 0.0333, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1333, "x": 1.06, "curve": 0.25, "c4": 0.5}, {"time": 0.2333}]}, "shaseng_12": {"rotate": [{"angle": -6.08}]}, "daoguang": {"scale": [{"time": 0.0333, "curve": 0.134, "c2": 0.53, "c3": 0.43, "c4": 0.94}, {"time": 0.1333, "x": 0.979, "y": 0.979, "curve": 0.17, "c2": 0.4, "c3": 0.533, "c4": 0.73}, {"time": 0.2, "x": 1.066, "y": 1.066, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.2333, "x": 1.347, "y": 1.347, "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.8}]}, "leg1": {"rotate": [{"time": 0.0333, "curve": 0.283, "c2": 0.35, "c3": 0.615, "c4": 0.68}, {"time": 0.0667, "angle": 15.03, "curve": 0.168, "c2": 0.54, "c3": 0.476, "c4": 0.93}, {"time": 0.1333}], "translate": [{"time": 0.0333, "curve": 0.253, "c2": 0.36, "c3": 0.581, "c4": 0.69}, {"time": 0.0667, "x": -6.67, "y": 205.52, "curve": 0.192, "c2": 0.55, "c3": 0.507, "c4": 0.93}, {"time": 0.1333, "x": -37.04, "y": 691.53, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -42.56, "y": 680.13, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.2333}]}, "leg2": {"translate": [{"time": 0.0333, "curve": 0.253, "c2": 0.36, "c3": 0.581, "c4": 0.69}, {"time": 0.0667, "x": -10.2, "y": 220.53, "curve": 0.192, "c2": 0.55, "c3": 0.507, "c4": 0.93}, {"time": 0.1333, "x": 107.63, "y": 656.28, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": 98.42, "y": 637.42, "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.2333, "x": 91.13, "curve": 0, "c2": 0.35, "c3": 0.286, "c4": 0.69}, {"time": 0.4, "x": 80.27, "curve": "stepped"}, {"time": 0.6667, "x": 80.27, "curve": 0, "c2": 0.39, "c3": 0.246, "c4": 0.77}, {"time": 0.7333, "x": 4.57, "y": 13.62, "curve": 0.244, "c2": 0.55, "c3": 0.57, "c4": 0.91}, {"time": 0.8}]}, "shaseng_77": {"rotate": [{"time": 0.1333}, {"time": 0.2, "angle": -15.98}, {"time": 0.2333}]}, "daoguang1": {"rotate": [{"curve": "stepped"}, {"time": 0.2, "angle": 138.54, "curve": "stepped"}, {"time": 0.2333}], "translate": [{"curve": "stepped"}, {"time": 0.2, "x": 508.84, "y": 246.34, "curve": "stepped"}, {"time": 0.2333}], "scale": [{"curve": "stepped"}, {"time": 0.2, "x": 0.813, "y": 0.367, "curve": "stepped"}, {"time": 0.2333}]}, "diguang_0": {"translate": [{"time": 0.2333, "x": 192.59, "y": -49.61}], "scale": [{"time": 0.2333, "x": 0.872, "y": 1.599}]}, "6_00000": {"translate": [{"time": 0.2333, "x": -63.14, "y": 8.64, "curve": 0.203, "c2": 0.33, "c3": 0.75}, {"time": 0.7333, "x": -72.37, "y": 162.13}], "scale": [{"time": 0.2333, "curve": 0.203, "c2": 0.33, "c3": 0.75}, {"time": 0.7333, "x": 3.015, "y": 6.019}]}, "atu2": {"translate": [{"time": 0.2333, "x": -59.05, "y": -40.82}], "scale": [{"time": 0.2333, "x": 0.249, "y": 0.249, "curve": 0.203, "c2": 0.61, "c3": 0.75}, {"time": 0.4333, "x": 2.706, "y": 2.706}]}, "all0": {"translate": [{"time": 0.2333, "x": -76, "y": -64}]}, "xuewu9": {"rotate": [{"time": 0.3, "curve": 0, "c2": 0.32, "c3": 0.343, "c4": 0.66}, {"time": 0.5333, "angle": -161.7}], "translate": [{"time": 0.3, "curve": 0, "c2": 0.41, "c3": 0.451}, {"time": 0.5333, "x": 55.84, "y": 525.54}], "scale": [{"time": 0.3, "x": 4, "y": 4, "curve": 0, "c2": 0.41, "c3": 0.451}, {"time": 0.5333, "x": 40, "y": 40}]}, "a1": {"scale": [{"time": 0.2333, "curve": 0, "c2": 0.26, "c3": 0.368, "c4": 0.61}, {"time": 0.4, "x": 18.681, "y": 18.681}]}, "xuewu10": {"translate": [{"time": 0.3, "curve": 0, "c2": 0.41, "c3": 0.451}, {"time": 0.6, "x": 32.04, "y": -424.07}], "scale": [{"time": 0.3, "x": 4, "y": 4, "curve": 0, "c2": 0.41, "c3": 0.451}, {"time": 0.6, "x": 50, "y": 50}]}, "xuewu11": {"rotate": [{"time": 0.3, "curve": 0, "c2": 0.32, "c3": 0.613, "c4": 0.93}, {"time": 0.7, "angle": 114.18}], "translate": [{"time": 0.3, "curve": 0, "c2": 0.41, "c3": 0.42, "c4": 0.93}, {"time": 0.7, "x": -352.42, "y": -95.47}], "scale": [{"time": 0.3, "x": 4, "y": 4, "curve": 0, "c2": 0.41, "c3": 0.451}, {"time": 0.7, "x": 40, "y": 40}]}, "xuewu12": {"rotate": [{"time": 0.3, "curve": 0, "c2": 0.32, "c3": 0.343, "c4": 0.66}, {"time": 0.7, "angle": -161.7}], "translate": [{"time": 0.3, "curve": 0, "c2": 0.41, "c3": 0.451}, {"time": 0.7, "x": 555.72, "y": -194.16}], "scale": [{"time": 0.3, "x": 4, "y": 4, "curve": 0, "c2": 0.41, "c3": 0.451}, {"time": 0.7, "x": 40, "y": 40}]}, "lanse": {"translate": [{"time": 0.2333, "x": 8.56, "y": -2.76, "curve": "stepped"}, {"time": 0.5333, "x": 8.56, "y": -2.76, "curve": "stepped"}, {"time": 0.5667, "x": -5.5, "y": 27.04, "curve": 0, "c2": 0.21, "c3": 0.75}, {"time": 0.7, "x": -5.5, "y": 244}], "scale": [{"time": 0.2333, "x": 6, "y": 6, "curve": 0, "c2": 0.21, "c3": 0.75}, {"time": 0.5333, "x": 25, "y": 25, "curve": "stepped"}, {"time": 0.5667, "curve": 0, "c2": 0.21, "c3": 0.75}, {"time": 0.7, "y": 18}]}, "xuewu7": {"rotate": [{"time": 0.3, "curve": 0, "c2": 0.32, "c3": 0.343, "c4": 0.66}, {"time": 0.5667, "angle": -161.7}], "translate": [{"time": 0.3, "curve": 0, "c2": 0.41, "c3": 0.451}, {"time": 0.5667, "x": 464.53, "y": 412.11}], "scale": [{"time": 0.3, "x": 4, "y": 4, "curve": 0, "c2": 0.41, "c3": 0.451}, {"time": 0.5667, "x": 40, "y": 40}]}, "bing2": {"scale": [{"time": 0.2333}, {"time": 0.3, "x": 14.227, "y": 14.227}]}, "xuewu8": {"rotate": [{"time": 0.3, "curve": 0, "c2": 0.32, "c3": 0.343, "c4": 0.66}, {"time": 0.5333, "angle": 114.18}], "translate": [{"time": 0.3, "curve": 0, "c2": 0.41, "c3": 0.451}, {"time": 0.5333, "x": -306.43, "y": 438.85}], "scale": [{"time": 0.3, "x": 4, "y": 4, "curve": 0, "c2": 0.41, "c3": 0.451}, {"time": 0.5333, "x": 40, "y": 40}]}, "b": {"scale": [{"time": 0.2333, "curve": "stepped"}, {"time": 0.2667, "x": 2.94, "y": 2.94, "curve": 0.223, "c2": 0.56, "c3": 0.761}, {"time": 0.6, "x": 33.45, "y": 33.45}]}, "bing3": {"scale": [{"time": 0.2333}, {"time": 0.3333, "x": 19.803, "y": 19.803}, {"time": 0.8, "x": 49.802, "y": 49.802}]}, "bing": {"translate": [{"time": 0.2333, "curve": 0, "c2": 0.29, "c3": 0.75}, {"time": 0.3333, "x": 63.29}], "scale": [{"time": 0.2333, "x": 8.612, "y": 8.612, "curve": 0.125, "c2": 0.25, "c3": 0.531}, {"time": 0.3333, "x": 36.479, "y": 36.479}, {"time": 0.8, "x": 73.27, "y": 73.27}]}, "fasan": {"scale": [{"time": 0.2333, "x": 0.316, "y": 0.451, "curve": 0.157, "c2": 0.37, "c3": 0.74, "c4": 0.95}, {"time": 0.6, "x": 2.113, "y": 2.466}]}, "dilie1s": {"scale": [{"time": 0.2333, "curve": 0.172, "c2": 0.53, "c3": 0.75}, {"time": 0.2667, "x": 2.261, "y": 1.491}]}, "ag1": {"rotate": [{"time": 0.2333, "curve": "stepped"}, {"time": 0.2667, "angle": 12.89, "curve": "stepped"}, {"time": 0.3, "angle": -70.94}], "translate": [{"time": 0.2333, "x": 132, "y": 5, "curve": "stepped"}, {"time": 0.3, "x": -29.88, "y": -140.11}], "scale": [{"time": 0.2333, "x": 1.353, "y": 1.255, "curve": "stepped"}, {"time": 0.2667, "x": 1.196, "y": 0.912}]}, "shaseng_78": {"translate": [{"time": 0.2333, "curve": 0.188, "c2": 0.41, "c3": 0.75}, {"time": 0.5667, "x": 1023.85, "y": 27.96}], "scale": [{"time": 0.2333, "curve": 0.188, "c2": 0.41, "c3": 0.75}, {"time": 0.5667, "x": 3.251, "y": 3.251}]}, "asansheguang222": {"scale": [{"time": 0.2333, "x": 4.059, "y": 4.059, "curve": 0, "c2": 0.27, "c3": 0.627, "c4": 0.98}, {"time": 0.3667, "x": 24.954, "y": 24.954}], "shear": [{"time": 0.2333, "y": 1}]}, "01_00": {"scale": [{"time": 0.2667, "x": 5.584, "y": 5.584}]}, "01_0": {"rotate": [{"time": 0.2667, "angle": 7.05}], "translate": [{"time": 0.2667, "x": -22.15}], "scale": [{"time": 0.2667, "x": 4.309, "y": -4.024}]}, "01_1": {"rotate": [{"time": 0.2667, "angle": -9.15}], "translate": [{"time": 0.2667, "x": 4.17}], "scale": [{"time": 0.2667, "x": 3.894, "y": 3.894}]}}, "deform": {"default": {"shaseng_0": {"shaseng_0": [{"offset": 54, "vertices": [-0.03646, -0.34388, 0.10032, -0.33095, -0.07283, -0.68767, -0.07283, -0.68767, 0, 0, 0, 0, 0.20083, 2.08921, 0.12687, 1.00017, 0.12687, 1.00017, -0.12721, 1.02398, -0.12721, 1.02398, -0.71556, 2.38928, -1.80434, 1.71917], "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.1333, "offset": 66, "vertices": [-0.00989, -1.19092, 1.76117, -2.99219, -3.01178, -4.1427, 1.3988, -4.92688, -0.46216, -3.44116, 0.28113, -2.51038, 1.66516, -1.89938], "curve": "stepped"}, {"time": 0.2, "offset": 66, "vertices": [-0.00989, -1.19092, 1.76117, -2.99219, -3.01178, -4.1427, 1.3988, -4.92688, -0.46216, -3.44116, 0.28113, -2.51038, 1.66516, -1.89938], "curve": 0.715, "c3": 0.891, "c4": 0.4}, {"time": 0.2333, "offset": 54, "vertices": [-0.03646, -0.34388, 0.10032, -0.33095, -0.07283, -0.68767, -0.07283, -0.68767, 0, 0, 0, 0, 0.20083, 2.08921, 0.12687, 1.00017, 0.12687, 1.00017, -0.12721, 1.02398, -0.12721, 1.02398, -0.71556, 2.38928, -1.80434, 1.71917]}]}, "shaseng_9": {"shaseng_9": [{"curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.1333, "offset": 6, "vertices": [-2.38416, 3.77466, -10.33405, 3.17566, -10.71179, 8.79553, -9.44229, 13.69397, -9.2923, 7.49048, -9.80225, 11.12439, -9.80225, 11.12439, -9.80225, 11.12439, -9.80225, 11.12439, -8.74341, 7.24951, -2.38416, 3.77466, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.38416, 3.77466, -8.74341, 7.24951, -10.61719, 7.39038, 0, 0, 0, 0, -2.38416, 3.77466, -8.74341, 7.24951, -10.9389, 8.21362, 0, 0, 0, 0, -2.38416, 3.77466, -8.74341, 7.24951, -9.80225, 11.12439], "curve": 0.25, "c4": 0.5}, {"time": 0.2333}]}, "shaseng_12": {"shaseng_12": [{"offset": 38, "vertices": [-0.15173, 2.96886, 0.10692, 2.97074, -0.10624, 2.07813, 0.07481, 2.07948, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.13476, -0.00691, -0.32353, -0.01655, -0.31803, 0.10331, 0.33301, -1.76637, 0.50324, 1.49926, -0.17594, -0.90179, -0.25176, -0.85232, 0.41558, 0.02124, 0.42157, 0.07574, -0.25341, -0.01296, -0.24782, 0.10075, 0.29612, 0.01514, 0.30013, 0.04975, 0, 0, 0, 0, 0.29615, 0.01514, 0.30013, 0.04971], "curve": "stepped"}, {"time": 0.0333, "offset": 38, "vertices": [-0.15173, 2.96886, 0.10692, 2.97074, -0.10624, 2.07813, 0.07481, 2.07948, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.13476, -0.00691, -0.32353, -0.01655, -0.31803, 0.10331, 0.33301, -1.76637, 0.50324, 1.49926, -0.17594, -0.90179, -0.25176, -0.85232, 0.41558, 0.02124, 0.42157, 0.07574, -0.25341, -0.01296, -0.24782, 0.10075, 0.29612, 0.01514, 0.30013, 0.04975, 0, 0, 0, 0, 0.29615, 0.01514, 0.30013, 0.04971], "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.1333, "offset": 38, "vertices": [-0.15173, 2.96886, 0.10692, 2.97074, -0.10624, 2.07813, 0.07481, 2.07948, 7.15027, -19.98091, 7.15027, -19.98091, 7.15027, -19.98091, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.13476, -0.00691, -0.32353, -0.01655, -0.31803, 0.10331, 0.33301, -1.76637, 4.07734, -8.49119, -0.17594, -0.90179, -0.25176, -0.85232, 0.41558, 0.02124, 0.42157, 0.07574, -0.25341, -0.01296, -0.24782, 0.10075, 0.29612, 0.01514, 0.30013, 0.04975, 0, 0, 0, 0, 0.29615, 0.01514, 0.30013, 0.04971], "curve": "stepped"}, {"time": 0.7, "offset": 38, "vertices": [-0.15173, 2.96886, 0.10692, 2.97074, -0.10624, 2.07813, 0.07481, 2.07948, 7.15027, -19.98091, 7.15027, -19.98091, 7.15027, -19.98091, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.13476, -0.00691, -0.32353, -0.01655, -0.31803, 0.10331, 0.33301, -1.76637, 4.07734, -8.49119, -0.17594, -0.90179, -0.25176, -0.85232, 0.41558, 0.02124, 0.42157, 0.07574, -0.25341, -0.01296, -0.24782, 0.10075, 0.29612, 0.01514, 0.30013, 0.04975, 0, 0, 0, 0, 0.29615, 0.01514, 0.30013, 0.04971], "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.8, "offset": 38, "vertices": [-0.15173, 2.96886, 0.10692, 2.97074, -0.10624, 2.07813, 0.07481, 2.07948, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.13476, -0.00691, -0.32353, -0.01655, -0.31803, 0.10331, 0.33301, -1.76637, 0.50324, 1.49926, -0.17594, -0.90179, -0.25176, -0.85232, 0.41558, 0.02124, 0.42157, 0.07574, -0.25341, -0.01296, -0.24782, 0.10075, 0.29612, 0.01514, 0.30013, 0.04975, 0, 0, 0, 0, 0.29615, 0.01514, 0.30013, 0.04971]}]}, "shaseng_13": {"shaseng_13": [{"offset": 40, "vertices": [-83.11365, -48.45935, -72.98035, -9.20032, -67.24121, 4.88055, -59.9043, 32.10095], "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.1333, "offset": 40, "vertices": [-85.29834, -54.31567, -75.16504, -15.05664, -69.4259, -0.97577, -64.56152, 27.9325], "curve": "stepped"}, {"time": 0.2, "offset": 40, "vertices": [-85.29834, -54.31567, -75.16504, -15.05664, -69.4259, -0.97577, -64.56152, 27.9325], "curve": 0.405, "c2": -0.05, "c4": 0.16}, {"time": 0.2333, "offset": 40, "vertices": [-83.11365, -48.45935, -72.98035, -9.20032, -67.24121, 4.88055, -59.9043, 32.10095]}]}, "shaseng_16": {"shaseng_16": [{"offset": 110, "vertices": [0.46622, -2.23144, 1.5949, -1.62874, 0.46622, -2.23144, 1.5949, -1.62874, 0.46622, -2.23144], "curve": "stepped"}, {"time": 0.0333, "offset": 110, "vertices": [0.46622, -2.23144, 1.5949, -1.62874, 0.46622, -2.23144, 1.5949, -1.62874, 0.46622, -2.23144], "curve": 0, "c2": 0.49, "c3": 0.218, "c4": 0.95}, {"time": 0.1333, "offset": 110, "vertices": [0.05134, -0.24571, 0.17562, -0.17935, 0.05134, -0.24571, 0.17562, -0.17935, 0.05134, -0.24571], "curve": "stepped"}, {"time": 0.2, "offset": 110, "vertices": [0.05134, -0.24571, 0.17562, -0.17935, 0.05134, -0.24571, 0.17562, -0.17935, 0.05134, -0.24571], "curve": 0.639, "c2": 0.15, "c3": 0.854, "c4": 0.52}, {"time": 0.2333, "offset": 110, "vertices": [0.46622, -2.23144, 1.5949, -1.62874, 0.46622, -2.23144, 1.5949, -1.62874, 0.46622, -2.23144]}]}, "shaseng_21": {"wuqi": [{"vertices": [3.35135, 4.41537, 3.35135, 4.41537, 3.35135, 4.41537, 3.35135, 4.41537]}]}}}, "drawOrder": [{"time": 0.5667, "offsets": [{"slot": "lanse", "offset": 25}]}]}, "idle": {"slots": {"daoguang": {"attachment": [{"name": null}]}, "daoying1": {"attachment": [{"name": null}]}}, "bones": {"body": {"translate": [{"y": -1, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "y": 1, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "y": -1}]}, "shaseng_13": {"rotate": [{"angle": -2.5, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": -3.96, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.3333, "angle": -2.5}]}, "shaseng_14": {"rotate": [{"angle": -5.57, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": -5.82, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 1.3333, "angle": -5.57}]}, "shaseng_15": {"rotate": [{"angle": -14.31, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1333, "angle": -16.45, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.3333, "angle": -14.31}]}, "shaseng_16": {"rotate": [{"angle": -10.28, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "angle": -20.57, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "angle": -10.28}]}, "shaseng_17": {"rotate": [{"angle": 2.32, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.2, "angle": 3.06, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1.3333, "angle": 2.32}]}, "shaseng_18": {"rotate": [{"angle": 0.61, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.4, "angle": 5.67, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": -2.34, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.3333, "angle": 0.61}]}, "shaseng_19": {"rotate": [{"angle": -6.09, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.6, "angle": 8.42, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": -6.74, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 1.3333, "angle": -6.09}]}, "shaseng_20": {"rotate": [{"angle": -9.23, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1333, "angle": -12.07, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": 9.81, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.3333, "angle": -9.23}]}, "shaseng_22": {"rotate": [{"angle": 0.88}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6667, "y": 1.28, "curve": 0.25, "c3": 0.75}, {"time": 1.3333}]}, "shaseng_23": {"rotate": [{"angle": 0.91, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1333, "angle": 1.42, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": -2.47, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.3333, "angle": 0.91}], "translate": [{"x": -1.1, "y": -0.05, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1333, "x": -1.5, "y": -0.08, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "x": 1.61, "y": 0.11, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.3333, "x": -1.1, "y": -0.05}]}, "shaseng_6": {"rotate": [{"angle": 0.4, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2667, "angle": 0.92, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": -0.5, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.3333, "angle": 0.4}], "translate": [{"x": 0.11, "y": -0.01, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "x": 0.3, "y": -0.02, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.3333, "x": 0.11, "y": -0.01}]}, "shaseng_3": {"rotate": [{"angle": -1.22, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.4, "angle": 0.94, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": -2.47, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.3333, "angle": -1.22}]}, "shaseng_4": {"rotate": [{"angle": -3.22, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.5333, "angle": 13.52, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": -5.72, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.3333, "angle": -3.22}]}, "shaseng_5": {"rotate": [{"angle": -11.17, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 17.62, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -11.17}]}, "shaseng_7": {"rotate": [{"angle": 0.32, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2667, "angle": 1.88, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": -2.34, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.3333, "angle": 0.32}]}, "shaseng_8": {"rotate": [{"angle": -0.85, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.4, "angle": 13.81, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": -9.37, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.3333, "angle": -0.85}]}, "shaseng_9": {"rotate": [{"angle": -19.64, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.5333, "angle": 14.97, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": -24.82, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.3333, "angle": -19.64}]}, "shaseng_10": {"rotate": [{"angle": -2.78, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.5333, "angle": 2.8, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": -3.61, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.3333, "angle": -2.78}]}, "shaseng_11": {"rotate": [{"angle": -7.55, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 10, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -7.55}]}, "shaseng_21": {"rotate": [{"angle": -6.53, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1333, "angle": -10.13, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": 17.49, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.3333, "angle": -6.53}]}, "shaseng_1": {"rotate": [{"angle": 0.65, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "angle": -4.11, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 5.4, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "angle": 0.65}]}, "shaseng_2": {"rotate": [{"angle": 5.3, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -5.55, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 5.3}]}, "shaseng_25": {"rotate": [{"angle": -1.32, "curve": 0.354, "c2": 0.41, "c3": 0.756}, {"time": 0.4333, "angle": 5.1, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "angle": -4.13, "curve": 0.263, "c3": 0.618, "c4": 0.43}, {"time": 1.3333, "angle": -1.32}]}, "shaseng_26": {"rotate": [{"angle": -5.94, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.1, "angle": -6.98, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 5.48, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 1.3333, "angle": -5.94}]}, "shaseng_27": {"rotate": [{"angle": 1.22, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "angle": 4.36, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -1.91, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "angle": 1.22}]}, "shaseng_28": {"rotate": [{"angle": -5.72, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.5333, "angle": 7.78, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": -7.73, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.3333, "angle": -5.72}]}, "shaseng_29": {"rotate": [{"angle": -10.42, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.0667, "angle": -11.31, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": 9.61, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 1.3333, "angle": -10.42}]}, "shaseng_30": {"rotate": [{"angle": -1.53, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2333, "angle": -3.95, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": 3.98, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 1.3333, "angle": -1.53}]}, "shaseng_31": {"rotate": [{"angle": 2.24, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.4, "angle": -6.42, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": 7.29, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.3333, "angle": 2.24}]}, "shaseng_32": {"rotate": [{"angle": 10.17, "curve": 0.306, "c2": 0.23, "c3": 0.756}, {"time": 0.5667, "angle": -10.05, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "angle": 12, "curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 1.3333, "angle": 10.17}]}, "shaseng_24": {"rotate": [{"angle": -1.59, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2667, "angle": 1.16, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": -6.33, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.3333, "angle": -1.59}]}, "shaseng_33": {"rotate": [{"angle": -7.82, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.4667, "angle": 3.13, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": -11.32, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 1.3333, "angle": -7.82}]}, "shaseng_34": {"rotate": [{"angle": -26.92, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 8.29, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -26.92}]}, "shaseng_35": {"rotate": [{"angle": -17.76, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.2, "angle": -29.11, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": 17.72, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1.3333, "angle": -17.76}]}, "shaseng_36": {"rotate": [{"angle": 0.16, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "angle": -1.54, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 1.86, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "angle": 0.16}]}, "shaseng_37": {"rotate": [{"angle": 2.26, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.4667, "angle": -4.6, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": 4.45, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 1.3333, "angle": 2.26}]}, "shaseng_38": {"rotate": [{"angle": 10.39, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.6, "angle": -12.76, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": 11.42, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 1.3333, "angle": 10.39}]}, "shaseng_39": {"rotate": [{"angle": 12.82, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.0667, "angle": 14.15, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": -17.23, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 1.3333, "angle": 12.82}]}, "shaseng_40": {"rotate": [{"angle": -3.61, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 3.84, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -3.61}]}, "shaseng_41": {"rotate": [{"angle": -5.03, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1333, "angle": -7.55, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": 11.8, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.3333, "angle": -5.03}]}, "shaseng_42": {"rotate": [{"angle": -3.16, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2667, "angle": -16.45, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 19.66, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.3333, "angle": -3.16}]}, "shaseng_43": {"rotate": [{"angle": 13.07, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.4, "angle": -14.3, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": 29, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.3333, "angle": 13.07}]}, "shaseng_0": {"rotate": [{"angle": 0.31, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2667, "angle": -1.22, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 2.95, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.3333, "angle": 0.31}]}, "shaseng_44": {"rotate": [{"angle": 1.4, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.4, "angle": -3, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": 3.96, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.3333, "angle": 1.4}]}, "shaseng_45": {"rotate": [{"angle": 3.88, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.5333, "angle": -6.16, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": 5.39, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.3333, "angle": 3.88}]}, "shaseng_47": {"rotate": [{"angle": -0.28, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.4, "angle": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": -0.68, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.3333, "angle": -0.28}]}, "shaseng_48": {"rotate": [{"angle": -1.21, "curve": 0.306, "c2": 0.23, "c3": 0.756}, {"time": 0.5667, "angle": 0.76, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "angle": -1.39, "curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 1.3333, "angle": -1.21}]}, "shaseng_49": {"rotate": [{"angle": -0.94, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.0667, "angle": -1, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": 0.48, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 1.3333, "angle": -0.94}]}, "shaseng_59": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6667, "y": 5.78, "curve": 0.25, "c3": 0.75}, {"time": 1.3333}]}, "shaseng_63": {"rotate": [{"angle": 0.48, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.3, "angle": 0.85, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "curve": 0.247, "c3": 0.63, "c4": 0.53}, {"time": 1.3333, "angle": 0.48}]}, "shaseng_63b": {"rotate": [{"angle": -11.96, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.6, "angle": 5.84, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": -12.75, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 1.3333, "angle": -11.96}]}, "shaseng_63c": {"rotate": [{"angle": -5.86, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2333, "angle": -12.75, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": 9.89, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 1.3333, "angle": -5.86}]}, "shaseng_50": {"rotate": [{"angle": 4.94, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.2, "angle": 10.27, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": -11.73, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1.3333, "angle": 4.94}]}, "shaseng_50b": {"rotate": [{"angle": 3.36, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "angle": -7.2, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 13.92, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "angle": 3.36}]}, "shaseng_50c": {"rotate": [{"angle": 18.6, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -11.14, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 18.6}]}, "shaseng_64": {"rotate": [{"angle": -2.95, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.1667, "angle": 5.8, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -41.64, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1.3333, "angle": -2.95}]}, "shaseng_65": {"rotate": [{"angle": -15.45, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "angle": -39.69, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 8.78, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "angle": -15.45}]}, "shaseng_66": {"rotate": [{"angle": -0.06, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.3, "angle": -0.76, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": 0.84, "curve": 0.247, "c3": 0.63, "c4": 0.53}, {"time": 1.3333, "angle": -0.06}]}, "shaseng_67": {"rotate": [{"angle": 1.41, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.4667, "angle": -2.73, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": 2.74, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 1.3333, "angle": 1.41}]}, "shaseng_68": {"rotate": [{"angle": 4.47, "curve": 0.271, "c2": 0.09, "c3": 0.753}, {"time": 0.6333, "angle": -5.95, "curve": 0.25, "c3": 0.75}, {"time": 1.3, "angle": 4.65, "curve": 0.318, "c3": 0.652, "c4": 0.34}, {"time": 1.3333, "angle": 4.47}]}, "shaseng_69": {"rotate": [{"angle": 7.4, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.1667, "angle": 11.5, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -10.7, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1.3333, "angle": 7.4}]}, "shaseng_70": {"rotate": [{"angle": -0.55, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1333, "angle": -0.76, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": 0.84, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.3333, "angle": -0.55}]}, "shaseng_71": {"rotate": [{"angle": -0.36, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.3, "angle": -2.73, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": 2.74, "curve": 0.247, "c3": 0.63, "c4": 0.53}, {"time": 1.3333, "angle": -0.36}]}, "shaseng_72": {"rotate": [{"angle": 2.08, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.4667, "angle": -5.95, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": 4.65, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 1.3333, "angle": 2.08}]}, "shaseng_73": {"rotate": [{"angle": 11.12, "curve": 0.271, "c2": 0.09, "c3": 0.753}, {"time": 0.6333, "angle": -10.7, "curve": 0.25, "c3": 0.75}, {"time": 1.3, "angle": 11.5, "curve": 0.318, "c3": 0.652, "c4": 0.34}, {"time": 1.3333, "angle": 11.12}]}, "shaseng_58": {"translate": [{"y": 1.63, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "y": 4.42, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.3333, "y": 1.63}]}, "shaseng_12": {"rotate": [{"angle": -6.08, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 7.74, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -6.08}]}}, "deform": {"default": {"shaseng_0": {"shaseng_0": [{"offset": 54, "vertices": [-0.03646, -0.34388, 0.10032, -0.33095, -0.07283, -0.68767, -0.07283, -0.68767, 0, 0, 0, 0, 0.20083, 2.08921, 0.12687, 1.00017, 0.12687, 1.00017, -0.12721, 1.02398, -0.12721, 1.02398, -0.71556, 2.38928, -1.80434, 1.71917], "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2667, "offset": 66, "vertices": [0.03601, 0.46753, -0.19629, -2.34314, -0.19629, -2.34314, 0.93073, -2.15979, 0.93073, -2.15979], "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "offset": 54, "vertices": [-0.09912, -0.93481, 0.27271, -0.89966, -0.198, -1.86938, -0.198, -1.86938, 0, 0, 0, 0, 0.48407, 4.87598, 0.68219, 6.74548, 0.68219, 6.74548, -1.94522, 6.49512, -1.94522, 6.49512, -1.94522, 6.49512, -4.90501, 4.67346], "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.3333, "offset": 54, "vertices": [-0.03646, -0.34388, 0.10032, -0.33095, -0.07283, -0.68767, -0.07283, -0.68767, 0, 0, 0, 0, 0.20083, 2.08921, 0.12687, 1.00017, 0.12687, 1.00017, -0.12721, 1.02398, -0.12721, 1.02398, -0.71556, 2.38928, -1.80434, 1.71917]}]}, "shaseng_12": {"shaseng_12": [{"offset": 38, "vertices": [-0.15173, 2.96886, 0.10692, 2.97074, -0.10624, 2.07813, 0.07481, 2.07948, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.13476, -0.00691, -0.32353, -0.01655, -0.31803, 0.10331, 0.33301, -1.76637, 0.50324, 1.49926, -0.17594, -0.90179, -0.25176, -0.85232, 0.41558, 0.02124, 0.42157, 0.07574, -0.25341, -0.01296, -0.24782, 0.10075, 0.29612, 0.01514, 0.30013, 0.04975, 0, 0, 0, 0, 0.29615, 0.01514, 0.30013, 0.04971], "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2667, "offset": 62, "vertices": [-1.67609, -0.08569, -2.5141, -0.12854, -2.50647, 0.23816, -0.83545, 0.07947, -0.83545, 0.07947, -0.83801, -0.04279, -0.83545, 0.07947, -2.5141, -0.12854, -2.50647, 0.23816, -2.5141, -0.12854, -2.50647, 0.23816, -1.67609, -0.08569, -1.67102, 0.15875, 0, 0, 0, 0, -1.67603, -0.08569, -1.67102, 0.15869], "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "offset": 38, "vertices": [-0.41248, 8.07068, 0.29065, 8.07581, -0.28882, 5.64929, 0.20337, 5.65295, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.51392, 0.12848, 3.44086, 0.1759, 3.44269, -0.12842, 2.34094, -4.93835, 2.80371, 3.93909, 0.96179, -2.37793, 0.75128, -2.45355, 5.45007, 0.27863, 5.45325, -0.20337, 3.63147, 0.18567, 3.63354, -0.13538, 3.68524, 0.18842, 3.68744, -0.13757, 0, 0, 0, 0, 3.68524, 0.18842, 3.68744, -0.13757], "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.3333, "offset": 38, "vertices": [-0.15173, 2.96886, 0.10692, 2.97074, -0.10624, 2.07813, 0.07481, 2.07948, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.13476, -0.00691, -0.32353, -0.01655, -0.31803, 0.10331, 0.33301, -1.76637, 0.50324, 1.49926, -0.17594, -0.90179, -0.25176, -0.85232, 0.41558, 0.02124, 0.42157, 0.07574, -0.25341, -0.01296, -0.24782, 0.10075, 0.29612, 0.01514, 0.30013, 0.04975, 0, 0, 0, 0, 0.29615, 0.01514, 0.30013, 0.04971]}]}, "shaseng_13": {"shaseng_13": [{"offset": 40, "vertices": [-83.11365, -48.45935, -72.98035, -9.20032, -67.24121, 4.88055, -59.9043, 32.10095]}]}, "shaseng_16": {"shaseng_16": [{"offset": 110, "vertices": [0.46622, -2.23144, 1.5949, -1.62874, 0.46622, -2.23144, 1.5949, -1.62874, 0.46622, -2.23144], "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "offset": 110, "vertices": [0.73752, -3.52997, 2.52301, -2.57654, 0.73752, -3.52997, 2.52301, -2.57654, 0.73752, -3.52997], "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.3333, "offset": 110, "vertices": [0.46622, -2.23144, 1.5949, -1.62874, 0.46622, -2.23144, 1.5949, -1.62874, 0.46622, -2.23144]}]}}}}, "jump2": {"slots": {"a_canying": {"attachment": [{"time": 0.1667, "name": "a_canying"}, {"time": 0.3, "name": null}]}, "daoguang": {"attachment": [{"name": null}]}, "daoying1": {"attachment": [{"name": null}]}, "shaseng_0": {"attachment": [{"time": 0.1667, "name": null}, {"time": 0.3, "name": "shaseng_0"}]}, "shaseng_1": {"attachment": [{"time": 0.1667, "name": null}, {"time": 0.3, "name": "shaseng_1"}]}, "shaseng_2": {"attachment": [{"time": 0.1667, "name": null}, {"time": 0.3, "name": "shaseng_2"}]}, "shaseng_3": {"attachment": [{"time": 0.1667, "name": null}, {"time": 0.3, "name": "shaseng_3"}]}, "shaseng_4": {"attachment": [{"time": 0.1667, "name": null}, {"time": 0.3, "name": "shaseng_4"}]}, "shaseng_5": {"attachment": [{"time": 0.1667, "name": null}, {"time": 0.3, "name": "shaseng_5"}]}, "shaseng_6": {"attachment": [{"time": 0.1667, "name": null}, {"time": 0.3, "name": "shaseng_6"}]}, "shaseng_7": {"attachment": [{"time": 0.1667, "name": null}, {"time": 0.3, "name": "shaseng_7"}]}, "shaseng_8": {"attachment": [{"time": 0.1667, "name": null}, {"time": 0.3, "name": "shaseng_8"}]}, "shaseng_9": {"attachment": [{"time": 0.1667, "name": null}, {"time": 0.3, "name": "shaseng_9"}]}, "shaseng_10": {"attachment": [{"time": 0.1667, "name": null}, {"time": 0.3, "name": "shaseng_10"}]}, "shaseng_11": {"attachment": [{"time": 0.1667, "name": null}, {"time": 0.3, "name": "shaseng_11"}]}, "shaseng_12": {"attachment": [{"time": 0.1667, "name": null}, {"time": 0.3, "name": "shaseng_12"}]}, "shaseng_13": {"attachment": [{"time": 0.1667, "name": null}, {"time": 0.3, "name": "shaseng_13"}]}, "shaseng_14": {"attachment": [{"time": 0.1667, "name": null}, {"time": 0.3, "name": "shaseng_14"}]}, "shaseng_16": {"attachment": [{"time": 0.1667, "name": null}, {"time": 0.3, "name": "shaseng_16"}]}, "shaseng_17": {"attachment": [{"time": 0.1667, "name": null}, {"time": 0.3, "name": "shaseng_17"}]}, "shaseng_18": {"attachment": [{"time": 0.1667, "name": null}, {"time": 0.3, "name": "shaseng_18"}]}, "shaseng_19": {"attachment": [{"time": 0.1667, "name": null}, {"time": 0.3, "name": "shaseng_20"}]}}, "bones": {"body": {"translate": [{"y": -1}, {"time": 0.1, "x": 14.71, "y": -27.56, "curve": "stepped"}, {"time": 0.1333, "x": 14.71, "y": -27.56, "curve": 0.471, "c2": 0.22, "c4": 0.61}, {"time": 0.2333, "x": -13.14, "y": -13.93, "curve": 0.324, "c2": 0.2, "c3": 0.7, "c4": 0.54}, {"time": 0.3, "y": -1}]}, "shaseng_13": {"rotate": [{"angle": -2.5, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1, "angle": -1.12, "curve": "stepped"}, {"time": 0.1333, "angle": -1.12, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.3, "angle": -2.5}]}, "shaseng_14": {"rotate": [{"angle": -5.57, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.1, "angle": 6.23, "curve": "stepped"}, {"time": 0.1333, "angle": 6.23, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -5.57}]}, "shaseng_15": {"rotate": [{"angle": -14.31, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1, "angle": -2.22, "curve": "stepped"}, {"time": 0.1333, "angle": -2.22, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2667, "angle": 6.06, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.3, "angle": -14.31}]}, "shaseng_16": {"rotate": [{"angle": -10.28, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "angle": -6.39, "curve": "stepped"}, {"time": 0.1333, "angle": -6.39, "curve": 0.369, "c2": 0.47, "c3": 0.753}, {"time": 0.3, "angle": -10.28}]}, "shaseng_17": {"rotate": [{"angle": 2.32, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1, "angle": 1.27, "curve": "stepped"}, {"time": 0.1333, "angle": 1.27, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3, "angle": 2.32}]}, "shaseng_18": {"rotate": [{"angle": 0.61, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1, "angle": -2.97, "curve": "stepped"}, {"time": 0.1333, "angle": -2.97, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.3, "angle": 0.61}]}, "shaseng_19": {"rotate": [{"angle": -6.09, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.1, "angle": 16.72, "curve": "stepped"}, {"time": 0.1333, "angle": 16.72, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.3, "angle": -6.09}]}, "shaseng_20": {"rotate": [{"angle": -9.23, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1, "angle": 11.05, "curve": "stepped"}, {"time": 0.1333, "angle": 11.05, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2667, "angle": 24.51, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 0.3, "angle": -9.23}]}, "shaseng_22": {"rotate": [{"angle": 0.88, "curve": 0, "c2": 0.18, "c3": 0.372, "c4": 0.57}, {"time": 0.1, "angle": 5.83, "curve": "stepped"}, {"time": 0.1333, "angle": 5.83, "curve": 0.213, "c2": 0.45, "c3": 0.599}, {"time": 0.3, "angle": 0.88}], "translate": [{}, {"time": 0.1, "x": -10.91, "y": -10.51, "curve": "stepped"}, {"time": 0.1667, "x": -10.91, "y": -10.51, "curve": 0.324, "c2": 0.2, "c3": 0.7, "c4": 0.54}, {"time": 0.3}]}, "shaseng_23": {"rotate": [{"angle": 0.91, "curve": 0, "c2": 0.18, "c3": 0.372, "c4": 0.57}, {"time": 0.1, "angle": 10.55, "curve": "stepped"}, {"time": 0.1333, "angle": 10.55, "curve": 0.213, "c2": 0.45, "c3": 0.599}, {"time": 0.3, "angle": 0.91}], "translate": [{"x": -1.1, "y": -0.05, "curve": 0, "c2": 0.19, "c3": 0.486}, {"time": 0.1, "x": 5.58, "y": 19.6, "curve": "stepped"}, {"time": 0.1333, "x": 5.58, "y": 19.6, "curve": 0.318, "c2": 0.23, "c3": 0.666, "c4": 0.57}, {"time": 0.1667, "x": -1.32, "y": 11.57, "curve": 0.318, "c2": 0.23, "c3": 0.666, "c4": 0.57}, {"time": 0.3, "x": -1.1, "y": -0.05}]}, "shaseng_6": {"rotate": [{"angle": 0.4, "curve": 0, "c2": 0.19, "c3": 0.486}, {"time": 0.1, "angle": -0.9, "curve": "stepped"}, {"time": 0.1333, "angle": -0.9, "curve": 0.25, "c4": 0.5}, {"time": 0.2333, "angle": -11.28, "curve": 0.375, "c2": 0.16, "c3": 0.852, "c4": 0.52}, {"time": 0.3, "angle": 0.4}], "translate": [{"x": 0.11, "y": -0.01, "curve": 0, "c2": 0.19, "c3": 0.486}, {"time": 0.1, "curve": "stepped"}, {"time": 0.2333, "curve": 0.375, "c2": 0.16, "c3": 0.852, "c4": 0.52}, {"time": 0.3, "x": 0.11, "y": -0.01}]}, "shaseng_3": {"rotate": [{"angle": -1.22, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1, "angle": -4.29, "curve": "stepped"}, {"time": 0.1333, "angle": -4.29, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.3, "angle": -1.22}]}, "shaseng_4": {"rotate": [{"angle": -3.22, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1, "angle": 1.75, "curve": "stepped"}, {"time": 0.1333, "angle": 1.75, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.3, "angle": -3.22}]}, "shaseng_5": {"rotate": [{"angle": -11.17, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 13.53, "curve": "stepped"}, {"time": 0.1333, "angle": 13.53, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -11.17}]}, "shaseng_7": {"rotate": [{"angle": 0.32, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1, "angle": -4.01, "curve": "stepped"}, {"time": 0.1333, "angle": -4.01, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.2333, "angle": -2.34, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3, "angle": 0.32}]}, "shaseng_8": {"rotate": [{"angle": -0.85, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1, "angle": 0.24, "curve": "stepped"}, {"time": 0.1333, "angle": 0.24, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.3, "angle": -0.85}]}, "shaseng_9": {"rotate": [{"angle": -19.64, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1, "angle": 1.81, "curve": "stepped"}, {"time": 0.1333, "angle": 1.81, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.3, "angle": -19.64}]}, "shaseng_10": {"rotate": [{"angle": -2.78, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1, "angle": 1.62, "curve": "stepped"}, {"time": 0.1333, "angle": 1.62, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.3, "angle": -2.78}]}, "shaseng_11": {"rotate": [{"angle": -7.55, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 1.37, "curve": "stepped"}, {"time": 0.1333, "angle": 1.37, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -7.55}]}, "shaseng_21": {"rotate": [{"angle": -12.53, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1, "angle": 3.84, "curve": "stepped"}, {"time": 0.1333, "angle": 3.84, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.3, "angle": -6.53}]}, "shaseng_1": {"rotate": [{"angle": 0.65, "curve": "stepped"}, {"time": 0.1333, "angle": 0.65, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667, "angle": 5.4, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3, "angle": 0.65}]}, "shaseng_2": {"rotate": [{"angle": 5.3, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -5.55, "curve": "stepped"}, {"time": 0.1333, "angle": -5.55, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 5.3}]}, "shaseng_25": {"rotate": [{"angle": -1.32, "curve": 0.354, "c2": 0.41, "c3": 0.756}, {"time": 0.1, "angle": 3.03, "curve": "stepped"}, {"time": 0.1333, "angle": 3.03, "curve": 0.342, "c2": 0.36, "c3": 0.757}, {"time": 0.3, "angle": -1.32}]}, "shaseng_26": {"rotate": [{"angle": -5.94, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.1, "angle": 5.48, "curve": "stepped"}, {"time": 0.1333, "angle": 5.48, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 0.3, "angle": -5.94}]}, "shaseng_27": {"rotate": [{"angle": 1.22, "curve": "stepped"}, {"time": 0.1333, "angle": 1.22, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667, "angle": -1.91, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3, "angle": 1.22}]}, "shaseng_28": {"rotate": [{"angle": -5.72, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1, "angle": 4.91, "curve": "stepped"}, {"time": 0.1333, "angle": 4.91, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.3, "angle": -5.72}]}, "shaseng_29": {"rotate": [{"angle": -10.42, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1, "angle": 9.61, "curve": "stepped"}, {"time": 0.1333, "angle": 9.61, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 0.3, "angle": -10.42}]}, "shaseng_30": {"rotate": [{"angle": -1.53, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1, "angle": 3.98, "curve": "stepped"}, {"time": 0.1667, "angle": 3.98, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.3, "angle": -1.53}]}, "shaseng_31": {"rotate": [{"angle": 2.24, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1, "angle": -1.82, "curve": "stepped"}, {"time": 0.1333, "angle": -1.82, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.3, "angle": 2.24}]}, "shaseng_32": {"rotate": [{"angle": 10.17, "curve": 0.306, "c2": 0.23, "c3": 0.756}, {"time": 0.1, "angle": -8.35, "curve": "stepped"}, {"time": 0.1333, "angle": -8.35, "curve": 0.303, "c2": 0.22, "c3": 0.756}, {"time": 0.3, "angle": 10.17}]}, "shaseng_24": {"rotate": [{"angle": -1.59, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1, "angle": -3.29, "curve": "stepped"}, {"time": 0.1333, "angle": -3.29, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.2333, "angle": -6.33, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3, "angle": -1.59}]}, "shaseng_33": {"rotate": [{"angle": -7.82, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1, "angle": -0.11, "curve": "stepped"}, {"time": 0.1333, "angle": -0.11, "curve": 0.342, "c2": 0.36, "c3": 0.757}, {"time": 0.3, "angle": -7.82}]}, "shaseng_34": {"rotate": [{"angle": -26.92, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 8.29, "curve": "stepped"}, {"time": 0.1333, "angle": 8.29, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -26.92}]}, "shaseng_35": {"rotate": [{"angle": -17.76, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1, "angle": -5.69, "curve": "stepped"}, {"time": 0.1333, "angle": -5.69, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3, "angle": -17.76}]}, "shaseng_36": {"rotate": [{"angle": 0.16, "curve": "stepped"}, {"time": 0.1333, "angle": 0.16, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667, "angle": 1.86, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3, "angle": 0.16}]}, "shaseng_37": {"rotate": [{"angle": 2.26, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1, "angle": -2.57, "curve": "stepped"}, {"time": 0.1333, "angle": -2.57, "curve": 0.342, "c2": 0.36, "c3": 0.757}, {"time": 0.3, "angle": 2.26}]}, "shaseng_38": {"rotate": [{"angle": 10.39, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.1, "angle": -20.6, "curve": "stepped"}, {"time": 0.1333, "angle": -20.6, "curve": 0.298, "c2": 0.2, "c3": 0.756}, {"time": 0.3, "angle": 10.39}]}, "shaseng_39": {"rotate": [{"angle": 12.82, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1, "angle": -17.23, "curve": "stepped"}, {"time": 0.1333, "angle": -17.23, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 0.3, "angle": 12.82}]}, "shaseng_40": {"rotate": [{"angle": -3.61, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 3.84, "curve": "stepped"}, {"time": 0.1333, "angle": 3.84, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -3.61}]}, "shaseng_41": {"rotate": [{"angle": -5.03, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1, "angle": 11.8, "curve": "stepped"}, {"time": 0.1333, "angle": 11.8, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.3, "angle": -5.03}]}, "shaseng_42": {"rotate": [{"angle": -3.16, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1, "angle": 5.01, "curve": "stepped"}, {"time": 0.1333, "angle": 5.01, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.2333, "angle": 19.66, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3, "angle": -3.16}]}, "shaseng_43": {"rotate": [{"angle": 13.07, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1, "angle": 0.24, "curve": "stepped"}, {"time": 0.1333, "angle": 0.24, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.3, "angle": 13.07}]}, "shaseng_0": {"rotate": [{"angle": 0.31, "curve": 0, "c2": 0.19, "c3": 0.486}, {"time": 0.1, "angle": -38.88, "curve": "stepped"}, {"time": 0.1333, "angle": -38.88, "curve": 0.25, "c4": 0.5}, {"time": 0.2667, "angle": 25.28, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 0.31}]}, "shaseng_44": {"rotate": [{"angle": 1.4, "curve": 0, "c2": 0.19, "c3": 0.486}, {"time": 0.1, "angle": -9.46, "curve": "stepped"}, {"time": 0.1333, "angle": -9.46, "curve": 0.25, "c4": 0.5}, {"time": 0.2667, "angle": -4.01, "curve": 0.351, "c2": 0.65, "c3": 0.685}, {"time": 0.3, "angle": 1.4}]}, "shaseng_45": {"rotate": [{"angle": 3.88, "curve": 0.145, "c2": 0.6, "c3": 0.75}, {"time": 0.1, "angle": 4.71, "curve": "stepped"}, {"time": 0.1333, "angle": 4.71, "curve": 0.408, "c2": 0.2, "c3": 0.847, "c4": 0.55}, {"time": 0.3, "angle": 3.88}]}, "shaseng_47": {"rotate": [{"angle": -0.28, "curve": 0, "c2": 0.19, "c3": 0.486}, {"time": 0.1, "angle": 28.16, "curve": "stepped"}, {"time": 0.1333, "angle": 28.16, "curve": 0.25, "c4": 0.4}, {"time": 0.2667, "angle": -26.37, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -0.28}], "translate": [{"curve": 0, "c2": 0.19, "c3": 0.486}, {"time": 0.1, "x": -18.69, "y": 4.03, "curve": "stepped"}, {"time": 0.1333, "x": -18.69, "y": 4.03, "curve": 0.25, "c4": 0.4}, {"time": 0.2667, "x": -21.4, "y": -1.56, "curve": 0.25, "c3": 0.75}, {"time": 0.3}], "scale": [{"curve": 0, "c2": 0.19, "c3": 0.486}, {"time": 0.1, "x": 1.065, "y": 1.021, "curve": "stepped"}, {"time": 0.2667, "x": 1.065, "y": 1.021, "curve": 0.25, "c3": 0.75}, {"time": 0.3}]}, "shaseng_48": {"rotate": [{"angle": -1.21, "curve": 0, "c2": 0.19, "c3": 0.486}, {"time": 0.1, "angle": -47.29, "curve": "stepped"}, {"time": 0.1333, "angle": -47.29, "curve": 0.25, "c4": 0.4}, {"time": 0.2667, "angle": -1.59, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -1.21}], "translate": [{"curve": 0, "c2": 0.19, "c3": 0.486}, {"time": 0.1, "x": 17, "y": 8.73, "curve": "stepped"}, {"time": 0.1333, "x": 17, "y": 8.73, "curve": 0.25, "c4": 0.4}, {"time": 0.2667, "x": 19.93, "y": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 0.3}]}, "shaseng_49": {"rotate": [{"angle": -0.94}]}, "shaseng_46": {"scale": [{"curve": 0, "c2": 0.19, "c3": 0.486}, {"time": 0.1, "x": 1.063, "y": 1.063, "curve": "stepped"}, {"time": 0.2667, "x": 1.063, "y": 1.063, "curve": 0.328, "c2": 0.32, "c3": 0.665, "c4": 0.66}, {"time": 0.3}]}, "shaseng_55": {"rotate": [{"angle": -1.93}]}, "shaseng_56": {"rotate": [{"angle": 5.76, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 0.1, "angle": 18.29, "curve": "stepped"}, {"time": 0.1333, "angle": 18.29, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.3, "angle": 5.76}]}, "shaseng_59": {"rotate": [{"angle": -1.03, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 0.1, "angle": -5.49, "curve": "stepped"}, {"time": 0.1333, "angle": -5.49, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.3, "angle": -1.03}]}, "shaseng_60": {"rotate": [{"angle": 1.88, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 0.1, "angle": 15.25, "curve": "stepped"}, {"time": 0.1333, "angle": 15.25, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.3, "angle": 1.88}]}, "shaseng_63": {"rotate": [{"angle": 0.48, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.1, "angle": 6.83, "curve": "stepped"}, {"time": 0.1333, "angle": 6.83, "curve": 0.337, "c2": 0.35, "c3": 0.758}, {"time": 0.3, "angle": 0.48}]}, "shaseng_63b": {"rotate": [{"angle": -11.96, "curve": 0.277, "c2": 0.14, "c3": 0.663, "c4": 0.65}, {"time": 0.1, "angle": 5.84, "curve": "stepped"}, {"time": 0.1333, "angle": 5.84, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -11.96}]}, "shaseng_63c": {"rotate": [{"angle": -5.86, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1, "angle": 12, "curve": "stepped"}, {"time": 0.1333, "angle": 12, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.3, "angle": -5.86}]}, "shaseng_50": {"rotate": [{"angle": 4.94, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1, "angle": -0.73, "curve": "stepped"}, {"time": 0.1333, "angle": -0.73, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3, "angle": 4.94}]}, "shaseng_50b": {"rotate": [{"angle": 3.36, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "angle": 4.47, "curve": "stepped"}, {"time": 0.1333, "angle": 4.47, "curve": 0.369, "c2": 0.47, "c3": 0.753}, {"time": 0.3, "angle": 3.36}]}, "shaseng_50c": {"rotate": [{"angle": 18.6, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -11.14, "curve": "stepped"}, {"time": 0.1333, "angle": -11.14, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.3, "angle": 18.6}]}, "shaseng_64": {"rotate": [{"angle": -2.95, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.1, "angle": 43.44, "curve": "stepped"}, {"time": 0.1333, "angle": 43.44, "curve": 0.379, "c2": 0.53, "c3": 0.746}, {"time": 0.2667, "angle": 71.44, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.3, "angle": -2.95}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": -21.76, "y": -0.39, "curve": "stepped"}, {"time": 0.1333, "x": -21.76, "y": -0.39, "curve": 0.379, "c2": 0.53, "c3": 0.746}, {"time": 0.2667, "x": -37.95, "y": -0.69, "curve": 0.25, "c3": 0.75}, {"time": 0.3}]}, "shaseng_65": {"rotate": [{"angle": -15.45, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "angle": -113.38, "curve": "stepped"}, {"time": 0.1333, "angle": -113.38, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3, "angle": -15.45}]}, "shaseng_66": {"rotate": [{"angle": -0.06, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.1, "angle": -0.08, "curve": "stepped"}, {"time": 0.1333, "angle": -0.08, "curve": 0.369, "c2": 0.47, "c3": 0.753}, {"time": 0.3, "angle": -0.06}]}, "shaseng_67": {"rotate": [{"angle": 1.41, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1, "angle": -0.33, "curve": "stepped"}, {"time": 0.1333, "angle": -0.33, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.3, "angle": 1.41}]}, "shaseng_68": {"rotate": [{"angle": 4.47, "curve": 0.271, "c2": 0.09, "c3": 0.753}, {"time": 0.1, "angle": -5.95, "curve": "stepped"}, {"time": 0.1333, "angle": -5.95, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 4.47}]}, "shaseng_69": {"rotate": [{"angle": 7.4, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.1, "angle": -1.23, "curve": "stepped"}, {"time": 0.1333, "angle": -1.23, "curve": 0.379, "c2": 0.53, "c3": 0.746}, {"time": 0.2667, "angle": -10.7, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.3, "angle": 7.4}]}, "shaseng_70": {"rotate": [{"angle": -0.55, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1, "angle": 0.25, "curve": "stepped"}, {"time": 0.1333, "angle": 0.25, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2667, "angle": 0.84, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.3, "angle": -0.55}], "translate": [{"curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.1, "x": -6.44, "y": -11.63, "curve": "stepped"}, {"time": 0.1333, "x": -6.44, "y": -11.63, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.2333, "x": -8.01, "y": -7.63, "curve": 0.25, "c3": 0.75}, {"time": 0.3}]}, "shaseng_71": {"rotate": [{"angle": -0.36, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.1, "angle": 3.67, "curve": "stepped"}, {"time": 0.1333, "angle": 3.67, "curve": 0.369, "c2": 0.47, "c3": 0.753}, {"time": 0.3, "angle": -0.36}]}, "shaseng_72": {"rotate": [{"angle": 2.08, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1, "angle": -2.26, "curve": "stepped"}, {"time": 0.1333, "angle": -2.26, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.3, "angle": 2.08}]}, "shaseng_73": {"rotate": [{"angle": 11.12, "curve": 0.263, "c2": 0.08, "c3": 0.645, "c4": 0.59}, {"time": 0.1, "angle": 5.84, "curve": "stepped"}, {"time": 0.1333, "angle": 5.84, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 11.12}]}, "shaseng_58": {"rotate": [{"curve": 0, "c2": 0.19, "c3": 0.486}, {"time": 0.1, "angle": 13.91, "curve": "stepped"}, {"time": 0.1667, "angle": 13.91, "curve": 0.37, "c2": 0.23, "c3": 0.748, "c4": 0.58}, {"time": 0.3}], "translate": [{"y": 1.63, "curve": 0, "c2": 0.19, "c3": 0.486}, {"time": 0.1, "x": -11.72, "y": -9.08, "curve": "stepped"}, {"time": 0.1667, "x": -11.72, "y": -9.08, "curve": 0.37, "c2": 0.23, "c3": 0.748, "c4": 0.58}, {"time": 0.3, "y": 1.63}], "scale": [{"curve": 0, "c2": 0.19, "c3": 0.486}, {"time": 0.1, "x": 1.056, "curve": "stepped"}, {"time": 0.1667, "x": 1.056, "curve": 0.322, "c2": 0.13, "c3": 0.754, "c4": 0.5}, {"time": 0.3}]}, "shaseng_12": {"rotate": [{"angle": -6.08, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 11.87, "curve": "stepped"}, {"time": 0.1333, "angle": 11.87, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -6.08}]}, "a_canying2": {"translate": [{"time": 0.2333}, {"time": 0.2667, "y": 12.86}, {"time": 0.3}], "scale": [{"time": 0.1667}, {"time": 0.2333, "x": 0.992}, {"time": 0.2667, "x": 0.895, "y": 1.141}, {"time": 0.3}], "shear": [{}, {"time": 0.1667, "y": -10.3}, {"time": 0.2333}, {"time": 0.2667, "x": -0.18, "y": -10.23}, {"time": 0.3}]}, "shaseng_78": {"scale": [{"curve": 0, "c2": 0.19, "c3": 0.486}, {"time": 0.1, "x": 1.063, "y": 1.063, "curve": "stepped"}, {"time": 0.2667, "x": 1.063, "y": 1.063, "curve": 0.328, "c2": 0.32, "c3": 0.665, "c4": 0.66}, {"time": 0.3}]}}, "deform": {"default": {"shaseng_0": {"shaseng_0": [{"offset": 54, "vertices": [-0.03646, -0.34388, 0.10032, -0.33095, -0.07283, -0.68767, -0.07283, -0.68767, 0, 0, 0, 0, 0.20083, 2.08921, 0.12687, 1.00017, 0.12687, 1.00017, -0.12721, 1.02398, -0.12721, 1.02398, -0.71556, 2.38928, -1.80434, 1.71917], "curve": 0, "c2": 0.19, "c3": 0.486}, {"time": 0.1, "offset": 66, "vertices": [0.35162, 3.62695, 1.04474, 10.7868, -0.83807, 10.77081, -4.94852, 9.58728, -1.54349, 10.26245, -1.03409, 3.49414, -2.54214, 2.61096], "curve": "stepped"}, {"time": 0.1333, "offset": 66, "vertices": [0.35162, 3.62695, 1.04474, 10.7868, -0.83807, 10.77081, -4.94852, 9.58728, -1.54349, 10.26245, -1.03409, 3.49414, -2.54214, 2.61096], "curve": 0.25, "c4": 0.5}, {"time": 0.2667, "offset": 66, "vertices": [0.35162, 3.62695, 4.19196, 16.65964, 1.76602, 16.04538, -5.474, 15.44615, -1.08838, 12.64333, -1.03409, 3.49414, -2.54214, 2.61096], "curve": 0.25, "c3": 0.75}, {"time": 0.3, "offset": 54, "vertices": [-0.03646, -0.34388, 0.10032, -0.33095, -0.07283, -0.68767, -0.07283, -0.68767, 0, 0, 0, 0, 0.20083, 2.08921, 0.12687, 1.00017, 0.12687, 1.00017, -0.12721, 1.02398, -0.12721, 1.02398, -0.71556, 2.38928, -1.80434, 1.71917]}]}, "shaseng_9": {"shaseng_9": [{"curve": 0, "c2": 0.19, "c3": 0.486}, {"time": 0.1, "offset": 6, "vertices": [-2.22737, 3.52644, -9.65449, 2.96683, -10.00739, 8.21714, -8.82137, 12.79346, -8.68124, 6.99791, -9.15766, 10.39286, -9.15766, 10.39286, -9.15766, 10.39286, -9.15766, 10.39286, -8.16845, 6.77279, -2.22737, 3.52644, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.22737, 3.52644, -8.16845, 6.77279, -9.91901, 6.90439, 0, 0, 0, 0, -2.22737, 3.52644, -8.16845, 6.77279, -10.21957, 7.6735, 0, 0, 0, 0, -2.22737, 3.52644, -8.16845, 6.77279, -9.15766, 10.39286], "curve": "stepped"}, {"time": 0.1667, "offset": 6, "vertices": [-2.22737, 3.52644, -9.65449, 2.96683, -10.00739, 8.21714, -8.82137, 12.79346, -8.68124, 6.99791, -9.15766, 10.39286, -9.15766, 10.39286, -9.15766, 10.39286, -9.15766, 10.39286, -8.16845, 6.77279, -2.22737, 3.52644, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.22737, 3.52644, -8.16845, 6.77279, -9.91901, 6.90439, 0, 0, 0, 0, -2.22737, 3.52644, -8.16845, 6.77279, -10.21957, 7.6735, 0, 0, 0, 0, -2.22737, 3.52644, -8.16845, 6.77279, -9.15766, 10.39286], "curve": 0.32, "c2": 0.13, "c3": 0.749, "c4": 0.5}, {"time": 0.3}]}, "shaseng_12": {"shaseng_12": [{"offset": 38, "vertices": [-0.15173, 2.96886, 0.10692, 2.97074, -0.10624, 2.07813, 0.07481, 2.07948, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.13476, -0.00691, -0.32353, -0.01655, -0.31803, 0.10331, 0.33301, -1.76637, 0.50324, 1.49926, -0.17594, -0.90179, -0.25176, -0.85232, 0.41558, 0.02124, 0.42157, 0.07574, -0.25341, -0.01296, -0.24782, 0.10075, 0.29612, 0.01514, 0.30013, 0.04975, 0, 0, 0, 0, 0.29615, 0.01514, 0.30013, 0.04971], "curve": 0, "c2": 0.19, "c3": 0.486}, {"time": 0.1, "vertices": [-2.02194, -5.5016, -2.02194, -5.5016, -0.86133, -6.39301, -2.22521, -6.05493, -0.86133, -6.39301, -2.22521, -6.05493, 0, 0, 0, 0, 0, 0, 0, 0, 0.89937, 6.67488, 0.89937, 6.67488, 0, 0, -2.09128, 5.79717, -0.56051, 6.13752, -2.09128, 5.79717, -0.56051, 6.13752, 1.23776, 13.14005, 4.80341, 12.23476, 1.89778, 34.97215, 11.65547, 33.07471, -1.51618, 19.65802, 4.9028, 19.16668, 0.05424, 19.85319, 4.70217, 18.62404, 4.79973, 11.50201, -1.32371, 6.12861, 0, 0, 0, 0, 1.31866, 9.78675, 3.39208, 9.27422, 2.1394, 9.82869, 1.13684, 0.81226, 1.28873, 0.74371, 2.38688, 3.35522, 2.36045, 15.06145, -1.50391, -12.3478, -4.13873, -11.68924, 0.65921, -11.55818, -1.82979, -11.07862, 3.21894, 11.82895, 6.31922, 10.72344, 4.2355, 16.642, 8.35737, 15.23071, 2.66995, 17.41696, 6.77065, 16.20456, 2.17481, 4.94721, 3.21316, 4.59564, 0.41929, 3.11188, 1.08316, 2.94748], "curve": "stepped"}, {"time": 0.1667, "vertices": [-2.02194, -5.5016, -2.02194, -5.5016, -0.86133, -6.39301, -2.22521, -6.05493, -0.86133, -6.39301, -2.22521, -6.05493, 0, 0, 0, 0, 0, 0, 0, 0, 0.89937, 6.67488, 0.89937, 6.67488, 0, 0, -2.09128, 5.79717, -0.56051, 6.13752, -2.09128, 5.79717, -0.56051, 6.13752, 1.23776, 13.14005, 4.80341, 12.23476, 1.89778, 34.97215, 11.65547, 33.07471, -1.51618, 19.65802, 4.9028, 19.16668, 0.05424, 19.85319, 4.70217, 18.62404, 4.79973, 11.50201, -1.32371, 6.12861, 0, 0, 0, 0, 1.31866, 9.78675, 3.39208, 9.27422, 2.1394, 9.82869, 1.13684, 0.81226, 1.28873, 0.74371, 2.38688, 3.35522, 2.36045, 15.06145, -1.50391, -12.3478, -4.13873, -11.68924, 0.65921, -11.55818, -1.82979, -11.07862, 3.21894, 11.82895, 6.31922, 10.72344, 4.2355, 16.642, 8.35737, 15.23071, 2.66995, 17.41696, 6.77065, 16.20456, 2.17481, 4.94721, 3.21316, 4.59564, 0.41929, 3.11188, 1.08316, 2.94748], "curve": 0.375, "c2": 0.16, "c3": 0.852, "c4": 0.52}, {"time": 0.3, "offset": 38, "vertices": [-0.15173, 2.96886, 0.10692, 2.97074, -0.10624, 2.07813, 0.07481, 2.07948, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.13476, -0.00691, -0.32353, -0.01655, -0.31803, 0.10331, 0.33301, -1.76637, 0.50324, 1.49926, -0.17594, -0.90179, -0.25176, -0.85232, 0.41558, 0.02124, 0.42157, 0.07574, -0.25341, -0.01296, -0.24782, 0.10075, 0.29612, 0.01514, 0.30013, 0.04975, 0, 0, 0, 0, 0.29615, 0.01514, 0.30013, 0.04971]}]}, "shaseng_13": {"shaseng_13": [{"offset": 40, "vertices": [-83.11365, -48.45935, -72.98035, -9.20032, -67.24121, 4.88055, -59.9043, 32.10095]}]}, "shaseng_16": {"shaseng_16": [{"offset": 110, "vertices": [0.46622, -2.23144, 1.5949, -1.62874, 0.46622, -2.23144, 1.5949, -1.62874, 0.46622, -2.23144], "curve": 0, "c2": 0.19, "c3": 0.486}, {"time": 0.1, "curve": "stepped"}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "offset": 110, "vertices": [0.46622, -2.23144, 1.5949, -1.62874, 0.46622, -2.23144, 1.5949, -1.62874, 0.46622, -2.23144]}]}}}}, "move": {"slots": {"daoguang": {"attachment": [{"name": null}]}, "daoying1": {"attachment": [{"name": null}]}, "shaseng_13": {"attachment": [{"name": null}]}, "shaseng_14": {"attachment": [{"name": null}]}, "shaseng_15": {"attachment": [{"name": "shaseng_15"}]}, "shaseng_20": {"attachment": [{"name": "shaseng_15"}]}}, "bones": {"body": {"translate": [{}, {"time": 0.1092, "y": -10}, {"time": 0.2729, "y": 6}, {"time": 0.4367, "y": -8}, {"time": 0.6004}]}, "shaseng_13": {"rotate": [{"angle": -2.5, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1637, "angle": 5.58, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.2729, "angle": -33.98, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.4912, "angle": -13.5, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6004, "angle": -2.5}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2729, "x": -1.68, "y": 2.69, "curve": 0.25, "c3": 0.75}, {"time": 0.6004}]}, "shaseng_14": {"rotate": [{"angle": 5.62, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.2729, "angle": -27.23, "curve": 0.25, "c3": 0.75}, {"time": 0.6004, "angle": 5.62}]}, "shaseng_15": {"rotate": [{"angle": -14.31, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0546, "angle": 8.04, "curve": 0.25, "c3": 0.75}, {"time": 0.3275, "angle": -35.56, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.6004, "angle": -14.31}]}, "shaseng_16": {"rotate": [{"angle": -10.28, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1092, "angle": 16.05, "curve": 0.25, "c3": 0.75}, {"time": 0.4367, "angle": -24.24, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6004, "angle": -10.28}]}, "shaseng_17": {"rotate": [{"angle": 2.32, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.0546, "angle": -0.02, "curve": 0.25, "c3": 0.75}, {"time": 0.3821, "angle": -20.43, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.6004, "angle": 2.32}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2729, "x": -28.97, "y": 1.42, "curve": 0.25, "c3": 0.75}, {"time": 0.6004}]}, "shaseng_18": {"rotate": [{"angle": 0.61, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1637, "angle": 1.23, "curve": 0.25, "c3": 0.75}, {"time": 0.4912, "angle": -12.01, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6004, "angle": 0.61}]}, "shaseng_19": {"rotate": [{"angle": -6.09, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.2729, "angle": -13.42, "curve": 0.25, "c3": 0.75}, {"time": 0.6004, "angle": -6.09}]}, "shaseng_20": {"rotate": [{"angle": -9.23, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0546, "angle": -12.07, "curve": 0.25, "c3": 0.75}, {"time": 0.3275, "angle": -22.22, "curve": 0.245, "c3": 0.638, "c4": 0.56}, {"time": 0.5333, "angle": -24.24, "curve": 0.349, "c2": 0.39, "c3": 0.688, "c4": 0.74}, {"time": 0.6004, "angle": -9.23}]}, "shaseng_22": {"rotate": [{"angle": 17}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2729, "y": 1.28, "curve": 0.25, "c3": 0.75}, {"time": 0.6004}]}, "shaseng_23": {"rotate": [{"angle": 10.47, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0546, "angle": 10.98, "curve": 0.25, "c3": 0.75}, {"time": 0.3275, "angle": 7.08, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.6004, "angle": 10.47}], "translate": [{"x": -1.1, "y": -0.05, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0546, "x": -1.5, "y": -0.08, "curve": 0.25, "c3": 0.75}, {"time": 0.3275, "x": 1.61, "y": 0.11, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.6004, "x": -1.1, "y": -0.05}]}, "shaseng_6": {"rotate": [{"angle": -7.5, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1092, "angle": -6.98, "curve": 0.25, "c3": 0.75}, {"time": 0.4367, "angle": -8.41, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6004, "angle": -7.5}], "translate": [{"x": 0.11, "y": -0.01, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1092, "curve": 0.25, "c3": 0.75}, {"time": 0.4367, "x": 0.3, "y": -0.02, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6004, "x": 0.11, "y": -0.01}]}, "shaseng_3": {"rotate": [{"angle": -1.22, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1637, "angle": 0.94, "curve": 0.25, "c3": 0.75}, {"time": 0.4912, "angle": -2.47, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6004, "angle": -1.22}]}, "shaseng_4": {"rotate": [{"angle": -3.22, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.2183, "angle": 13.52, "curve": 0.25, "c3": 0.75}, {"time": 0.5458, "angle": -5.72, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.6004, "angle": -3.22}]}, "shaseng_5": {"rotate": [{"angle": -11.17, "curve": 0.25, "c3": 0.75}, {"time": 0.2729, "angle": 17.62, "curve": 0.25, "c3": 0.75}, {"time": 0.6004, "angle": -11.17}]}, "shaseng_7": {"rotate": [{"angle": 0.32, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1092, "angle": 1.88, "curve": 0.25, "c3": 0.75}, {"time": 0.4367, "angle": -2.34, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6004, "angle": 0.32}]}, "shaseng_8": {"rotate": [{"angle": -0.85, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1637, "angle": 13.81, "curve": 0.25, "c3": 0.75}, {"time": 0.4912, "angle": -9.37, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6004, "angle": -0.85}]}, "shaseng_9": {"rotate": [{"angle": -19.64, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.2183, "angle": 14.97, "curve": 0.25, "c3": 0.75}, {"time": 0.5458, "angle": -24.82, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.6004, "angle": -19.64}]}, "shaseng_10": {"rotate": [{"angle": -2.78, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.2183, "angle": 2.8, "curve": 0.25, "c3": 0.75}, {"time": 0.5458, "angle": -3.61, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.6004, "angle": -2.78}]}, "shaseng_11": {"rotate": [{"angle": -7.55, "curve": 0.25, "c3": 0.75}, {"time": 0.2729, "angle": 10, "curve": 0.25, "c3": 0.75}, {"time": 0.6004, "angle": -7.55}]}, "shaseng_21": {"rotate": [{"angle": -6.53, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0546, "angle": -10.13, "curve": 0.25, "c3": 0.75}, {"time": 0.3275, "angle": 17.49, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.6004, "angle": -6.53}]}, "shaseng_1": {"rotate": [{"angle": -2.45, "curve": 0.25, "c3": 0.75}, {"time": 0.2729, "angle": 3.66, "curve": 0.25, "c3": 0.75}, {"time": 0.6004, "angle": -2.45}]}, "shaseng_2": {"rotate": [{"angle": -5.55, "curve": 0.25, "c3": 0.75}, {"time": 0.2729, "angle": 5.3, "curve": 0.25, "c3": 0.75}, {"time": 0.6004, "angle": -5.55}]}, "shaseng_25": {"rotate": [{"angle": 1.45, "curve": 0.25, "c3": 0.75}, {"time": 0.2729, "angle": -1.27, "curve": 0.25, "c3": 0.75}, {"time": 0.6004, "angle": 1.45}]}, "shaseng_26": {"rotate": [{"angle": 4.23, "curve": 0.25, "c3": 0.75}, {"time": 0.2729, "angle": -3.38, "curve": 0.25, "c3": 0.75}, {"time": 0.6004, "angle": 4.23}]}, "shaseng_27": {"rotate": [{"angle": 4.36, "curve": 0.25, "c3": 0.75}, {"time": 0.2729, "angle": -1.91, "curve": 0.25, "c3": 0.75}, {"time": 0.6004, "angle": 4.36}]}, "shaseng_28": {"rotate": [{"angle": 7.78, "curve": 0.25, "c3": 0.75}, {"time": 0.2729, "angle": -7.73, "curve": 0.25, "c3": 0.75}, {"time": 0.6004, "angle": 7.78}]}, "shaseng_29": {"rotate": [{"angle": 9.61, "curve": 0.25, "c3": 0.75}, {"time": 0.2729, "angle": -11.31, "curve": 0.25, "c3": 0.75}, {"time": 0.6004, "angle": 9.61}]}, "shaseng_30": {"rotate": [{"angle": -3.95, "curve": 0.25, "c3": 0.75}, {"time": 0.2729, "angle": 3.98, "curve": 0.25, "c3": 0.75}, {"time": 0.6004, "angle": -3.95}]}, "shaseng_31": {"rotate": [{"angle": -6.42, "curve": 0.25, "c3": 0.75}, {"time": 0.2729, "angle": 7.29, "curve": 0.25, "c3": 0.75}, {"time": 0.6004, "angle": -6.42}]}, "shaseng_32": {"rotate": [{"angle": -10.05, "curve": 0.25, "c3": 0.75}, {"time": 0.2729, "angle": 12, "curve": 0.25, "c3": 0.75}, {"time": 0.6004, "angle": -10.05}]}, "shaseng_24": {"rotate": [{"angle": -1.59, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1092, "angle": 1.16, "curve": 0.25, "c3": 0.75}, {"time": 0.4367, "angle": -6.33, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6004, "angle": -1.59}]}, "shaseng_33": {"rotate": [{"angle": -7.82, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1637, "angle": 3.13, "curve": 0.25, "c3": 0.75}, {"time": 0.4912, "angle": -11.32, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.6004, "angle": -7.82}]}, "shaseng_34": {"rotate": [{"angle": -26.92, "curve": 0.25, "c3": 0.75}, {"time": 0.2729, "angle": 8.29, "curve": 0.25, "c3": 0.75}, {"time": 0.6004, "angle": -26.92}]}, "shaseng_35": {"rotate": [{"angle": -17.76, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.0546, "angle": -29.11, "curve": 0.25, "c3": 0.75}, {"time": 0.3821, "angle": 17.72, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.6004, "angle": -17.76}]}, "shaseng_36": {"rotate": [{"angle": 0.16, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1092, "angle": -1.54, "curve": 0.25, "c3": 0.75}, {"time": 0.4367, "angle": 1.86, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6004, "angle": 0.16}]}, "shaseng_37": {"rotate": [{"angle": 2.26, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1637, "angle": -4.6, "curve": 0.25, "c3": 0.75}, {"time": 0.4912, "angle": 4.45, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.6004, "angle": 2.26}]}, "shaseng_38": {"rotate": [{"angle": 10.39, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.2729, "angle": -12.76, "curve": 0.25, "c3": 0.75}, {"time": 0.6004, "angle": 10.39}]}, "shaseng_39": {"rotate": [{"angle": 12.82, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.0546, "angle": 14.15, "curve": 0.25, "c3": 0.75}, {"time": 0.3275, "angle": -17.23, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 0.6004, "angle": 12.82}]}, "shaseng_40": {"rotate": [{"angle": -3.61, "curve": 0.25, "c3": 0.75}, {"time": 0.2729, "angle": 3.84, "curve": 0.25, "c3": 0.75}, {"time": 0.6004, "angle": -3.61}]}, "shaseng_41": {"rotate": [{"angle": -5.03, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0546, "angle": -7.55, "curve": 0.25, "c3": 0.75}, {"time": 0.3275, "angle": 11.8, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.6004, "angle": -5.03}]}, "shaseng_42": {"rotate": [{"angle": -3.16, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1092, "angle": -16.45, "curve": 0.25, "c3": 0.75}, {"time": 0.4367, "angle": 19.66, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6004, "angle": -3.16}]}, "shaseng_43": {"rotate": [{"angle": 13.07, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1637, "angle": -14.3, "curve": 0.25, "c3": 0.75}, {"time": 0.4912, "angle": 29, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6004, "angle": 13.07}]}, "shaseng_0": {"rotate": [{"angle": -16.45, "curve": 0.25, "c3": 0.75}, {"time": 0.2729, "angle": 27.39, "curve": 0.25, "c3": 0.75}, {"time": 0.6004, "angle": -16.45}], "translate": [{"x": 2.64, "y": 6.2, "curve": 0.25, "c3": 0.75}, {"time": 0.2729, "curve": 0.25, "c3": 0.75}, {"time": 0.6004, "x": 2.64, "y": 6.2}]}, "shaseng_44": {"rotate": [{"angle": -44.55, "curve": 0.25, "c3": 0.75}, {"time": 0.2729, "angle": -8.43, "curve": 0.25, "c3": 0.75}, {"time": 0.6004, "angle": -44.55}]}, "shaseng_45": {"rotate": [{"angle": 14.96, "curve": 0.25, "c3": 0.75}, {"time": 0.2729, "angle": -6.16, "curve": 0.25, "c3": 0.75}, {"time": 0.6004, "angle": 14.96}]}, "shaseng_47": {"rotate": [{"angle": 39.19, "curve": 0.25, "c3": 0.75}, {"time": 0.2729, "angle": 11.26, "curve": 0.25, "c3": 0.75}, {"time": 0.6004, "angle": 39.19}], "translate": [{"x": 10.27, "y": -4.37, "curve": 0.25, "c3": 0.75}, {"time": 0.2729, "curve": 0.25, "c3": 0.75}, {"time": 0.6004, "x": 10.27, "y": -4.37}]}, "shaseng_48": {"rotate": [{"angle": -64.9, "curve": 0.25, "c3": 0.75}, {"time": 0.3275, "angle": -61.2, "curve": 0.25, "c3": 0.75}, {"time": 0.6004, "angle": -64.9}]}, "shaseng_49": {"rotate": [{"angle": -0.94, "curve": 0.25, "c3": 0.75}, {"time": 0.3821, "angle": 0.48, "curve": 0.25, "c3": 0.75}, {"time": 0.6004, "angle": -0.94}]}, "shaseng_51": {"rotate": [{"angle": -45.31}, {"time": 0.0546, "angle": -25.97}, {"time": 0.1092, "angle": -7.99}, {"time": 0.1637, "angle": 1.76}, {"time": 0.2183, "angle": 20.05}, {"time": 0.2729, "angle": 47.72}, {"time": 0.3275, "angle": 44.04}, {"time": 0.3821, "angle": 27.11}, {"time": 0.4367, "angle": 5.68}, {"time": 0.4912, "angle": -14.35}, {"time": 0.5458, "angle": -42.41}, {"time": 0.6004, "angle": -45.31}], "translate": [{"y": 5.52}], "scale": [{"time": 0.1637}, {"time": 0.2729, "x": 0.92, "y": 0.92}, {"time": 0.6004}]}, "shaseng_52": {"rotate": [{"angle": 48.77}, {"time": 0.0546, "angle": 4.91}, {"time": 0.1092, "angle": 6.32}, {"time": 0.1637, "angle": 13.29}, {"time": 0.2183, "angle": 3.2}, {"time": 0.2729, "angle": 7.6}, {"time": 0.3275, "angle": 32.12}, {"time": 0.3821, "angle": 80.73}, {"time": 0.4367, "angle": 107.39}, {"time": 0.4912, "angle": 124.99}, {"time": 0.5458, "angle": 98.94}, {"time": 0.6004, "angle": 48.77}]}, "shaseng_53": {"rotate": [{"angle": -3.5}, {"time": 0.0546, "angle": -12.45}, {"time": 0.1092, "angle": -6.79}, {"time": 0.1637, "angle": -17.53}, {"time": 0.2183, "angle": -19.1}, {"time": 0.2729, "angle": 9.79}, {"time": 0.3275, "angle": -0.71}, {"time": 0.3821, "angle": 3.26}, {"time": 0.4367}, {"time": 0.4912, "angle": -7.78}, {"time": 0.5458, "angle": -15.56}, {"time": 0.6004, "angle": -3.5}]}, "shaseng_54": {"rotate": [{}, {"time": 0.0546, "angle": 1.95}, {"time": 0.1092, "angle": 3.9}, {"time": 0.1637, "angle": -7.34}, {"time": 0.2183, "angle": -9.14}, {"time": 0.2729, "angle": -19.93}, {"time": 0.3275, "angle": -9.13}, {"time": 0.3821, "angle": -7.3}, {"time": 0.4367, "angle": -5.48}, {"time": 0.4912, "angle": -3.65}, {"time": 0.5458, "angle": -1.83}, {"time": 0.6004}]}, "shaseng_59": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2729, "y": 5.78, "curve": 0.25, "c3": 0.75}, {"time": 0.6004}]}, "shaseng_63": {"rotate": [{"angle": -28.64, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.1092, "angle": 0.85, "curve": 0.25, "c3": 0.75}, {"time": 0.4367, "angle": 7.57, "curve": 0.247, "c3": 0.63, "c4": 0.53}, {"time": 0.6004, "angle": -28.64}], "translate": [{"x": 5.27, "y": 8.54, "curve": 0.25, "c3": 0.75}, {"time": 0.2729, "x": 15.54, "y": -8.26, "curve": 0.25, "c3": 0.75}, {"time": 0.6004, "x": 5.27, "y": 8.54}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2729, "x": 0.786, "y": 0.786, "curve": 0.25, "c3": 0.75}, {"time": 0.6004}]}, "shaseng_63b": {"rotate": [{"angle": -11.96, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.2729, "angle": 23.21, "curve": 0.25, "c3": 0.75}, {"time": 0.6004, "angle": -11.96}]}, "shaseng_63c": {"rotate": [{"angle": -5.86, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.0546, "angle": -12.75, "curve": 0.25, "c3": 0.75}, {"time": 0.3821, "angle": 36.82, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.6004, "angle": -5.86}]}, "shaseng_50": {"rotate": [{"angle": 4.94, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.0546, "angle": 10.27, "curve": 0.25, "c3": 0.75}, {"time": 0.3821, "angle": -11.73, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.6004, "angle": 4.94}]}, "shaseng_50b": {"rotate": [{"angle": 3.36, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1092, "angle": -7.2, "curve": 0.25, "c3": 0.75}, {"time": 0.4367, "angle": 13.92, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6004, "angle": 3.36}]}, "shaseng_50c": {"rotate": [{"angle": 18.6, "curve": 0.25, "c3": 0.75}, {"time": 0.2729, "angle": -11.14, "curve": 0.25, "c3": 0.75}, {"time": 0.6004, "angle": 18.6}]}, "shaseng_64": {"rotate": [{"angle": -2.95, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0546, "angle": 5.8, "curve": 0.25, "c3": 0.75}, {"time": 0.3821, "angle": -41.64, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.6004, "angle": -2.95}]}, "shaseng_65": {"rotate": [{"angle": -15.45, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1092, "angle": -39.69, "curve": 0.25, "c3": 0.75}, {"time": 0.4367, "angle": 8.78, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6004, "angle": -15.45}]}, "shaseng_66": {"rotate": [{"angle": -0.06, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.1092, "angle": -10.83, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2729, "angle": -28.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4367, "angle": -8.97, "curve": 0.247, "c3": 0.63, "c4": 0.53}, {"time": 0.6004, "angle": -0.06}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2729, "x": 9.96, "y": -4.66, "curve": 0.25, "c3": 0.75}, {"time": 0.6004}], "scale": [{"curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.1092, "x": 0.772, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.2729, "x": 0.642, "y": 0.774, "curve": 0.25, "c3": 0.75}, {"time": 0.6004}]}, "shaseng_67": {"rotate": [{"angle": 1.41, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1637, "angle": -8.76, "curve": 0.249, "c3": 0.627, "c4": 0.51}, {"time": 0.3333, "angle": -18.12, "curve": 0.377, "c2": 0.51, "c3": 0.749}, {"time": 0.4912, "angle": 2.74, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.6004, "angle": 1.41}]}, "shaseng_68": {"rotate": [{"angle": 4.47, "curve": 0.271, "c2": 0.09, "c3": 0.753}, {"time": 0.2729, "angle": -13.42, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3821, "angle": -24.95, "curve": 0.324, "c2": 0.3, "c3": 0.667, "c4": 0.67}, {"time": 0.4667, "angle": -13.74, "curve": 0.381, "c2": 0.54, "c3": 0.743}, {"time": 0.6004, "angle": 4.47}]}, "shaseng_69": {"rotate": [{"angle": 7.4, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0546, "angle": 11.5, "curve": 0.25, "c3": 0.75}, {"time": 0.3821, "angle": -10.7, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.6004, "angle": 7.4}]}, "shaseng_70": {"rotate": [{"angle": -0.55, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0546, "angle": -0.76, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.3275, "angle": -47.93, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.6004, "angle": -0.55}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2729, "x": -6.77, "y": -0.18, "curve": 0.25, "c3": 0.75}, {"time": 0.6004}], "scale": [{"curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.2729, "x": 0.711, "curve": 0.25, "c3": 0.75}, {"time": 0.6004}]}, "shaseng_71": {"rotate": [{"angle": -0.36, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.1092, "angle": -7.03, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2729, "angle": -14.38, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4367, "angle": -3.18, "curve": 0.247, "c3": 0.63, "c4": 0.53}, {"time": 0.6004, "angle": -0.36}]}, "shaseng_72": {"rotate": [{"angle": 2.08, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1637, "angle": -8.08, "curve": 0.249, "c3": 0.627, "c4": 0.51}, {"time": 0.3333, "angle": -12.67, "curve": 0.377, "c2": 0.51, "c3": 0.749}, {"time": 0.4912, "angle": 1.63, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.6004, "angle": 2.08}]}, "shaseng_73": {"rotate": [{"angle": 11.12, "curve": 0.271, "c2": 0.09, "c3": 0.753}, {"time": 0.2729, "angle": -19.37, "curve": 0.25, "c3": 0.75}, {"time": 0.6004, "angle": 11.12}]}, "shaseng_58": {"rotate": [{"angle": 2.81}], "translate": [{"y": 1.63, "curve": 0.25, "c3": 0.75}, {"time": 0.2729, "x": -11.43, "y": 11.32, "curve": 0.25, "c3": 0.75}, {"time": 0.6004, "y": 1.63}]}, "shaseng_12": {"rotate": [{"angle": -6.08, "curve": 0.25, "c3": 0.75}, {"time": 0.2729, "angle": 7.74, "curve": 0.25, "c3": 0.75}, {"time": 0.6004, "angle": -6.08}], "translate": [{"curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 0.2729, "x": -2.94, "y": 0.14, "curve": 0.37, "c2": 0.48, "c3": 0.753}, {"time": 0.6004}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2729, "x": 1.083, "y": 1.083, "curve": 0.25, "c3": 0.75}, {"time": 0.6004}]}, "shaseng_76": {"rotate": [{"angle": -27.57}, {"time": 0.0546, "angle": -1.24}, {"time": 0.1092, "angle": -2.47}, {"time": 0.1637, "angle": -5.56}, {"time": 0.2183, "angle": -8.65}, {"time": 0.2729, "angle": -11.74}, {"time": 0.3275, "angle": -14.82}, {"time": 0.3821, "angle": -12.13}, {"time": 0.4367, "angle": -9.43}, {"time": 0.4912, "angle": -0.22}, {"time": 0.5458, "angle": -17.72}, {"time": 0.6004, "angle": -27.57}]}, "shaseng_75": {"rotate": [{"angle": 14.88}, {"time": 0.0546, "angle": 2.45}, {"time": 0.1092, "angle": 1.63}, {"time": 0.1637, "angle": -6.45}, {"time": 0.2183, "angle": 3.07}, {"time": 0.2729, "angle": 3.79}, {"time": 0.3275, "angle": -8.34}, {"time": 0.3821, "angle": -4.17}, {"time": 0.4367}, {"time": 0.4912, "angle": -19.94}, {"time": 0.5458, "angle": -23.83}, {"time": 0.6004, "angle": 14.88}]}, "shaseng_62": {"rotate": [{"angle": 27.85}, {"time": 0.0546, "angle": 13.41}, {"time": 0.1092, "angle": -2.99}, {"time": 0.1637, "angle": -26.93}, {"time": 0.2183, "angle": -47.66}, {"time": 0.2729, "angle": -55.96}, {"time": 0.3275, "angle": -45.68}, {"time": 0.3821, "angle": -31.17}, {"time": 0.4367, "angle": -15.18}, {"time": 0.4912, "angle": -2.64}, {"time": 0.5458, "angle": 18.62}, {"time": 0.6004, "angle": 27.85}], "scale": [{}, {"time": 0.2729, "x": 1.08, "y": 1.08}, {"time": 0.6004}]}, "shaseng_74": {"rotate": [{"angle": 26.61}, {"time": 0.0546, "angle": 77.26}, {"time": 0.1092, "angle": 98.43}, {"time": 0.1637, "angle": 115.75}, {"time": 0.2183, "angle": 92.05}, {"time": 0.2729, "angle": 52.24}, {"time": 0.3275, "angle": 19.14}, {"time": 0.3821, "angle": 6.07}, {"time": 0.4367, "angle": 7.66}, {"time": 0.4912, "angle": 10.86}, {"time": 0.5458, "angle": 5.68}, {"time": 0.6004, "angle": 26.61}]}, "shaseng_77": {"rotate": [{}, {"time": 0.2729, "angle": -4.3}, {"time": 0.6004}]}}, "deform": {"default": {"shaseng_0": {"shaseng_0": [{"offset": 54, "vertices": [-0.03646, -0.34388, 0.10032, -0.33095, -0.07283, -0.68767, -0.07283, -0.68767, 0, 0, 0, 0, 0.20083, 2.08921, 0.12687, 1.00017, 0.12687, 1.00017, -0.12721, 1.02398, -0.12721, 1.02398, -0.71556, 2.38928, -1.80434, 1.71917], "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1092, "offset": 66, "vertices": [-0.93756, -4.16541, -0.93756, -4.16541, -0.93756, -4.16541, 2.73688, -3.27667, 2.73688, -3.27667, 0.98685, -2.51318, 2.04175, -1.76672, 1.60004, -2.27716], "curve": 0.25, "c3": 0.75}, {"time": 0.4367, "offset": 58, "vertices": [-1.43091, -3.78857, -1.43091, -3.78857, 0, 0, 0, 0, 0.36493, 9.3941, -2.30225, 17.61639, -0.71698, 19.90384, -15.15103, 11.66718, -7.46117, 11.3468, -4.16865, 7.89093, -7.40485, 4.95905, -3.64697, 1.76099], "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6004, "offset": 54, "vertices": [-0.03646, -0.34388, 0.10032, -0.33095, -0.07283, -0.68767, -0.07283, -0.68767, 0, 0, 0, 0, 0.20083, 2.08921, 0.12687, 1.00017, 0.12687, 1.00017, -0.12721, 1.02398, -0.12721, 1.02398, -0.71556, 2.38928, -1.80434, 1.71917]}]}, "shaseng_9": {"shaseng_9": [{"offset": 12, "vertices": [-2.73273, 0.13409, -2.73273, 0.13409, -2.73273, 0.13409, -2.73273, 0.13409, -2.73273, 0.13409], "curve": 0.25, "c3": 0.75}, {"time": 0.2729, "vertices": [-5.41284, 0.26538, -2.11908, 0.104, -5.17743, 0.25391, -6.63367, 0.32538, -10.03156, 0.49207, 3.45929, 2.16278, 16.47137, 6.06879, 16.31036, 2.78424, 16.14917, -0.50031, 16.80505, -3.82495, 16.10089, -18.17609, 5.22906, -11.84473, -7.88086, -5.75006, -4.18262, 0.20508, -2.72638, 0.13361, -0.48505, 0.0238, 0, 0, 0, 0, -1.88953, 0.09271, -3.05096, 0.1496, -2.11908, 0.104, -5.17743, 0.25391, -6.63367, 0.32538, -10.03156, 0.49207, 3.39746, 0.90204, -0.48505, 0.0238, -3.5434, 0.17371, -4.99963, 0.24518, -8.39752, 0.41187, 4.96967, -0.43903, -0.48505, 0.0238, -2.72638, 0.13361, -4.18262, 0.20508, -7.58051, 0.37177, 5.72479, -1.73993], "curve": 0.25, "c3": 0.75}, {"time": 0.6004, "offset": 12, "vertices": [-2.73273, 0.13403, -2.73273, 0.13409, -2.73273, 0.13422, -2.73273, 0.13409, -2.73267, 0.13416]}]}, "shaseng_12": {"shaseng_12": [{"offset": 4, "vertices": [0.7359, 5.46265, 2.15863, 5.07166, 0.7359, 5.46265, 2.15863, 5.07166, 0.36798, 2.73132, 1.07935, 2.53577, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.71613, 10.05591, 4.78918, 9.01703, 10.66333, -12.80087, 6.88452, -15.1709, 2.63867, -25.14502, 3.83716, -22.32959, 3.83716, -22.32959, 6.88452, -15.1709, 0, 0, 0, 0, 0, 0, 0, 0, -0.44025, -2.27449, 0.00332, 2.40954, 0.64071, 2.35587, 2.79392, 5.64713, 1.45275, 2.37103, -0.22147, -0.01129, -0.2197, 0.03872, 1.23278, 6.08642, 2.81836, 5.70703, 0.25318, 1.89232, 0.82585, 1.75804, -0.03079, -2.41095, -0.6588, -2.20276, 0, 0, 0, 0, -0.00933, -2.25244, -0.59605, -2.05561], "curve": 0.25, "c3": 0.75}, {"time": 0.2729, "vertices": [-2.14801, -5.84464, -2.14801, -5.84464, -0.91504, -6.79163, -2.36395, -6.43246, -0.91504, -6.79163, -2.36395, -6.43246, 0, 0, 0, 0, 0, 0, 0, 0, 0.95544, 7.09106, 0.95544, 7.09106, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.40088, 10.39697, 3.60358, 9.85248, 2.28119, 10.44195, 1.2279, 0.86394, 1.38891, 0.78364, 2.51094, 3.56345, 2.91989, 4.435, -1.58387, -13.117, -4.38309, -12.42049, 0.6744, -12.28018, -1.97016, -11.77411, 2.17287, 7.0202, 3.64616, 6.60465, 3.21856, 12.13159, 5.77723, 11.39614, 1.99487, 14.80505, 5.138, 14.02762, 2.29195, 5.25473, 3.39479, 4.87909, 0.44543, 3.30591, 1.1507, 3.13126], "curve": 0.25, "c3": 0.75}, {"time": 0.6004, "offset": 4, "vertices": [0.7359, 5.46265, 2.15863, 5.07166, 0.7359, 5.46265, 2.15863, 5.07166, 0.36798, 2.73132, 1.07935, 2.53577, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.71613, 10.05591, 4.78918, 9.01703, 10.66333, -12.80087, 6.88452, -15.1709, 2.63867, -25.14502, 3.83716, -22.32959, 3.83716, -22.32959, 6.88452, -15.1709, 0, 0, 0, 0, 0, 0, 0, 0, -0.44025, -2.27449, 0.00332, 2.40954, 0.64071, 2.35587, 2.79392, 5.64713, 1.45275, 2.37103, -0.22147, -0.01129, -0.2197, 0.03872, 1.23278, 6.08642, 2.81836, 5.70703, 0.25318, 1.89232, 0.82585, 1.75804, -0.03079, -2.41095, -0.6588, -2.20276, 0, 0, 0, 0, -0.00933, -2.25244, -0.59605, -2.05561]}]}, "shaseng_16": {"shaseng_16": [{"offset": 110, "vertices": [0.46622, -2.23144, 1.5949, -1.62874, 0.46622, -2.23144, 1.5949, -1.62874, 0.46622, -2.23144], "curve": 0.25, "c3": 0.75}, {"time": 0.2729, "curve": 0.25, "c3": 0.75}, {"time": 0.6004, "offset": 110, "vertices": [0.46622, -2.23144, 1.5949, -1.62874, 0.46622, -2.23144, 1.5949, -1.62874, 0.46622, -2.23144]}]}}}}}}