{"skeleton": {"hash": "v+bEKGFpGV3COnXt5ErRlQNHqcM", "spine": "3.8.99", "x": -125.48, "y": -30.71, "width": 202.62, "height": 218.51}, "bones": [{"name": "root"}, {"name": "all", "parent": "root", "scaleX": 0.26, "scaleY": 0.26}, {"name": "all2", "parent": "all", "x": -43.15, "y": 223.13}, {"name": "all3", "parent": "all2", "length": 76.2, "rotation": 92.08, "x": -43.35, "y": -10.34}, {"name": "zhu_12", "parent": "all3", "length": 148.49, "rotation": -2.08, "x": 28.93, "y": -57.63}, {"name": "zhu_13", "parent": "zhu_12", "length": 141.71, "rotation": 1.58, "x": 148.49}, {"name": "zhu_0", "parent": "zhu_13", "length": 107.04, "rotation": 16.47, "x": 126.56, "y": -7.41}, {"name": "zhu_15", "parent": "zhu_13", "length": 139.85, "rotation": 156.91, "x": 126.58, "y": 53.74}, {"name": "zhu_14", "parent": "zhu_15", "length": 108.32, "rotation": -4.56, "x": 127.91, "y": -0.68}, {"name": "zhu_16", "parent": "zhu_14", "length": 52.21, "rotation": -1.85, "x": 108.32}, {"name": "zhu_18", "parent": "zhu_16", "length": 344.69, "rotation": -11.24, "x": 13.05, "y": 1.66}, {"name": "zhu_2", "parent": "zhu_13", "length": 164.38, "rotation": -146.73, "x": 105.9, "y": -135.18}, {"name": "zhu_1", "parent": "zhu_2", "length": 69.68, "rotation": -17.74, "x": 182.49, "y": 5.08}, {"name": "zhu_3", "parent": "zhu_1", "length": 45.16, "rotation": -1.75, "x": 69.68}, {"name": "zhu_4", "parent": "zhu_3", "length": 31.94, "rotation": -30.28, "x": 49.55, "y": 4.11}, {"name": "zhu_5", "parent": "zhu_4", "length": 32.86, "rotation": -53, "x": 31.94}, {"name": "zhu_6", "parent": "zhu_3", "length": 26.9, "rotation": -44.66, "x": 19.69, "y": -22.26}, {"name": "zhu_7", "parent": "zhu_6", "length": 27.51, "rotation": 7.35, "x": 26.9}, {"name": "zhu_8", "parent": "zhu_13", "length": 37.95, "rotation": 173.09, "x": 118.32, "y": 19.27}, {"name": "zhu_9", "parent": "zhu_8", "length": 29.85, "rotation": -16.47, "x": 29.09, "y": -13.48}, {"name": "zhu_10", "parent": "zhu_9", "length": 20.66, "rotation": 9.12, "x": 29.85}, {"name": "zhu_11", "parent": "zhu_10", "length": 28.38, "rotation": 6.56, "x": 20.66}, {"name": "zhu_17", "parent": "zhu_8", "length": 25.7, "rotation": 6.46, "x": 27.53, "y": 8.64}, {"name": "zhu_19", "parent": "zhu_17", "length": 16.82, "rotation": 7.49, "x": 25.7}, {"name": "zhu_20", "parent": "zhu_19", "length": 24.61, "rotation": 2, "x": 16.82}, {"name": "all4", "parent": "all3", "length": 62.1, "rotation": -68.98, "x": 10.07, "y": -119.92}, {"name": "all5", "parent": "all4", "length": 62.92, "rotation": 28.78, "x": 62.1}, {"name": "all6", "parent": "all3", "length": 49.1, "rotation": 21.7, "x": 25.36, "y": 70.03}, {"name": "zhu_21", "parent": "all2", "length": 37.82, "rotation": -119.05, "x": -111.52, "y": -5.62}, {"name": "zhu_22", "parent": "zhu_21", "length": 21.35, "rotation": -20.13, "x": 37.82}, {"name": "zhu_23", "parent": "zhu_22", "length": 43.27, "rotation": 9.01, "x": 20.32, "y": -0.08}, {"name": "zhu_24", "parent": "all2", "length": 32.08, "rotation": -47.42, "x": 142.55, "y": 0.38}, {"name": "zhu_25", "parent": "zhu_24", "length": 35.69, "rotation": -16.02, "x": 32.08}, {"name": "zhu_26", "parent": "zhu_25", "length": 43.57, "rotation": -15.58, "x": 35.69}, {"name": "zhu_27", "parent": "all3", "length": 29.54, "rotation": 177.92, "x": -58.05, "y": 17.66}, {"name": "zhu_28", "parent": "zhu_27", "length": 23.39, "rotation": 1.51, "x": 28.92}, {"name": "zhu_29", "parent": "zhu_28", "length": 27.8, "rotation": 3.57, "x": 23.39}, {"name": "zhu_30", "parent": "all3", "length": 30.55, "rotation": -172.81, "x": -53.44, "y": -25}, {"name": "zhu_31", "parent": "zhu_30", "length": 26.08, "rotation": 10.02, "x": 30.55}, {"name": "zhu_32", "parent": "zhu_31", "length": 22.77, "rotation": -0.37, "x": 26.08}, {"name": "zhu_33", "parent": "zhu_32", "length": 31.04, "rotation": -32.69, "x": 22.77}, {"name": "zhu_34", "parent": "all2", "length": 62.3, "rotation": -97.37, "x": -72.98, "y": -12.75}, {"name": "zhu_35", "parent": "zhu_34", "length": 84.37, "rotation": 9.35, "x": 62.3, "scaleX": 1.2181}, {"name": "zhu_36", "parent": "zhu_35", "length": 55.72, "rotation": 5.12, "x": 84.37, "scaleX": 0.6576}, {"name": "zhu_37", "parent": "zhu_36", "length": 42.8, "rotation": -84.02, "x": 55.72}, {"name": "zhu_38", "parent": "zhu_37", "length": 60.46, "rotation": -2.68, "x": 42.8}, {"name": "zhu_39", "parent": "all2", "length": 98.06, "rotation": -77.29, "x": 55.96, "y": -16.15}, {"name": "zhu_40", "parent": "zhu_39", "length": 101, "rotation": 13.49, "x": 98.06, "scaleX": 1.2082}, {"name": "zhu_41", "parent": "zhu_40", "length": 70.06, "rotation": -7.02, "x": 98.11, "y": -0.98, "scaleX": 0.677, "scaleY": 1.001}, {"name": "zhu_42", "parent": "zhu_41", "length": 46.28, "rotation": -58.6, "x": 66.4, "y": -1.77}, {"name": "zhu_43", "parent": "zhu_42", "length": 54.7, "rotation": -19.33, "x": 46.28}, {"name": "t2", "parent": "all", "x": 87.26, "y": 1.47}, {"name": "t1", "parent": "all", "x": -89.8, "y": 47.61}, {"name": "xy", "parent": "zhu_18", "x": 106, "y": 327.45}, {"name": "cy", "parent": "all"}, {"name": "tx", "parent": "all"}, {"name": "1", "parent": "tx"}, {"name": "2", "parent": "tx"}, {"name": "3", "parent": "tx"}, {"name": "4", "parent": "tx"}, {"name": "5", "parent": "tx"}, {"name": "6", "parent": "tx"}, {"name": "zhu_45", "parent": "zhu_0", "length": 36.84, "rotation": -10.25, "x": 51.04, "y": -18.15}, {"name": "zhu_46", "parent": "zhu_45", "length": 36, "rotation": -7, "x": 36.84}, {"name": "zhu_47", "parent": "zhu_46", "length": 38.01, "rotation": -64.23, "x": 36}, {"name": "zhu_48", "parent": "zhu_47", "length": 47.03, "rotation": -70.27, "x": 35.33, "y": -0.89}, {"name": "zhu_49", "parent": "zhu_48", "length": 38.58, "rotation": -3.92, "x": 47.03}, {"name": "zhu_50", "parent": "zhu_49", "length": 38.63, "rotation": 26.38, "x": 38.91, "y": 0.37}, {"name": "zhu_51", "parent": "zhu_13", "x": 27.92, "y": 21.9}, {"name": "zxct", "parent": "root"}, {"name": "yunshi", "parent": "zxct"}, {"name": "bbbb", "parent": "zxct"}, {"name": "zxc", "parent": "zxct"}, {"name": "sss", "parent": "zxct"}, {"name": "sss2", "parent": "zxct"}, {"name": "yunshi2", "parent": "zxct"}, {"name": "dluie", "parent": "zxct"}, {"name": "bz", "parent": "zxct"}, {"name": "bz2", "parent": "zxct"}], "slots": [{"name": "105027tcxrjrcjppgu7uug", "bone": "dluie"}, {"name": "yunshi", "bone": "yunshi"}, {"name": "yunshi2", "bone": "yunshi2"}, {"name": "101944w3nhjcmqalolnlnl", "bone": "sss"}, {"name": "101944w3nhjcmqalolnlnl2", "bone": "sss2"}, {"name": "bz", "bone": "bz"}, {"name": "bz2", "bone": "bz2"}, {"name": "zhu_15", "bone": "zhu_18", "attachment": "zhu_15"}, {"name": "zhu_17", "bone": "zhu_45", "attachment": "zhu_17"}, {"name": "zhu_14", "bone": "zhu_14", "attachment": "zhu_14"}, {"name": "zhu_13", "bone": "zhu_15", "attachment": "zhu_13"}, {"name": "zhu_10", "bone": "zhu_34", "attachment": "zhu_10"}, {"name": "zhu_11", "bone": "zhu_39", "attachment": "zhu_11"}, {"name": "zhu_12", "bone": "zhu_12", "attachment": "zhu_12"}, {"name": "zhu_8", "bone": "zhu_24", "attachment": "zhu_8"}, {"name": "zhu_7", "bone": "zhu_21", "attachment": "zhu_7"}, {"name": "zhu_6", "bone": "zhu_27", "attachment": "zhu_6"}, {"name": "zhu_5", "bone": "zhu_30", "attachment": "zhu_5"}, {"name": "zhu_4", "bone": "all3", "attachment": "zhu_4"}, {"name": "zhu_3", "bone": "zhu_8", "attachment": "zhu_3"}, {"name": "zhu_16", "bone": "zhu_51", "attachment": "zhu_16"}, {"name": "zhu_0", "bone": "zhu_0", "attachment": "zhu_0"}, {"name": "zhu_2", "bone": "zhu_2", "attachment": "zhu_2"}, {"name": "zhu_1", "bone": "zhu_1", "attachment": "zhu_1"}, {"name": "bbb", "bone": "xy"}, {"name": "3", "bone": "3"}, {"name": "a_canying", "bone": "cy"}, {"name": "dg_04", "bone": "1"}, {"name": "dg_4", "bone": "6"}, {"name": "dg_17", "bone": "2"}, {"name": "4", "bone": "4"}, {"name": "5", "bone": "5"}, {"name": "round_064_00000", "bone": "bbbb"}, {"name": "zxc", "bone": "zxc"}], "ik": [{"name": "t1", "order": 1, "bones": ["zhu_34", "zhu_35"], "target": "t1"}, {"name": "t2", "bones": ["zhu_39", "zhu_40"], "target": "t2"}], "skins": [{"name": "default", "attachments": {"3": {"dg_xl_30.png Animation_00000": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [128, -128, -128, -128, -128, 128, 128, 128], "hull": 4}, "dg_xl_30.png Animation_00001": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [128, -128, -128, -128, -128, 128, 128, 128], "hull": 4}, "dg_xl_30.png Animation_00002": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [128, -128, -128, -128, -128, 128, 128, 128], "hull": 4}, "dg_xl_30.png Animation_00003": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [128, -128, -128, -128, -128, 128, 128, 128], "hull": 4}, "dg_xl_30.png Animation_00004": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [128, -128, -128, -128, -128, 128, 128, 128], "hull": 4}, "dg_xl_30.png Animation_00005": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [128, -128, -128, -128, -128, 128, 128, 128], "hull": 4}, "dg_xl_30.png Animation_00006": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [128, -128, -128, -128, -128, 128, 128, 128], "hull": 4}, "dg_xl_30.png Animation_00007": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [128, -128, -128, -128, -128, 128, 128, 128], "hull": 4}}, "4": {"dg_xl_31.png Animation_00000": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [128, -128, -128, -128, -128, 128, 128, 128], "hull": 4}, "dg_xl_31.png Animation_00001": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [128, -128, -128, -128, -128, 128, 128, 128], "hull": 4}, "dg_xl_31.png Animation_00002": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [128, -128, -128, -128, -128, 128, 128, 128], "hull": 4}, "dg_xl_31.png Animation_00003": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [128, -128, -128, -128, -128, 128, 128, 128], "hull": 4}}, "5": {"dg_xl_31.png Animation_00000": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [128, -128, -128, -128, -128, 128, 128, 128], "hull": 4}, "dg_xl_31.png Animation_00001": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [128, -128, -128, -128, -128, 128, 128, 128], "hull": 4}, "dg_xl_31.png Animation_00002": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [128, -128, -128, -128, -128, 128, 128, 128], "hull": 4}, "dg_xl_31.png Animation_00003": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [128, -128, -128, -128, -128, 128, 128, 128], "hull": 4}}, "101944w3nhjcmqalolnlnl": {"101944w3nhjcmqalolnlnl": {"width": 128, "height": 128}}, "101944w3nhjcmqalolnlnl2": {"101944w3nhjcmqalolnlnl": {"width": 128, "height": 128}}, "105027tcxrjrcjppgu7uug": {"105027tcxrjrcjppgu7uug": {"width": 256, "height": 256}}, "a_canying": {"a_canying2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [112, -58, -112, -58, -112, 59, 112, 59], "hull": 4}}, "bbb": {"bbb2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [33, -31, -33, -31, -33, 32, 33, 32], "hull": 4}}, "bz": {"bao_00001": {"y": 0.5, "width": 242, "height": 221}, "bao_00003": {"y": 0.5, "width": 242, "height": 221}, "bao_00005": {"y": 0.5, "width": 242, "height": 221}, "bao_00007": {"y": 0.5, "width": 242, "height": 221}, "bao_00009": {"y": 0.5, "width": 242, "height": 221}, "bao_00011": {"y": 0.5, "width": 242, "height": 221}, "bao_00013": {"y": 0.5, "width": 242, "height": 221}, "bao_00015": {"y": 0.5, "width": 242, "height": 221}, "bao_00017": {"y": 0.5, "width": 242, "height": 221}, "bao_00019": {"y": 0.5, "width": 242, "height": 221}}, "bz2": {"bao_00001": {"y": 0.5, "width": 242, "height": 221}, "bao_00003": {"y": 0.5, "width": 242, "height": 221}, "bao_00005": {"y": 0.5, "width": 242, "height": 221}, "bao_00007": {"y": 0.5, "width": 242, "height": 221}, "bao_00009": {"y": 0.5, "width": 242, "height": 221}, "bao_00011": {"y": 0.5, "width": 242, "height": 221}, "bao_00013": {"y": 0.5, "width": 242, "height": 221}, "bao_00015": {"y": 0.5, "width": 242, "height": 221}, "bao_00017": {"y": 0.5, "width": 242, "height": 221}, "bao_00019": {"y": 0.5, "width": 242, "height": 221}}, "dg_04": {"dg_04": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -64, -64, -64, -64, 64, 64, 64], "hull": 4}}, "dg_4": {"dg_04": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -64, -64, -64, -64, 64, 64, 64], "hull": 4}}, "dg_17": {"dg_17": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -64, -64, -64, -64, 64, 64, 64], "hull": 4}}, "round_064_00000": {"round_064_00000": {"width": 128, "height": 128}}, "yunshi": {"yunshi_00000": {"width": 260, "height": 290}, "yunshi_00001": {"width": 260, "height": 290}, "yunshi_00002": {"width": 260, "height": 290}, "yunshi_00003": {"width": 260, "height": 290}, "yunshi_00004": {"width": 260, "height": 290}, "yunshi_00005": {"width": 260, "height": 290}, "yunshi_00006": {"width": 260, "height": 290}, "yunshi_00007": {"width": 260, "height": 290}, "yunshi_00008": {"width": 260, "height": 290}, "yunshi_00009": {"width": 260, "height": 290}, "yunshi_00010": {"width": 260, "height": 290}, "yunshi_00011": {"width": 260, "height": 290}, "yunshi_00012": {"width": 260, "height": 290}, "yunshi_00013": {"width": 260, "height": 290}}, "yunshi2": {"yunshi_00000": {"width": 260, "height": 290}, "yunshi_00001": {"width": 260, "height": 290}, "yunshi_00002": {"width": 260, "height": 290}, "yunshi_00003": {"width": 260, "height": 290}, "yunshi_00004": {"width": 260, "height": 290}, "yunshi_00005": {"width": 260, "height": 290}, "yunshi_00006": {"width": 260, "height": 290}, "yunshi_00007": {"width": 260, "height": 290}, "yunshi_00008": {"width": 260, "height": 290}, "yunshi_00009": {"width": 260, "height": 290}, "yunshi_00010": {"width": 260, "height": 290}, "yunshi_00011": {"width": 260, "height": 290}, "yunshi_00012": {"width": 260, "height": 290}, "yunshi_00013": {"width": 260, "height": 290}}, "zhu_0": {"zhu_0": {"type": "mesh", "uvs": [0.26901, 0.12919, 0.27418, 0.29732, 0.34608, 0.69588, 0.04312, 0.34387, 0, 0.36406, 0.04396, 0.61811, 0.19509, 0.79973, 0.52578, 0.99689, 0.61411, 0.99822, 0.90596, 0.86521, 1, 0.6373, 1, 0.40675, 0.87164, 0.07835, 0.63562, 0, 0.52603, 0], "triangles": [1, 0, 14, 2, 5, 3, 4, 3, 5, 13, 2, 1, 13, 1, 14, 13, 11, 2, 6, 5, 2, 12, 11, 13, 10, 2, 11, 10, 9, 2, 8, 7, 2, 6, 2, 7, 9, 8, 2], "vertices": [132.9, 42.95, 104.95, 51.07, 35.32, 59.92, 110.4, 93.77, 109.52, 102.35, 65.24, 108.32, 26.8, 91.76, -24.38, 44.79, -29.61, 29.5, -24.28, -28.41, 7.87, -56.99, 45.79, -69.35, 107.09, -64.62, 133.36, -27.76, 139.58, -8.69], "hull": 15}}, "zhu_1": {"zhu_1": {"type": "mesh", "uvs": [0, 0.22532, 0, 0.34132, 0.27428, 0.56251, 0.17733, 0.69567, 0.11328, 0.83363, 0.23149, 0.87203, 0.3317, 0.8441, 0.42374, 0.74756, 0.46979, 0.77586, 0.45327, 0.79149, 0.39625, 0.79854, 0.27184, 0.88679, 0.41282, 0.99576, 0.48296, 0.9987, 0.66103, 0.93918, 0.77798, 0.83488, 0.77622, 0.75645, 0.88043, 0.75282, 0.85944, 0.6147, 0.99999, 0.57094, 1, 0.08985, 0.66713, 0, 0.41023, 0, 0.55784, 0.48736, 0.65216, 0.60659], "triangles": [5, 4, 6, 6, 4, 3, 7, 6, 3, 14, 13, 9, 11, 10, 12, 9, 13, 10, 13, 12, 10, 14, 9, 15, 15, 9, 8, 8, 16, 15, 16, 8, 24, 16, 18, 17, 16, 24, 18, 8, 7, 24, 3, 2, 7, 7, 2, 24, 19, 18, 23, 2, 23, 24, 18, 24, 23, 19, 23, 20, 2, 1, 23, 23, 0, 22, 23, 21, 20, 23, 22, 21, 23, 1, 0], "vertices": [2, 12, -17.19, -44.91, 0.99881, 16, -57.02, -91.9, 0.00119, 1, 12, 8.98, -52.96, 1, 3, 12, 69.68, -33.19, 0.8454, 13, 1.01, -33.17, 0.06398, 16, -5.62, -20.89, 0.09062, 3, 12, 95.89, -54.85, 0.00015, 16, 28.14, -16.84, 0.51766, 17, -0.92, -16.86, 0.48219, 1, 17, 32.48, -12.64, 1, 1, 17, 34.97, 5.44, 1, 5, 13, 67.13, -43.34, 0.00591, 14, 39.11, -32.11, 7e-05, 15, 29.96, -13.6, 3e-05, 16, 48.56, 18.35, 0.00147, 17, 23.83, 15.43, 0.99253, 5, 13, 48.43, -25.42, 0.53564, 14, 13.92, -26.07, 0.16703, 15, 9.97, -30.08, 0.07272, 16, 22.66, 17.95, 0.11582, 17, -1.91, 18.35, 0.1088, 4, 13, 56.5, -21.24, 0.26974, 14, 18.78, -18.38, 0.41699, 15, 6.76, -21.57, 0.27362, 17, 1.97, 26.57, 0.03966, 4, 13, 59.47, -24.35, 0.14849, 14, 22.92, -19.57, 0.34194, 15, 10.2, -18.99, 0.49121, 17, 6.22, 25.89, 0.01836, 4, 13, 59.05, -32.16, 0.0569, 14, 26.49, -26.53, 0.18738, 15, 17.91, -20.32, 0.75137, 17, 10.62, 19.43, 0.00436, 3, 13, 74.72, -53.75, 0.00013, 14, 50.91, -37.27, 0.00098, 15, 41.18, -7.28, 0.99889, 1, 15, 33.33, 23.65, 1, 1, 15, 24.88, 27.83, 1, 2, 14, 49.42, 16.31, 0.18343, 15, -2.51, 23.77, 0.81657, 2, 14, 21.6, 25.11, 0.97259, 15, -26.28, 6.85, 0.02741, 3, 13, 62.95, 19.57, 0.00691, 14, 3.78, 20.11, 0.97766, 15, -33.01, -10.39, 0.01543, 2, 13, 65.82, 33.26, 0.01581, 14, -0.65, 33.38, 0.98419, 3, 12, 104.51, 38.13, 0.11345, 13, 33.64, 39.18, 0.06262, 14, -31.42, 22.27, 0.82392, 3, 12, 100.18, 59.17, 0.37738, 13, 28.67, 60.08, 0.07218, 14, -46.25, 37.8, 0.55044, 3, 12, -8.34, 92.56, 0.93096, 13, -80.82, 90.13, 0.06731, 14, -155.96, 8.55, 0.00173, 2, 12, -41.72, 56.17, 0.98631, 13, -113.07, 52.73, 0.01369, 2, 12, -51.85, 23.26, 0.99971, 13, -122.18, 19.54, 0.00029, 3, 12, 63.9, 8.35, 0.92889, 13, -6.03, 8.17, 0.05584, 14, -50.04, -24.53, 0.01527, 3, 12, 94.51, 12.15, 0.7242, 13, 24.45, 12.9, 0.1237, 14, -26.11, -5.06, 0.15211], "hull": 23}}, "zhu_2": {"zhu_2": {"type": "mesh", "uvs": [0.99998, 0.6773, 0.99439, 0.98575, 0.67451, 1, 0.45192, 0.91678, 0.18291, 0.67784, 0.0508, 0.60343, 1e-05, 0.39349, 0.02554, 0.28603, 0.02574, 0.16806, 0.09744, 0.05605, 0.18405, 0, 0.50944, 0.03352, 0.76411, 0.26584, 0.87093, 0.48198], "triangles": [8, 11, 7, 7, 11, 6, 5, 6, 11, 11, 8, 9, 11, 9, 10, 4, 11, 12, 4, 12, 13, 4, 5, 11, 3, 4, 13, 3, 13, 0, 0, 2, 3, 1, 2, 0], "vertices": [152.58, 49.07, 200.93, 14.33, 174.13, -28.98, 140.72, -48.85, 78.44, -57.61, 54.65, -66.64, 16.78, -50.12, 2.08, -34.94, -16.59, -21.9, -27.82, -0.19, -28.83, 17.29, 6.05, 56.05, 65.98, 63.67, 109.92, 53.77], "hull": 14}}, "zhu_3": {"zhu_3": {"type": "mesh", "uvs": [0.5451, 0, 0.1539, 0.14635, 0.05056, 0.30564, 0.07824, 0.51974, 0.15759, 0.63108, 0.12806, 0.74755, 0.08378, 0.91369, 0.20188, 0.9685, 0.29599, 0.8349, 0.3047, 0.72266, 0.31048, 0.85657, 0.35708, 1, 0.38371, 1, 0.47292, 0.88747, 0.59675, 0.84421, 0.57678, 0.71568, 0.53814, 0.65302, 0.8805, 0.48274, 1, 0.33068, 0.99186, 0.08953, 0.67548, 0, 0.34147, 0.50452, 0.45626, 0.47679, 0.22354, 0.54831, 0.31601, 0.62104, 0.4851, 0.58217, 0.6752, 0.43805, 0.80359, 0.24018, 0.16649, 0.41332, 0.37081, 0.43764, 0.30699, 0.32787, 0.60495, 0.25817], "triangles": [13, 12, 10, 12, 11, 10, 13, 10, 16, 13, 15, 14, 13, 16, 15, 16, 10, 9, 24, 16, 9, 24, 25, 16, 16, 26, 17, 16, 25, 26, 21, 22, 25, 25, 22, 26, 26, 27, 17, 17, 27, 18, 26, 22, 31, 26, 31, 27, 27, 19, 18, 7, 6, 8, 6, 5, 8, 8, 5, 9, 5, 4, 9, 9, 4, 24, 4, 23, 24, 4, 3, 23, 3, 28, 23, 23, 28, 21, 3, 2, 28, 24, 21, 25, 24, 23, 21, 21, 29, 22, 21, 28, 29, 22, 29, 31, 28, 30, 29, 29, 30, 31, 28, 2, 30, 2, 1, 30, 31, 30, 0, 30, 1, 0, 31, 20, 27, 31, 0, 20, 27, 20, 19], "vertices": [2, 18, -81.09, 25.43, 0.74652, 22, -106.04, 28.89, 0.25348, 3, 18, -46.09, -42.42, 0.81716, 19, -63.9, -49.07, 0.18253, 22, -78.9, -42.46, 0.00031, 2, 18, -13.43, -58.16, 0.55707, 19, -28.11, -54.9, 0.44293, 2, 18, 27.67, -49.29, 0.0862, 19, 8.79, -34.74, 0.9138, 2, 19, 23.62, -13.34, 0.8749, 20, -8.26, -12.19, 0.1251, 2, 20, 15.07, -12.42, 0.79711, 21, -6.97, -11.7, 0.20289, 1, 21, 26.09, -16.21, 1, 1, 21, 34.44, 6.18, 1, 4, 18, 85.2, -4.33, 0.00182, 20, 25.01, 20.98, 0.22767, 21, 6.72, 20.34, 0.73613, 23, 27.36, -23.14, 0.03438, 7, 18, 63.26, -4.8, 0.06921, 19, 30.31, 18.01, 0.15048, 20, 3.31, 17.71, 0.28447, 21, -15.21, 19.58, 0.02998, 22, 33.99, -17.37, 0.02224, 23, 5.96, -18.31, 0.39871, 24, -11.49, -17.92, 0.0449, 5, 18, 89.17, -1.33, 0.00032, 19, 54.17, 28.68, 0.0007, 20, 28.56, 24.46, 0.00818, 23, 31.93, -21.18, 0.11636, 24, 14.36, -21.7, 0.87444, 1, 24, 43.41, -18.56, 1, 1, 24, 44.3, -13.82, 1, 2, 23, 42.29, 6.98, 0.01431, 24, 25.7, 6.09, 0.98569, 3, 22, 58.73, 35.01, 0.00132, 23, 37.31, 30.41, 0.19791, 24, 21.54, 29.67, 0.80077, 3, 22, 33.6, 31.89, 0.19061, 23, 11.99, 30.59, 0.44107, 24, -3.76, 30.74, 0.36833, 3, 22, 21.25, 25.14, 0.79381, 23, -1.14, 25.5, 0.12395, 24, -17.05, 26.12, 0.08223, 2, 18, 7, 94.62, 0.25566, 22, -10.74, 87.74, 0.74434, 2, 18, -24.54, 113.4, 0.34894, 22, -39.96, 109.95, 0.65106, 2, 18, -71.22, 107.57, 0.4418, 22, -87.01, 109.4, 0.5582, 2, 18, -83.28, 48.93, 0.64482, 22, -105.58, 52.49, 0.35518, 2, 18, 20.29, -2.12, 0.94255, 19, -11.66, 8.39, 0.05745, 2, 18, 12.98, 18.06, 0.35657, 22, -13.4, 10.99, 0.64343, 2, 18, 30.78, -22.58, 0.00082, 19, 4.2, -8.25, 0.99918, 5, 18, 43.34, -4.6, 0.57998, 19, 11.15, 12.55, 0.31776, 20, -16.47, 15.36, 0.01276, 22, 14.22, -14.94, 0.04957, 23, -13.33, -13.31, 0.03993, 3, 22, 7.24, 15.81, 0.95889, 23, -16.24, 18.08, 0.04058, 24, -32.4, 19.23, 0.00053, 2, 18, 1.77, 56.82, 0.25447, 22, -20.18, 50.76, 0.74553, 2, 18, -38.8, 76.37, 0.43437, 22, -58.3, 74.75, 0.56563, 2, 18, 5.53, -35.31, 0.39477, 19, -16.41, -27.62, 0.60523, 2, 18, 6.81, 1.95, 0.97488, 22, -21.34, -4.32, 0.02512, 3, 18, -13.43, -11.54, 0.88705, 19, -41.32, -10.19, 0.1122, 22, -42.97, -15.45, 0.00075, 2, 18, -31.97, 40.9, 0.59678, 22, -55.5, 38.74, 0.40322], "hull": 21}}, "zhu_4": {"zhu_4": {"type": "mesh", "uvs": [0.15456, 0.26014, 0.13181, 0, 0.07453, 0.05877, 0, 0.49373, 0, 0.6951, 0.07706, 0.72463, 0.12643, 0.86695, 0.21741, 0.87634, 0.39264, 1, 0.45834, 1, 0.5847, 0.81593, 0.72688, 0.88693, 0.91507, 0.77735, 1, 0.51783, 1, 0.34944, 0.9416, 0.22449, 0.92351, 0, 0.87888, 0, 0.84027, 0.16105, 0.66054, 0.19758, 0.56041, 0.35329, 0.40842, 0.15721, 0.2142, 0.14183, 0.25912, 0.78137, 0.11871, 0.55991], "triangles": [15, 14, 13, 18, 16, 15, 16, 18, 17, 13, 18, 15, 13, 12, 18, 19, 18, 12, 12, 20, 19, 12, 11, 20, 1, 0, 2, 3, 2, 0, 4, 3, 24, 5, 4, 24, 24, 0, 23, 5, 24, 23, 6, 5, 23, 21, 0, 22, 23, 21, 20, 23, 0, 21, 23, 20, 10, 7, 6, 23, 8, 23, 10, 7, 23, 8, 9, 8, 10, 20, 11, 10, 24, 3, 0], "vertices": [2, 3, 84.61, 52.81, 0.944, 27, 48.69, -37.9, 0.056, 1, 27, 104.02, -53.61, 1, 2, 3, 129.7, 79.12, 7e-05, 27, 100.31, -30.13, 0.99993, 1, 27, 23.63, 32.09, 1, 2, 3, -8.62, 110.18, 0.00828, 27, -16.73, 49.87, 0.99172, 2, 3, -16.06, 83.54, 0.94547, 27, -33.49, 27.87, 0.05453, 1, 3, -47.83, 67.45, 1, 3, 3, -51.04, 35.8, 0.96557, 27, -83.65, -3.56, 0.024, 25, -167.28, -1.18, 0.01043, 2, 3, -80.33, -24.33, 0.99349, 27, -133.09, -48.6, 0.00651, 2, 3, -81.16, -47.25, 0.9999, 27, -142.34, -69.58, 0.0001, 2, 3, -42.48, -92.78, 0.91723, 25, -44.19, -39.32, 0.08277, 2, 3, -59.82, -141.81, 0.01163, 25, -4.65, -73.09, 0.98837, 2, 25, 65.18, -76.79, 0.82399, 26, -34.27, -68.79, 0.17601, 2, 25, 114.74, -36.15, 0.1637, 26, 28.75, -57.03, 0.8363, 2, 25, 129.22, -2.23, 0.00356, 26, 57.76, -34.27, 0.99644, 1, 26, 66.71, -1.34, 1, 1, 26, 101.49, 33.98, 1, 1, 26, 91.87, 46.23, 1, 2, 25, 94.14, 57.59, 0.07096, 26, 55.81, 35.06, 0.92904, 3, 3, 91.89, -124.15, 0.02163, 25, 33.3, 74.85, 0.69557, 26, 10.79, 79.47, 0.2828, 2, 3, 59.08, -87.99, 0.88855, 25, -12.22, 57.2, 0.11145, 1, 3, 103.92, -36.54, 1, 1, 3, 109.75, 31.07, 1, 1, 3, -30.79, 20.5, 1, 2, 3, 19.46, 67.7, 0.95085, 27, -6.34, 0.02, 0.04915], "hull": 23}}, "zhu_5": {"zhu_5": {"type": "mesh", "uvs": [0, 0.03727, 0, 0.29573, 0.13038, 0.52835, 0.39306, 0.69473, 0.20085, 0.86596, 0.39946, 1, 0.76465, 1, 1, 0.89217, 1, 0.70317, 0.89278, 0.43825, 0.70378, 0.23021, 0.55643, 0], "triangles": [7, 6, 3, 3, 6, 5, 5, 4, 3, 3, 8, 7, 3, 9, 8, 3, 2, 9, 2, 10, 9, 2, 1, 10, 10, 0, 11, 10, 1, 0], "vertices": [1, 37, -5.41, -16.05, 1, 2, 37, 24.44, -20.93, 0.73355, 38, -9.66, -19.54, 0.26645, 3, 37, 52.54, -17.72, 0.00496, 38, 18.57, -21.27, 0.85365, 39, -7.38, -21.32, 0.14139, 3, 38, 42.06, -13.08, 0.02986, 39, 16.06, -12.98, 0.73922, 40, 1.36, -14.54, 0.23092, 2, 39, 31.34, -30.2, 0.06853, 40, 23.52, -20.79, 0.93147, 2, 39, 49.97, -24.2, 0.00063, 40, 35.96, -5.68, 0.99937, 2, 39, 56.96, -3.82, 0.00589, 40, 30.84, 15.25, 0.99411, 2, 39, 49.53, 13.41, 0.15233, 40, 15.28, 25.74, 0.84767, 3, 38, 54.82, 20.4, 0.00101, 39, 28.61, 20.58, 0.76221, 40, -6.2, 20.48, 0.23678, 3, 37, 49.38, 28.37, 0.08279, 38, 23.48, 24.66, 0.45088, 39, -2.76, 24.65, 0.46633, 3, 37, 23.56, 21.29, 0.74047, 38, -3.18, 22.18, 0.25082, 39, -29.4, 21.99, 0.00871, 1, 37, -4.42, 17.05, 1], "hull": 12}}, "zhu_6": {"zhu_6": {"type": "mesh", "uvs": [0.05453, 0, 0.00586, 0.27477, 0.03019, 0.5224, 0.08929, 0.69381, 0, 0.89134, 0.33957, 1, 0.66286, 1, 1, 0.91138, 0.89924, 0.73818, 0.94095, 0.51632, 0.89229, 0.2774, 0.83319, 0], "triangles": [8, 3, 9, 5, 3, 8, 6, 5, 8, 4, 3, 5, 7, 6, 8, 10, 2, 1, 9, 3, 2, 9, 2, 10, 1, 0, 11, 11, 10, 1], "vertices": [1, 34, -9.26, -13.96, 1, 2, 34, 18.76, -16.01, 0.76063, 35, -10.58, -15.73, 0.23937, 2, 35, 14.7, -15.38, 0.9602, 36, -9.64, -14.81, 0.0398, 2, 35, 32.24, -13.36, 0.13712, 36, 8, -13.88, 0.86288, 1, 36, 27.74, -19.4, 1, 1, 36, 40.04, -6.18, 1, 1, 36, 41.24, 7.35, 1, 2, 35, 55.43, 24.3, 0.00419, 36, 33.49, 22.25, 0.99581, 2, 35, 37.66, 20.53, 0.17198, 36, 15.52, 19.6, 0.82802, 3, 34, 43.4, 23.27, 0.00166, 35, 15.08, 22.88, 0.87741, 36, -6.87, 23.35, 0.12094, 2, 34, 19.03, 21.22, 0.56435, 35, -9.33, 21.48, 0.43565, 2, 34, -9.26, 18.74, 0.99832, 35, -37.68, 19.74, 0.00168], "hull": 12}}, "zhu_7": {"zhu_7": {"type": "mesh", "uvs": [1, 0.21165, 0.67444, 0, 0.5718, 0.08039, 0.4719, 0.18484, 0.40814, 0.33302, 0.40178, 0.46242, 0.24371, 0.47307, 0.1468, 0.53807, 0, 0.80223, 0.02849, 0.84725, 0.0957, 0.78021, 0.16018, 0.7693, 0.30732, 0.92988, 0.459, 1, 0.48876, 0.61914, 0.61885, 0.69873, 0.78552, 0.64494, 0.87907, 0.48825, 0.61772, 0.40083, 0.70503, 0.23668], "triangles": [12, 11, 14, 13, 12, 14, 14, 6, 5, 7, 6, 14, 9, 8, 10, 10, 8, 7, 10, 7, 11, 14, 11, 7, 15, 14, 18, 14, 5, 18, 16, 15, 18, 16, 18, 17, 5, 4, 18, 18, 19, 17, 17, 19, 0, 4, 3, 18, 18, 3, 19, 3, 2, 19, 2, 1, 19, 19, 1, 0], "vertices": [1, 28, -2.62, 25.29, 1, 1, 28, -8.89, -18.79, 1, 3, 28, 5.47, -23.61, 0.98893, 29, -22.24, -33.3, 0.00026, 30, -47.24, -26.15, 0.01081, 3, 28, 22.36, -26.69, 0.88452, 29, -5.33, -30.38, 0.06012, 30, -30.08, -25.91, 0.05535, 3, 28, 42.18, -23.62, 0.36393, 29, 12.23, -20.68, 0.39446, 30, -11.21, -19.08, 0.24161, 3, 28, 56.88, -16.25, 0.01406, 29, 23.49, -8.69, 0.44483, 30, 1.79, -9.01, 0.54112, 2, 29, 37.42, -18.93, 0.16649, 30, 13.94, -21.3, 0.83351, 2, 29, 50.81, -19.59, 0.07838, 30, 27.06, -24.04, 0.92162, 1, 30, 63.02, -14.62, 1, 1, 30, 65.38, -8.56, 1, 1, 30, 54.15, -8.46, 1, 2, 29, 68.9, 3.59, 4e-05, 30, 48.56, -3.98, 0.99996, 1, 30, 53.79, 21.43, 1, 1, 30, 49.93, 39.81, 1, 3, 28, 69.68, 1.71, 0.00023, 29, 29.33, 12.57, 0.08309, 30, 10.88, 11.08, 0.91668, 3, 28, 71.63, 19.01, 0.04324, 29, 25.2, 29.48, 0.48835, 30, 9.45, 28.43, 0.46841, 3, 28, 56.83, 31.57, 0.22585, 29, 6.99, 36.19, 0.56578, 30, -7.49, 37.9, 0.20838, 3, 28, 34.49, 30.82, 0.70278, 29, -13.73, 27.79, 0.27021, 30, -29.27, 32.86, 0.02701, 2, 28, 38.62, 0.53, 0.31841, 29, 0.57, 0.77, 0.68159, 2, 28, 15.77, -1.28, 0.99955, 30, -41.44, -2.25, 0.00045], "hull": 18}}, "zhu_8": {"zhu_8": {"type": "mesh", "uvs": [0.3731, 0.26875, 0, 0.44, 0.04986, 0.63856, 0.16684, 0.79288, 0.28628, 0.90704, 0.32244, 0.98274, 0.38161, 0.97902, 0.43952, 0.8343, 0.51038, 0.81758, 0.5551, 0.95858, 0.61965, 0.99999, 0.82888, 0.90947, 0.96464, 0.6676, 0.99999, 0.53851, 0.91744, 0.59058, 0.78664, 0.38225, 0.70008, 0.25319, 0.63543, 0, 0.56311, 0, 0.43389, 0.48795, 0.56462, 0.57344, 0.69903, 0.7486], "triangles": [11, 10, 21, 10, 9, 21, 9, 8, 21, 12, 21, 14, 12, 11, 21, 8, 20, 21, 14, 21, 15, 12, 14, 13, 5, 4, 6, 6, 4, 7, 4, 3, 7, 20, 8, 7, 20, 7, 19, 21, 20, 15, 20, 16, 15, 20, 19, 16, 7, 3, 19, 3, 2, 19, 2, 1, 19, 16, 0, 18, 18, 17, 16, 1, 0, 19, 16, 19, 0], "vertices": [1, 31, -5.62, 1.69, 1, 3, 31, -32.14, -69.2, 0.84366, 32, -42.64, -84.23, 0.15633, 33, -52.83, -102.18, 1e-05, 3, 31, -1.53, -84.6, 0.74147, 32, -8.97, -90.59, 0.23603, 33, -18.68, -99.25, 0.0225, 3, 31, 32.21, -85.73, 0.53699, 32, 23.78, -82.37, 0.36064, 33, 10.65, -82.54, 0.10237, 3, 31, 61.36, -82.02, 0.18908, 32, 50.77, -70.76, 0.4496, 33, 33.54, -64.11, 0.36132, 3, 31, 75.21, -85.52, 0.16612, 32, 65.05, -70.3, 0.45135, 33, 47.17, -59.83, 0.38253, 3, 31, 82.28, -76.91, 0.16251, 32, 69.47, -60.08, 0.44958, 33, 48.68, -48.79, 0.3879, 3, 31, 71.96, -52.64, 0.13386, 32, 52.85, -39.59, 0.38357, 33, 27.17, -33.53, 0.48256, 3, 31, 78.93, -40.95, 0.03539, 32, 56.33, -26.44, 0.15184, 33, 26.98, -19.92, 0.81277, 3, 31, 101.85, -50.6, 2e-05, 32, 81.02, -29.39, 0.00031, 33, 51.56, -16.13, 0.99967, 1, 33, 60.62, -5.52, 1, 2, 32, 96.75, 20.3, 0.05786, 33, 53.36, 35.95, 0.94214, 2, 32, 72.25, 61.08, 0.38183, 33, 18.81, 68.66, 0.61817, 2, 32, 56.06, 76.61, 0.41426, 33, -0.96, 79.26, 0.58574, 3, 31, 102.96, 40.89, 0.00023, 32, 56.85, 58.86, 0.44191, 33, 4.57, 62.38, 0.55786, 3, 31, 60.86, 46.19, 0.16089, 32, 14.92, 52.33, 0.75975, 33, -34.07, 44.83, 0.07936, 3, 31, 34.08, 48.7, 0.61855, 32, -11.52, 47.36, 0.38081, 33, -58.2, 32.93, 0.00065, 2, 31, -5.1, 68.19, 0.99534, 32, -54.55, 55.28, 0.00466, 1, 31, -14.3, 58.18, 1, 3, 31, 28.91, -14.52, 0.57472, 32, 0.96, -14.83, 0.42316, 33, -29.47, -23.62, 0.00212, 1, 32, 24.64, 0.81, 1, 2, 32, 61.95, 10.4, 0.07437, 33, 22.5, 17.08, 0.92563], "hull": 19}}, "zhu_10": {"zhu_10": {"type": "mesh", "uvs": [0.53613, 0, 0.15902, 0, 0.13292, 0.15336, 0.16381, 0.29328, 0.21888, 0.46242, 0.32834, 0.56288, 0.34374, 0.61236, 0.31735, 0.69482, 0.17661, 0.81192, 0, 0.90428, 0, 0.962, 0.09305, 1, 0.35033, 1, 0.52186, 0.94056, 0.74396, 0.8614, 0.68212, 0.74629, 0.67598, 0.67835, 0.67911, 0.59717, 0.89109, 0.54071, 0.99999, 0.44297, 1, 0.27168, 1, 0.17211, 0.99999, 1e-05, 0.49939, 0.57509, 0.52235, 0.47004, 0.52924, 0.30471, 0.53383, 0.16694], "triangles": [13, 12, 8, 12, 11, 8, 8, 11, 9, 11, 10, 9, 8, 7, 13, 13, 7, 15, 13, 15, 14, 7, 16, 15, 16, 7, 23, 6, 23, 7, 16, 23, 17, 6, 5, 23, 18, 17, 24, 17, 23, 24, 23, 5, 24, 5, 4, 24, 24, 25, 18, 19, 18, 25, 20, 19, 25, 24, 4, 25, 4, 3, 25, 25, 26, 20, 26, 0, 22, 20, 26, 21, 21, 26, 22, 25, 3, 26, 3, 2, 26, 2, 1, 26, 26, 1, 0], "vertices": [1, 41, -9.02, -0.57, 1, 2, 41, 1.08, -75.68, 0.98184, 42, -72.74, -64.73, 0.01816, 2, 41, 43.42, -75.46, 0.82121, 42, -31.02, -71.39, 0.17879, 2, 41, 77.87, -64.81, 0.4053, 42, 4.9, -66.48, 0.5947, 2, 41, 112.13, -49.56, 0.02689, 42, 42.1, -56.99, 0.97311, 4, 42, 62.15, -36.16, 0.95765, 43, -26.22, -33.86, 0.0292, 44, 13.95, -87.56, 0.01255, 45, -24.72, -88.81, 0.0006, 4, 42, 72.39, -33.6, 0.75097, 43, -15.97, -32.29, 0.17663, 44, 14.79, -77.23, 0.06209, 45, -24.36, -78.45, 0.01031, 4, 42, 95.92, -39.13, 0.18315, 43, 8.29, -40.06, 0.4067, 44, 27.97, -55.42, 0.28604, 45, -12.22, -56.06, 0.12411, 4, 42, 130.04, -67.59, 6e-05, 43, 41.8, -71.67, 0.02665, 44, 66.49, -30.15, 0.03748, 45, 25.08, -29.01, 0.93581, 1, 45, 66.89, -12.6, 1, 1, 45, 72.17, 5.87, 1, 1, 45, 58.07, 24.96, 1, 2, 44, 54.33, 43.62, 0.35924, 45, 9.48, 44.11, 0.64076, 3, 43, 97.56, -6.73, 0.00381, 44, 16.25, 39.14, 0.95795, 45, -28.35, 37.85, 0.03824, 2, 43, 79.76, 39.96, 0.58211, 44, -33.3, 32.64, 0.41789, 4, 41, 190.36, 45.8, 0.00035, 42, 108.33, 32.92, 0.15096, 43, 40.65, 30.7, 0.699, 44, -32.11, -11.8, 0.14969, 4, 41, 150.87, 48.36, 0.00055, 42, 96.59, 33.26, 0.23467, 43, 16.07, 31.93, 0.76382, 44, -40.08, -31.16, 0.00096, 3, 41, 126.8, 45.21, 0.01817, 42, 73.25, 34.09, 0.78909, 43, -8.44, 35, 0.19275, 3, 41, 111.39, 86.41, 0.13015, 42, 63.9, 77.29, 0.86897, 43, -13.07, 78.9, 0.00088, 2, 41, 88.16, 105.67, 0.24127, 42, 43.56, 100.07, 0.75873, 2, 41, 50.52, 101.12, 0.52772, 42, 4.88, 101.69, 0.47228, 2, 41, 26.06, 98.03, 0.73632, 42, -19.97, 102.62, 0.26368, 2, 41, -19.14, 92.2, 0.90561, 42, -65.57, 104.21, 0.09439, 3, 42, 66.48, -1.85, 0.99959, 44, -17.07, -72.17, 0.00041, 45, -56.42, -74.89, 0, 2, 41, 105.42, 11.06, 0.00162, 42, 45.4, 3.91, 0.99838, 2, 41, 70.15, 8.21, 0.10327, 42, 9.27, 6.83, 0.89673, 2, 41, 35.46, 4.72, 0.99469, 42, -25.73, 9.02, 0.00531], "hull": 23}}, "zhu_11": {"zhu_11": {"type": "mesh", "uvs": [0.41577, 0.0051, 0, 0.00924, 0, 0.20768, 0.21897, 0.36147, 0.32957, 0.51286, 0.52656, 0.64556, 0.63025, 0.63812, 0.64251, 0.68423, 0.64155, 0.74452, 0.55076, 0.8369, 0.39178, 0.94853, 0.492, 1, 0.75121, 0.98449, 0.85316, 0.91876, 0.99832, 0.86667, 0.93855, 0.77304, 0.91364, 0.70694, 0.90945, 0.6474, 0.89463, 0.61118, 1, 0.53181, 0.99999, 0.34099, 0.99346, 0.19241, 0.92747, 0, 0.7512, 0.62866, 0.62809, 0.44501, 0.51222, 0.30468], "triangles": [12, 11, 9, 11, 10, 9, 12, 9, 13, 9, 8, 13, 13, 8, 15, 15, 8, 7, 13, 15, 14, 16, 7, 23, 16, 15, 7, 16, 23, 17, 17, 23, 18, 7, 6, 23, 6, 5, 24, 5, 4, 24, 6, 24, 23, 23, 24, 18, 18, 24, 19, 24, 20, 19, 4, 25, 24, 24, 25, 20, 4, 3, 25, 25, 3, 0, 25, 21, 20, 3, 2, 0, 25, 22, 21, 25, 0, 22, 2, 1, 0], "vertices": [1, 46, -14.33, -5.72, 1, 1, 46, -34.85, -102.96, 1, 2, 46, 30.05, -117.39, 0.99806, 47, -93.58, -98.29, 0.00194, 2, 46, 91.37, -77.52, 0.75812, 47, -24.37, -73.82, 0.24188, 2, 46, 131.43, -65.78, 0.21546, 47, 20.22, -71.75, 0.78454, 3, 46, 163.85, -34.44, 0.00081, 47, 62.95, -48.84, 0.96123, 48, -25.22, -51.79, 0.03795, 3, 47, 76.7, -27.68, 0.72964, 48, -10.24, -29.12, 0.27022, 50, -24.04, -94.41, 0.00014, 3, 47, 86.97, -29.47, 0.21867, 48, 2.35, -29.63, 0.77388, 50, -22.13, -82.07, 0.00746, 3, 48, 26.61, -36.72, 0.627, 49, 14.88, -50.9, 0.26243, 50, -12.61, -58.45, 0.11057, 3, 48, 53.78, -67.78, 0.02992, 49, 54.36, -39.51, 0.20159, 50, 20.79, -34.55, 0.76849, 1, 50, 71.91, -10.24, 1, 1, 50, 58.42, 20.26, 1, 3, 48, 129.19, -40.05, 0.08712, 49, 61.36, 40.53, 0.39507, 50, 0.69, 43.24, 0.51781, 3, 48, 112.7, -9.65, 0.56849, 49, 26.84, 38.55, 0.40845, 50, -31.19, 29.86, 0.02306, 1, 48, 107.36, 28.84, 1, 3, 47, 150.16, 19.29, 0.17252, 48, 72.65, 26.45, 0.80066, 49, -20.84, 20.04, 0.02682, 3, 47, 129.82, 23.74, 0.25733, 48, 50.22, 28.42, 0.70267, 49, -34.69, -1.81, 0.04, 4, 46, 222.99, 62.74, 0.0006, 47, 112.66, 31.83, 0.67088, 48, 15.41, 34.32, 0.31083, 49, -57.37, -24.72, 0.01769, 2, 46, 182.49, 55.7, 0.00107, 47, 98.08, 34.47, 0.99893, 2, 46, 168.45, 87.33, 0.02354, 47, 90.65, 68.5, 0.97646, 2, 46, 119.08, 104.33, 0.26755, 47, 45.39, 96.55, 0.73245, 2, 46, 76.52, 115.15, 0.61755, 47, 5.95, 116.99, 0.38245, 2, 46, 11.69, 114.13, 0.87207, 47, -57.43, 131.13, 0.12793, 1, 47, 81.44, 0.94, 1, 2, 46, 131.75, 9.63, 0.00081, 47, 35.78, 1.5, 0.99919, 1, 46, 88.01, -5.19, 1], "hull": 23}}, "zhu_12": {"zhu_12": {"type": "mesh", "uvs": [0.29975, 0, 0.11888, 0.17383, 0.0468, 0.40424, 0.10187, 0.54039, 0, 0.64962, 0, 0.73374, 0.03692, 0.87794, 0.21771, 1, 0.52156, 1, 0.78596, 0.90732, 0.92269, 0.73775, 1, 0.41463, 1, 0.22927, 0.8923, 0.12246, 0.66594, 0.02499, 0.4973, 0, 0.33832, 0.51016, 0.62569, 0.57445, 0.7859, 0.53369, 0.75463, 0.76022, 0.30958, 0.89226, 0.29426, 0.68491, 0.15821, 0.44713, 0.62012, 0.46344, 0.34491, 0.34794, 0.84146, 0.33006, 0.2136, 0.54393], "triangles": [10, 18, 11, 16, 23, 17, 17, 23, 18, 26, 22, 16, 3, 2, 22, 18, 25, 11, 18, 23, 25, 16, 24, 23, 16, 22, 24, 23, 24, 14, 23, 14, 25, 14, 24, 15, 24, 22, 1, 25, 12, 11, 22, 2, 1, 1, 0, 24, 24, 0, 15, 25, 13, 12, 25, 14, 13, 7, 20, 8, 9, 8, 19, 7, 6, 20, 8, 20, 19, 9, 19, 10, 6, 21, 20, 19, 21, 17, 19, 20, 21, 21, 5, 26, 3, 26, 4, 5, 21, 6, 19, 18, 10, 19, 17, 18, 26, 5, 4, 21, 16, 17, 21, 26, 16, 3, 22, 26], "vertices": [1, 5, 195.11, 33.44, 1, 2, 4, 280.9, 95.24, 0.02504, 5, 134.99, 91.55, 0.97496, 2, 4, 199.1, 117.73, 0.26815, 5, 53.84, 116.29, 0.73185, 2, 4, 150.77, 100.55, 0.67047, 5, 5.05, 100.45, 0.32953, 2, 4, 111.99, 132.33, 0.94478, 5, -32.83, 133.29, 0.05522, 2, 4, 82.13, 132.33, 0.98243, 5, -62.69, 134.11, 0.01757, 1, 4, 30.94, 120.81, 1, 1, 4, -12.39, 64.41, 1, 2, 4, -12.39, -30.39, 0.99992, 5, -161.66, -25.95, 8e-05, 2, 4, 20.51, -112.89, 0.92745, 5, -131.04, -109.31, 0.07255, 2, 4, 80.71, -155.55, 0.71312, 5, -72.05, -153.62, 0.28688, 2, 4, 195.42, -179.67, 0.19468, 5, 41.95, -180.89, 0.80532, 2, 4, 261.22, -179.67, 0.05897, 5, 107.73, -182.71, 0.94103, 2, 4, 299.14, -146.07, 0.01897, 5, 146.56, -150.16, 0.98103, 1, 5, 183.1, -80.52, 1, 1, 5, 193.42, -28.17, 1, 2, 4, 161.51, 26.78, 0.40261, 5, 13.75, 26.41, 0.59739, 2, 4, 138.68, -62.88, 0.54795, 5, -11.54, -62.59, 0.45205, 2, 4, 153.15, -112.87, 0.40175, 5, 1.55, -112.95, 0.59825, 2, 4, 72.73, -103.11, 0.8366, 5, -78.57, -100.98, 0.1634, 1, 4, 25.86, 35.74, 1, 2, 4, 99.47, 40.52, 0.9909, 5, -47.89, 41.86, 0.0091, 2, 4, 183.88, 82.97, 0.35261, 5, 37.66, 81.96, 0.64739, 2, 4, 178.09, -61.14, 0.1829, 5, 27.9, -61.94, 0.8171, 2, 4, 219.09, 24.72, 0.02539, 5, 71.25, 22.76, 0.97461, 2, 4, 225.44, -130.2, 0.09958, 5, 73.33, -132.27, 0.90042, 2, 4, 149.51, 65.69, 0.65871, 5, 2.83, 65.63, 0.34129], "hull": 16}}, "zhu_13": {"zhu_13": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [124.71, 78.57, 163.92, -19.92, 14.33, -79.46, -24.87, 19.02], "hull": 4}}, "zhu_14": {"zhu_14": {"type": "mesh", "uvs": [0.32102, 0, 0.25083, 0.34235, 0, 0.62422, 0, 0.70173, 0.16309, 0.8638, 0.21996, 0.87894, 0.26339, 0.84816, 0.30299, 0.91485, 0.26339, 0.95383, 0.35153, 1, 0.41285, 1, 0.59424, 0.92921, 0.73476, 0.77122, 0.9659, 0.48276, 1, 0.17063, 0.93387, 0.0005], "triangles": [1, 12, 6, 4, 3, 6, 5, 4, 6, 11, 7, 6, 12, 11, 6, 10, 9, 7, 8, 7, 9, 11, 10, 7, 2, 6, 3, 14, 0, 15, 0, 13, 1, 14, 13, 0, 12, 1, 13, 1, 6, 2], "vertices": [1, 8, 10.83, -77.66, 1, 2, 8, 74.9, -58.17, 0.87709, 9, -31.54, -59.22, 0.12291, 2, 8, 140.88, -68.96, 0.90408, 9, 34.77, -67.88, 0.09592, 2, 8, 154.3, -62.34, 0.95297, 9, 47.96, -60.83, 0.04703, 1, 9, 63.64, -23.79, 1, 1, 9, 62.06, -14.64, 1, 1, 9, 53.65, -11.5, 1, 1, 9, 62.11, -0.02, 1, 1, 9, 71.64, -1.89, 1, 1, 9, 73.06, 14.36, 1, 1, 9, 68.58, 22.74, 1, 2, 8, 152.91, 39.69, 0.01254, 9, 43.28, 41.1, 0.98746, 2, 8, 115.92, 45.72, 0.3871, 9, 6.12, 45.94, 0.6129, 2, 8, 50.15, 53.21, 0.99879, 9, -59.86, 51.31, 0.00121, 1, 8, -6.21, 31.29, 1, 1, 8, -31.12, 7.57, 1], "hull": 16}}, "zhu_15": {"zhu_15": {"type": "mesh", "uvs": [0.34369, 1e-05, 0.25509, 0.02673, 0.24467, 0.76846, 0, 0.8601, 0.01144, 0.99999, 0.97306, 0.9873, 1, 0.83901, 0.41281, 0.7708, 0.4111, 0.03055], "triangles": [1, 0, 8, 2, 1, 8, 7, 2, 8, 5, 7, 6, 3, 5, 4, 7, 3, 2, 5, 3, 7], "vertices": [-600.72, 51.39, -577.09, 26.56, 125.48, -27.53, 207.68, -97.33, 340.43, -104.05, 346.64, 146.18, 206.65, 163.44, 130.89, 15.91, -570.5, 66.75], "hull": 9}}, "zhu_16": {"zhu_16": {"type": "mesh", "uvs": [0.19906, 0.15807, 0.14007, 0.21938, 0.05889, 0.28798, 0.07898, 0.37053, 0.02751, 0.39155, 0, 0.45858, 0.03656, 0.5357, 1e-05, 0.58143, 0.00209, 0.67984, 0.06618, 0.73023, 0.06808, 0.78542, 0.10302, 0.8635, 0.18784, 0.87416, 0.24671, 0.98701, 0.36039, 1, 0.45454, 0.97168, 0.5004, 0.88229, 0.55691, 0.89291, 0.64625, 0.84449, 0.6644, 0.78996, 0.75284, 0.78151, 0.81779, 0.72484, 0.83081, 0.6343, 0.90945, 0.57646, 0.9165, 0.48595, 0.99999, 0.44098, 1, 0.34711, 0.9603, 0.28925, 0.97605, 0.2126, 0.91967, 0.13183, 0.86672, 0.11664, 0.84992, 0.03326, 0.70445, 0, 0.56923, 0, 0.56789, 0.09491, 0.65763, 0.16824, 0.75215, 0.19703, 0.75745, 0.26453, 0.79576, 0.31863, 0.78477, 0.3513, 0.78071, 0.38906, 0.69489, 0.444, 0.68775, 0.53546, 0.5853, 0.58569, 0.57841, 0.67296, 0.50345, 0.68446, 0.4558, 0.73587, 0.38392, 0.69354, 0.27761, 0.72145, 0.24457, 0.67951, 0.21424, 0.66082, 0.21623, 0.60902, 0.17454, 0.54558, 0.20463, 0.50467, 0.20805, 0.42656, 0.25049, 0.33587, 0.30803, 0.2236], "triangles": [49, 10, 50, 11, 10, 12, 49, 12, 10, 14, 13, 12, 49, 48, 12, 14, 12, 48, 16, 14, 48, 14, 16, 15, 19, 44, 20, 46, 17, 16, 19, 18, 46, 18, 17, 46, 48, 46, 16, 46, 48, 47, 19, 45, 44, 19, 46, 45, 42, 22, 44, 22, 21, 44, 44, 43, 42, 44, 21, 20, 10, 9, 50, 9, 52, 50, 32, 34, 33, 35, 34, 32, 36, 32, 31, 36, 31, 30, 35, 32, 36, 37, 36, 30, 29, 28, 37, 37, 28, 38, 29, 37, 30, 27, 38, 28, 56, 1, 0, 55, 1, 56, 3, 2, 1, 3, 1, 55, 54, 3, 55, 27, 26, 39, 27, 39, 38, 25, 24, 26, 26, 24, 39, 40, 39, 24, 53, 3, 54, 3, 5, 4, 6, 3, 53, 6, 5, 3, 52, 6, 53, 41, 40, 42, 23, 22, 24, 40, 24, 42, 22, 42, 24, 50, 52, 51, 9, 8, 7, 52, 7, 6, 52, 9, 7], "vertices": [1, 5, 175.83, 49.81, 1, 1, 5, 162.59, 63.22, 1, 2, 5, 147.86, 81.58, 0.8, 68, 119.94, 59.67, 0.2, 2, 5, 129.42, 77.64, 0.7, 68, 101.5, 55.74, 0.3, 2, 5, 125.06, 89.14, 0.7, 68, 97.15, 67.24, 0.3, 2, 5, 110.36, 95.63, 0.7, 68, 82.44, 73.73, 0.3, 2, 5, 93.02, 88.03, 0.7, 68, 65.1, 66.12, 0.3, 2, 5, 83.09, 96.38, 0.7, 68, 55.18, 74.48, 0.3, 2, 5, 61.24, 96.52, 0.6, 68, 33.32, 74.62, 0.4, 2, 5, 49.67, 82.67, 0.4, 68, 21.75, 60.77, 0.6, 2, 5, 37.41, 82.59, 0.4, 68, 9.49, 60.69, 0.6, 2, 5, 19.87, 75.35, 0.4, 68, -8.05, 53.45, 0.6, 2, 5, 16.99, 56.68, 0.3, 68, -10.93, 34.77, 0.7, 2, 5, -8.41, 44.36, 0.3, 68, -36.33, 22.46, 0.7, 2, 5, -11.99, 19.33, 0.3, 68, -39.91, -2.57, 0.7, 2, 5, -6.28, -1.64, 0.3, 68, -34.2, -23.55, 0.7, 2, 5, 13.28, -12.32, 0.3, 68, -14.64, -34.23, 0.7, 2, 5, 10.58, -24.74, 0.4, 68, -17.34, -46.64, 0.6, 2, 5, 20.78, -44.77, 0.4, 68, -7.14, -66.68, 0.6, 2, 5, 32.77, -49.12, 0.4, 68, 4.85, -71.02, 0.6, 2, 5, 34.11, -68.71, 0.5, 68, 6.19, -90.61, 0.5, 2, 5, 46.29, -83.4, 0.5, 68, 18.37, -105.31, 0.5, 2, 5, 66.3, -86.83, 0.5, 68, 38.38, -108.74, 0.5, 2, 5, 78.66, -104.56, 0.7, 68, 50.74, -126.46, 0.3, 2, 5, 98.7, -106.67, 0.6, 68, 70.78, -128.57, 0.4, 2, 5, 108.17, -125.39, 0.7, 68, 80.25, -147.29, 0.3, 2, 5, 129, -125.97, 0.7, 68, 101.08, -147.87, 0.3, 2, 5, 142.08, -117.55, 0.7, 68, 114.16, -139.46, 0.3, 2, 5, 159, -121.5, 0.8, 68, 131.08, -143.4, 0.2, 2, 5, 177.26, -109.54, 0.8, 68, 149.35, -131.44, 0.2, 2, 5, 180.96, -97.94, 0.8, 68, 153.04, -119.84, 0.2, 2, 5, 199.56, -94.73, 0.8, 68, 171.64, -116.64, 0.2, 1, 5, 207.83, -62.8, 1, 1, 5, 208.65, -32.93, 1, 1, 5, 187.6, -32.05, 1, 2, 5, 170.78, -51.43, 0.8, 68, 142.86, -73.33, 0.2, 2, 5, 163.82, -72.13, 0.8, 68, 135.9, -94.04, 0.2, 2, 5, 148.8, -72.89, 0.8, 68, 120.89, -94.79, 0.2, 2, 5, 136.57, -81.02, 0.7, 68, 108.65, -102.93, 0.3, 2, 5, 129.38, -78.39, 0.7, 68, 101.46, -100.3, 0.3, 2, 5, 121.03, -77.27, 0.6, 68, 93.11, -99.17, 0.4, 2, 5, 109.36, -57.97, 0.6, 68, 81.44, -79.88, 0.4, 2, 5, 89.1, -55.83, 0.5, 68, 61.19, -77.74, 0.5, 2, 5, 78.58, -32.9, 0.5, 68, 50.67, -54.8, 0.5, 2, 5, 59.26, -30.84, 0.4, 68, 31.34, -52.74, 0.6, 2, 5, 57.16, -14.21, 0.4, 68, 29.24, -36.11, 0.6, 2, 5, 46.04, -3.37, 0.3, 68, 18.13, -25.27, 0.7, 2, 5, 55.88, 12.25, 0.3, 68, 27.96, -9.65, 0.7, 2, 5, 50.33, 35.91, 0.3, 68, 22.41, 14.01, 0.7, 2, 5, 59.84, 42.95, 0.4, 68, 31.92, 21.05, 0.6, 2, 5, 64.17, 49.54, 0.4, 68, 36.25, 27.64, 0.6, 2, 5, 75.66, 48.78, 0.6, 68, 47.74, 26.88, 0.4, 2, 5, 89.99, 57.6, 0.7, 68, 62.07, 35.7, 0.3, 2, 5, 98.88, 50.71, 0.7, 68, 70.96, 28.8, 0.3, 2, 5, 116.19, 49.47, 0.7, 68, 88.28, 27.57, 0.3, 2, 5, 136.06, 39.54, 0.8, 68, 108.15, 17.64, 0.2, 1, 5, 160.63, 26.14, 1], "hull": 57}}, "zhu_17": {"zhu_17": {"type": "mesh", "uvs": [0.17167, 1, 0.08347, 0.91159, 0, 0.75766, 0, 0.45601, 0.09461, 0.28732, 0.16481, 0.21284, 0.18551, 0.03532, 0.23591, 0.08994, 0.23771, 0.18553, 0.21251, 0.26746, 0.30251, 0.20291, 0.39251, 0.16567, 0.43571, 0.04774, 0.50528, 0, 0.55928, 0, 0.59168, 0.14241, 0.54758, 0.27772, 0.61868, 0.23303, 0.74378, 0.28889, 0.85988, 0.44531, 0.79238, 0.52848, 0.70688, 0.50241, 0.83468, 0.5682, 0.90668, 0.70724, 1, 0.80034, 0.99938, 1, 0.88328, 1, 0.78878, 0.99728, 0.68798, 0.92901, 0.61418, 0.91908, 0.55298, 1, 0.21644, 0.86609, 0.25467, 0.67023, 0.34206, 0.53086, 0.50863, 0.50073], "triangles": [23, 28, 22, 27, 28, 23, 26, 27, 23, 24, 26, 23, 25, 26, 24, 28, 29, 21, 22, 28, 21, 30, 33, 29, 21, 17, 18, 20, 21, 18, 34, 16, 21, 19, 20, 18, 29, 34, 21, 29, 33, 34, 5, 6, 7, 5, 7, 8, 9, 5, 8, 13, 15, 12, 15, 13, 14, 15, 11, 12, 16, 11, 15, 34, 11, 16, 16, 17, 21, 33, 10, 11, 33, 11, 34, 9, 10, 33, 4, 5, 9, 32, 9, 33, 32, 2, 3, 4, 9, 32, 32, 3, 4, 31, 2, 32, 32, 33, 30, 1, 2, 31, 0, 1, 31, 31, 32, 30, 0, 31, 30], "vertices": [4, 62, 2.6, 31.31, 0.87653, 63, -37.8, 26.9, 0.1232, 65, 19.78, -104.45, 4e-05, 66, -20.05, -106.07, 0.00024, 4, 62, 17.69, 47.05, 0.65602, 63, -24.74, 44.36, 0.34381, 65, -1.83, -107.37, 2e-05, 66, -41.41, -110.46, 0.00014, 5, 62, 42.07, 60.56, 0.3623, 63, -2.19, 60.75, 0.63675, 64, -71.31, -7.99, 0.00086, 65, -29.32, -102.78, 1e-05, 66, -69.15, -107.75, 7e-05, 5, 62, 85.41, 54.62, 0.06295, 63, 41.54, 60.14, 0.79123, 64, -51.75, 31.13, 0.1458, 65, -59.54, -71.16, 0, 66, -101.46, -78.27, 2e-05, 5, 62, 107.07, 32.56, 0.0034, 63, 65.74, 40.88, 0.51155, 64, -23.89, 44.55, 0.48504, 65, -62.76, -40.4, 0, 66, -106.78, -47.81, 0, 4, 63, 76.34, 26.69, 0.26618, 64, -6.5, 47.93, 0.73382, 65, -60.08, -22.89, 0, 66, -105.3, -30.16, 0, 2, 63, 102.02, 22.19, 0.15929, 64, 8.72, 69.1, 0.84071, 2, 63, 93.96, 12.22, 0.16636, 64, 14.19, 57.51, 0.83364, 4, 63, 80.1, 12.06, 0.19876, 64, 8.31, 44.95, 0.80124, 65, -52.27, -9.96, 0, 66, -98.4, -16.72, 0, 4, 63, 68.29, 17.26, 0.23706, 64, -1.51, 36.58, 0.76294, 65, -47.71, -22.03, 0, 66, -93.02, -28.45, 0, 2, 63, 77.4, -0.87, 0.0169, 64, 18.78, 36.9, 0.9831, 1, 64, 37.29, 33.68, 1, 2, 64, 52.67, 45.11, 1, 65, -37.45, 31.85, 0, 2, 64, 68.21, 45.08, 1, 65, -32.18, 46.47, 0, 2, 64, 77.87, 40.25, 1, 65, -24.37, 53.93, 0, 1, 64, 74.43, 18.88, 1, 2, 64, 57.77, 5.28, 0.81659, 65, 1.76, 23.2, 0.18341, 2, 64, 73.38, 4.72, 0.47771, 65, 7.56, 37.71, 0.52229, 3, 64, 92.14, -13.72, 0.25094, 65, 31.25, 49.15, 0.74825, 66, -19.11, 47.95, 0.0008, 3, 64, 102.77, -44.39, 0.15791, 65, 63.7, 48.8, 0.83228, 66, 13.3, 49.82, 0.00981, 4, 64, 85.3, -49.14, 0.1491, 65, 62.28, 30.75, 0.8164, 66, 13.11, 31.72, 0.03106, 67, -9.19, 39.55, 0.00344, 4, 64, 71.69, -38.11, 0.09348, 65, 47.31, 21.67, 0.64929, 66, -1.21, 21.64, 0.2021, 67, -26.5, 36.88, 0.05513, 4, 64, 90.29, -58.07, 0.0006, 65, 72.37, 32.43, 0.0196, 66, 23.06, 34.09, 0.26451, 67, 0.78, 37.25, 0.71528, 2, 66, 47.66, 31.14, 0.00308, 67, 21.51, 23.68, 0.99692, 1, 67, 43.79, 17.86, 1, 1, 67, 54.17, -9.16, 1, 2, 66, 75.87, -0.93, 0.04064, 67, 32.53, -17.58, 0.95936, 3, 62, -13.76, -91.02, 0.00136, 66, 62.84, -14.63, 0.33222, 67, 14.77, -24.06, 0.66642, 4, 62, -1.22, -72.39, 0.05959, 65, 87.31, -25.66, 0.00273, 66, 41.94, -22.85, 0.88827, 67, -7.6, -22.14, 0.0494, 4, 62, 2.21, -57.96, 0.23282, 63, -27.3, -61.75, 0.0023, 65, 75.65, -34.82, 0.03835, 66, 30.93, -32.78, 0.72653, 3, 62, -7.75, -44.24, 0.45753, 65, 74.91, -51.76, 0.0507, 66, 31.35, -49.73, 0.49177, 4, 62, 20.62, 19.81, 0.84708, 63, -18.51, 17.68, 0.15281, 65, 12.84, -84.23, 2e-05, 66, -28.36, -86.37, 9e-05, 4, 62, 47.72, 8.38, 0.08515, 63, 9.78, 9.64, 0.91485, 65, -1.26, -58.42, 0, 66, -44.19, -61.58, 0, 4, 62, 65.37, -11.68, 0.00021, 63, 29.74, -8.12, 0.56045, 64, 4.59, -9.17, 0.3818, 65, -2.59, -31.73, 0.05754, 3, 62, 65.18, -45.28, 0.005, 63, 33.65, -41.49, 0.02496, 65, 18.47, -5.55, 0.97005], "hull": 31}}, "zxc": {"zxc": {"width": 192, "height": 192}}}}], "animations": {"1": {"slots": {"3": {"attachment": [{"time": 0.1, "name": "dg_xl_30.png Animation_00000"}, {"time": 0.1333, "name": "dg_xl_30.png Animation_00001"}, {"time": 0.1667, "name": "dg_xl_30.png Animation_00002"}, {"time": 0.2, "name": "dg_xl_30.png Animation_00003"}, {"time": 0.2333, "name": "dg_xl_30.png Animation_00004"}, {"time": 0.2667, "name": "dg_xl_30.png Animation_00005"}, {"time": 0.3, "name": "dg_xl_30.png Animation_00006"}, {"time": 0.3333, "name": "dg_xl_30.png Animation_00007"}, {"time": 0.3667, "name": null}]}, "bbb": {"color": [{"time": 0.1, "color": "ff2715ff"}, {"time": 0.2333, "color": "ff1f10ff"}, {"time": 0.3, "color": "ff121200"}], "attachment": [{"time": 0.1, "name": "bbb2"}]}, "dg_04": {"color": [{"time": 0.1, "color": "ffffff32"}]}, "dg_4": {"color": [{"time": 0.1, "color": "ffffffc5"}]}}, "bones": {"zhu_12": {"translate": [{"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -4.71, "y": -0.61, "curve": 0.25, "c3": 0.75}, {"time": 0.3}]}, "zhu_14": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -80.93, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -10.84, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -16.48, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}], "translate": [{"x": -1.28, "y": -0.5, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": 0.57, "y": 17.39, "curve": 0.25, "c3": 0.75}, {"time": 0.2}]}, "all3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -9.26, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 7.89, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 6.62, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": -13.33, "y": 7.31, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -29.07, "y": -12.5, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": 0.56, "y": 3.44, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}]}, "zhu_10": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 26.68, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -19.7, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 11.47, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}]}, "zhu_2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -110.13, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -100.91, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -71.01, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": -26.66, "y": 12.43, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -20.93, "y": 10.69, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": -14.53, "y": 7.3, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": 1.068, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": 1.144, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": 1.058, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}]}, "zhu_11": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 13.07, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -30, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 12.39, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}]}, "zhu_19": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 15.45, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -25.69, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 8.5, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}]}, "zhu_20": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 9.57, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -20.24, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 10.61, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}]}, "all4": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -0.75, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 5.64, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -0.53, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}]}, "all6": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 1.38, "curve": 0.25, "c3": 0.75}, {"time": 0.2}]}, "zhu_21": {"rotate": [{"angle": -0.47, "curve": 0.341, "c2": 0.36, "c3": 0.675, "c4": 0.7}, {"time": 0.1, "angle": -4.31, "curve": 0.355, "c2": 0.45, "c3": 0.691, "c4": 0.79}, {"time": 0.3, "angle": -3.32, "curve": 0.355, "c2": 0.65, "c3": 0.689}, {"time": 0.4333, "angle": -0.47}], "translate": [{"x": -2.77, "y": 1.68, "curve": 0.341, "c2": 0.36, "c3": 0.675, "c4": 0.7}, {"time": 0.1, "x": -25.73, "y": 21.29, "curve": 0.343, "c2": 0.38, "c3": 0.677, "c4": 0.72}, {"time": 0.2, "x": -47.5, "y": -16.65, "curve": 0.346, "c2": 0.41, "c3": 0.68, "c4": 0.75}, {"time": 0.3, "x": -24.01, "y": -0.23, "curve": 0.355, "c2": 0.65, "c3": 0.689}, {"time": 0.4333, "x": -2.77, "y": 1.68}]}, "zhu_22": {"rotate": [{"angle": -1.64, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.1, "angle": -15.97, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.2, "angle": -3.64, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -1.64}]}, "zhu_23": {"rotate": [{"angle": -2, "curve": 0.318, "c2": 0.28, "c3": 0.656, "c4": 0.63}, {"time": 0.1, "angle": -13.05, "curve": 0.332, "c2": 0.33, "c3": 0.67, "c4": 0.68}, {"time": 0.2, "angle": -0.97, "curve": 0.381, "c2": 0.54, "c3": 0.743}, {"time": 0.4333, "angle": -2}]}, "zhu_24": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -6.88, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 20.45, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -1.56, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": -18.54, "y": -25.97, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -30.82, "y": 14.15, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": -7.73, "y": 25.31, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}]}, "zhu_25": {"rotate": [{"angle": 1.85, "curve": 0.341, "c2": 0.36, "c3": 0.675, "c4": 0.7}, {"time": 0.1, "angle": 11.94, "curve": 0.343, "c2": 0.38, "c3": 0.677, "c4": 0.72}, {"time": 0.2, "angle": 2.7, "curve": 0.346, "c2": 0.41, "c3": 0.68, "c4": 0.75}, {"time": 0.3, "angle": 12.76, "curve": 0.355, "c2": 0.65, "c3": 0.689}, {"time": 0.4333, "angle": 1.85}]}, "zhu_26": {"rotate": [{"angle": 5.54, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.1, "angle": 39.77, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.2, "angle": -1.02, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 0.3, "angle": 25.07, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.4333, "angle": 5.54}]}, "zhu_27": {"rotate": [{"angle": -1.15, "curve": 0.341, "c2": 0.36, "c3": 0.675, "c4": 0.7}, {"time": 0.1, "angle": 4.55, "curve": 0.343, "c2": 0.38, "c3": 0.677, "c4": 0.72}, {"time": 0.2, "angle": -12.46, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.4333, "angle": -1.15}], "translate": [{"curve": 0.341, "c2": 0.36, "c3": 0.675, "c4": 0.7}, {"time": 0.1, "x": -3.16, "y": -20.75, "curve": 0.343, "c2": 0.38, "c3": 0.677, "c4": 0.72}, {"time": 0.2, "x": 6.35, "y": 10.92, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.4333}]}, "zhu_28": {"rotate": [{"angle": 4.56, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.1, "angle": 15.3, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.2, "angle": -15.13, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 4.56}]}, "zhu_29": {"rotate": [{"angle": 8.49, "curve": 0.318, "c2": 0.28, "c3": 0.656, "c4": 0.63}, {"time": 0.1, "angle": 18.11, "curve": 0.332, "c2": 0.33, "c3": 0.67, "c4": 0.68}, {"time": 0.2, "angle": -10.35, "curve": 0.381, "c2": 0.54, "c3": 0.743}, {"time": 0.4333, "angle": 8.49}]}, "zhu_30": {"rotate": [{"angle": -2.29, "curve": 0.341, "c2": 0.36, "c3": 0.675, "c4": 0.7}, {"time": 0.1, "angle": -0.57, "curve": 0.343, "c2": 0.38, "c3": 0.677, "c4": 0.72}, {"time": 0.2, "angle": -6.65, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.4333, "angle": -2.29}], "translate": [{"curve": 0.341, "c2": 0.36, "c3": 0.675, "c4": 0.7}, {"time": 0.1, "x": 5.56, "y": -19.34, "curve": 0.343, "c2": 0.38, "c3": 0.677, "c4": 0.72}, {"time": 0.2, "x": 6.35, "y": 10.92, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.4333}]}, "zhu_31": {"rotate": [{"angle": 2.89, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.1, "angle": 8.81, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.2, "angle": -29.75, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 2.89}]}, "zhu_32": {"rotate": [{"angle": 4.08, "curve": 0.318, "c2": 0.28, "c3": 0.656, "c4": 0.63}, {"time": 0.1, "angle": 13.57, "curve": 0.332, "c2": 0.33, "c3": 0.67, "c4": 0.68}, {"time": 0.2, "angle": -18.35, "curve": 0.381, "c2": 0.54, "c3": 0.743}, {"time": 0.4333, "angle": 4.08}]}, "zhu_33": {"rotate": [{"angle": 21.8, "curve": 0.279, "c3": 0.622, "c4": 0.39}, {"time": 0.1, "angle": 43.61, "curve": 0.329, "c2": 0.32, "c3": 0.758}, {"time": 0.4333, "angle": 21.8}]}, "zhu_34": {"rotate": [{"angle": 13.03}]}, "zhu_35": {"rotate": [{"angle": -3.68}]}, "zhu_36": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -8.51, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -6.34, "curve": "stepped"}, {"time": 0.3, "angle": -6.34, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}]}, "zhu_37": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -18.28, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -19.84, "curve": "stepped"}, {"time": 0.3, "angle": -19.84, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}]}, "zhu_38": {"rotate": [{"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 17.43, "curve": "stepped"}, {"time": 0.3, "angle": 17.43, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}]}, "zhu_39": {"rotate": [{"angle": -0.29}]}, "zhu_40": {"rotate": [{"angle": 0.01}]}, "zhu_41": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -16.06, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 12, "curve": 0.25, "c3": 0.75}, {"time": 0.3}], "translate": [{"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": 4.11, "y": -10.31, "curve": 0.25, "c3": 0.75}, {"time": 0.3}]}, "zhu_42": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -1.34, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -26.26, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -0.54, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}]}, "zhu_43": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 4.19, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -11.69, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -30.76, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}]}, "t2": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": -5.98, "y": -2.99, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -0.16, "y": 53.09, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": -10.93, "y": 53.09, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}]}, "all2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -2.05, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 9.19, "curve": "stepped"}, {"time": 0.3, "angle": 9.19, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": 18.61, "y": -13.58, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -73.92, "y": -17.51, "curve": "stepped"}, {"time": 0.3, "x": -73.92, "y": -17.51, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}]}, "zhu_13": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -11, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 9.27, "curve": "stepped"}, {"time": 0.3, "angle": 9.27, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": -2.64, "y": -0.31, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -22.18, "y": 36.9, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": -11.18, "y": 3.16, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}]}, "zhu_0": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -19.59, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 3, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -7.23, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": 9.63, "y": 3.52, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -9.09, "y": 8.84, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": -0.28, "y": 1.44, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}]}, "zhu_15": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -58.12, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -48.07, "curve": "stepped"}, {"time": 0.3, "angle": -48.07, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": -35.67, "y": -30.09, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -37.93, "y": -0.45, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": -21.31, "y": -8.8, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}]}, "zhu_16": {"rotate": [{"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -2.25, "curve": 0.25, "c3": 0.75}, {"time": 0.3}]}, "zhu_18": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -15.49, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 7.64, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -3.26, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": 214.85, "y": -113.14, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": 85.39, "y": -10.72, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": 40.69, "y": -8.2, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}]}, "zhu_1": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -34.99, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -3.83, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -21.01, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": 21.29, "y": -17.21, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": 26.51, "y": -5.75, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": -0.99, "y": -15.48, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": 1.07, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": 1.076, "curve": 0.25, "c3": 0.75}, {"time": 0.3}]}, "zhu_3": {"rotate": [{"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 13.63, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}]}, "zhu_4": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 13.06, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -7.03, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 9.63, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}]}, "zhu_5": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -13.41, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 21.5, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 15.7, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}]}, "zhu_6": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 11.82, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 8.1, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 15.49, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}]}, "zhu_7": {"rotate": [{"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -6.81, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 21.82, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}]}, "t1": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": -36.34, "y": 22.53, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -98.38, "y": -18.74, "curve": "stepped"}, {"time": 0.3, "x": -98.38, "y": -18.74, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}]}, "xy": {"rotate": [{"time": 0.1, "angle": -93.41}, {"time": 0.2333, "angle": -88.6}, {"time": 0.3, "angle": -72.58}], "translate": [{"time": 0.1, "x": 6.5, "y": -593.99}, {"time": 0.1667, "x": -51.97, "y": -652.25}, {"time": 0.2333, "x": 0.39, "y": -581.82}, {"time": 0.3, "x": 93.46, "y": -510.61}], "scale": [{"time": 0.1, "x": -8.541, "y": 7.455}, {"time": 0.1667, "x": -11.056, "y": 10.272}, {"time": 0.2333, "x": -8.84, "y": 8.84}, {"time": 0.3, "x": -8.062, "y": 6.906}]}, "1": {"rotate": [{"time": 0.1, "angle": -14.99}, {"time": 0.1667, "angle": 137.5}, {"time": 0.2, "angle": -162.87}], "translate": [{"time": 0.1, "x": 223.08, "y": 572.32}, {"time": 0.1333, "x": -36.41, "y": 602.38}, {"time": 0.1667, "x": -277.4, "y": 472.2}, {"time": 0.2, "x": -572.03, "y": 308.61}], "scale": [{"time": 0.1, "x": 9.339, "y": 9.339}, {"time": 0.1333, "x": 9.953, "y": 9.936}, {"time": 0.1667, "x": 11.417, "y": 10.554}, {"time": 0.2, "x": 7.416, "y": 7.416}]}, "3": {"rotate": [{"time": 0.1, "angle": -113.88}, {"time": 0.1667, "angle": -47.3}, {"time": 0.2333, "angle": -2.92}], "translate": [{"time": 0.1, "x": 270.77, "y": 574.49}, {"time": 0.1333, "x": 4.51, "y": 508.45}, {"time": 0.1667, "x": -181.1, "y": 443.33}, {"time": 0.2333, "x": -393.83, "y": 288.07}], "scale": [{"time": 0.1, "x": -4.112, "y": 4.112}, {"time": 0.1333, "x": -5.331, "y": 5.331}, {"time": 0.1667, "x": -6.014, "y": 6.014}]}, "6": {"rotate": [{"time": 0.1, "angle": -14.99}, {"time": 0.1667, "angle": 137.5}, {"time": 0.2, "angle": -162.87}], "translate": [{"time": 0.1, "x": 223.08, "y": 572.32}, {"time": 0.1333, "x": -36.41, "y": 602.38}, {"time": 0.1667, "x": -277.4, "y": 472.2}, {"time": 0.2, "x": -572.03, "y": 308.61}], "scale": [{"time": 0.1, "x": 9.339, "y": 9.339}, {"time": 0.1333, "x": 9.953, "y": 9.936}, {"time": 0.1667, "x": 11.417, "y": 10.554}, {"time": 0.2, "x": 7.416, "y": 7.416}]}, "zhu_47": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -2.94, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 2.68, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -1.48, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}]}, "zhu_48": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -3.46, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 8.92, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -6.19, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}]}, "zhu_49": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -6.37, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 10.01, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -7.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}]}, "zhu_50": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -11.15, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 15.24, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -8.92, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}]}, "zhu_51": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": 38.63, "y": 6.28, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -15.42, "y": 45.01, "curve": 0.25, "c3": 0.75}, {"time": 0.3}]}}, "deform": {"default": {"zhu_1": {"zhu_1": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "vertices": [1.2002, -11.9691, 9.49704, -7.38289, -4.91135, -8.83644, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -7.4075, -32.89113, -6.39508, -33.10294], "curve": 0.25, "c3": 0.75}, {"time": 0.2, "vertices": [-19.90098, -6.29993, -9.1575, -18.758, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -9.72343, -8.5433, -9.45732, -8.83667], "curve": 0.25, "c3": 0.75}, {"time": 0.3}]}, "zhu_2": {"zhu_2": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "offset": 8, "vertices": [-0.58553, 4.33026, -57.8201, 7.08826, -41.62415, 11.88208, -39.07993, 11.68585, -27.42225, 17.70584, -16.87288, 16.83002, -2.66786, 21.20398, -3.48923, 2.97528], "curve": 0.25, "c3": 0.75}, {"time": 0.2, "offset": 10, "vertices": [-34.12541, 13.77023, -24.60329, 11.41956, -24.70787, 10.72061, -14.00977, 17.5853, -7.65338, 30.16467, -2.23135, 34.01395, 2.59341, 16.58185], "curve": 0.25, "c3": 0.75}, {"time": 0.3, "offset": 10, "vertices": [-35.64026, 21.77251, -25.03525, 23.15811, -16.8031, 27.48831, -8.99747, 27.59602, 3.00812, 23.05611, 10.39572, 21.51799, 6.84982, 9.33907], "curve": 0.25, "c3": 0.75}, {"time": 0.4333}]}, "zhu_4": {"zhu_4": [{}, {"time": 0.1, "vertices": [-5.4686, -3.39799, -6.36273, -0.98283, -7.34885, 6.52508, -18.43834, -0.67761, -17.22867, 6.60318, -4.73184, -1.06791, 2.83665, -3.93445, 1.06772, -4.73151, -5.14803, -8.89462, -8.22223, -6.16527, -3.35475, -6.40565, -4.4344, -8.77869, -7.5202, -6.33833, 6.69838, -7.20123, 1.00739, -15.22, -5.03842, -14.39713, 7.89424, -17.03122, 0.58759, -18.76279, -2.27547, -4.03253, 2.99429, -3.53149, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -6.6897, -3.50684, 0.97163, -7.49033, -2.75406, -7.03297, -6.42805, -3.75262, 1.29286, -7.32986, -6.99457, -14.24858, -6.26643, -8.34383, 4.8221, -15.44176, 6.12196, -7.01269, 2.88373, -8.85125]}, {"time": 0.2, "offset": 10, "vertices": [3.3855, 2.80824, 11.51886, 6.7144, 13.18533, 1.97964, 8.34106, 8.58396, 10.92401, 4.89177, 4.9709, 8.798, 5.67628, 12.53926, 9.9104, 9.5521, -8.65799, 10.69997, 3.39693, 19.26788, 10.28052, 16.6468, 2.32443, 12.3847, 6.73898, 10.64775, -5.55635, 12.48453, -13.65015, 0.63756, 2.8631, 8.23984, -6.07847, 6.25653, -12.24588, 6.34547, -7.67877, 11.45697, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -6.4492, 4.21972, -6.66524, -3.86946, -7.70453, -0.18301, -1.05232, 10.25986, -9.64081, 3.66411, -2.5088, 4.16982, -0.25922, 10.64388, 1.78584, 16.52959, 6.79559, 12.94839, 11.10159, 9.51824]}, {"time": 0.3, "offset": 10, "vertices": [5.53693, 3.45481, 15.07669, 5.56654, 16.0665, -0.40247, 7.00169, 4.43316, 8.14474, 1.53032, 3.80388, 7.21046, 2.94061, 10.14845, 6.48453, 8.34214, -7.37618, 7.30043, 2.44016, 16.3031, 8.29538, 14.24583, 1.36768, 9.41987, 4.75378, 8.24675, -5.55635, 12.48453, -13.65015, 0.63756, 2.8631, 8.23984, -6.07847, 6.25653, -12.24588, 6.34547, -7.67877, 11.45697, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -6.4492, 4.21972, -6.66524, -3.86946, -7.70453, -0.18301, -1.05232, 10.25986, -9.64081, 3.66411, -2.5088, 4.16982, -0.25922, 10.64388, 1.23071, 12.78048, 6.79559, 12.94839, 11.10159, 9.51824]}, {"time": 0.4333}]}, "zhu_11": {"zhu_11": [{}, {"time": 0.1, "offset": 92, "vertices": [2.04628, 26.48057, 17.96239, 15.31036, -5.47049, 26.35864, 13.91727, 21.02246, -9.60443, 8.60096, 0.41415, 12.88273]}, {"time": 0.2, "offset": 92, "vertices": [2.04628, 26.48057, 17.96239, 15.31036, -5.47049, 26.35864, 13.91727, 21.02246, -56.54698, -16.52762, -42.29214, 26.01946]}, {"time": 0.3, "offset": 92, "vertices": [11.65009, 26.79999, 23.20967, 7.58266, -7.27951, 28.70742, 14.79196, 23.63895, -42.20099, 1.43203, -22.04236, 29.34193]}, {"time": 0.4333}]}, "zhu_12": {"zhu_12": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "offset": 38, "vertices": [-1.07327, -4.6814, -0.29303, -4.79401, -0.32333, 5.49215, -1.21869, 5.36498, -2.51108, 1.98048, -2.80167, 1.54272], "curve": 0.25, "c3": 0.75}, {"time": 0.2, "offset": 42, "vertices": [-7.85909, 6.05975, -6.57806, 7.4311, -6.32077, 3.15688, -5.61362, 4.29057], "curve": 0.25, "c3": 0.75}, {"time": 0.3, "offset": 42, "vertices": [-5.06091, -1.78561, -5.30768, -0.80005, -6.56784, 1.42275, -6.18317, 2.63464], "curve": 0.25, "c3": 0.75}, {"time": 0.4333}]}, "zhu_14": {"zhu_14": [{"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "offset": 16, "vertices": [3.01163, 6.77353, 0.39917, 1.39563, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -4.65652, -9.97256, -3.93225, -10.27896], "curve": 0.25, "c3": 0.75}, {"time": 0.3, "offset": 16, "vertices": [-1.07605, -2.3847, -0.76218, -2.43182, 0, 0, 0.01218, -1.17154], "curve": 0.25, "c3": 0.75}, {"time": 0.4333}]}}}}, "2": {"slots": {"3": {"attachment": [{"time": 0.1, "name": "dg_xl_30.png Animation_00003"}, {"time": 0.1333, "name": "dg_xl_30.png Animation_00004"}, {"time": 0.1667, "name": "dg_xl_30.png Animation_00005"}, {"time": 0.2, "name": "dg_xl_30.png Animation_00006"}, {"time": 0.2333, "name": "dg_xl_30.png Animation_00007"}, {"time": 0.2667, "name": null}]}, "4": {"attachment": [{"time": 0.1, "name": "dg_xl_31.png Animation_00000"}, {"time": 0.1333, "name": "dg_xl_31.png Animation_00001"}, {"time": 0.1667, "name": "dg_xl_31.png Animation_00002"}, {"time": 0.2, "name": "dg_xl_31.png Animation_00003"}, {"time": 0.2333, "name": null}]}, "5": {"attachment": [{"time": 0.1, "name": "dg_xl_31.png Animation_00000"}, {"time": 0.1333, "name": "dg_xl_31.png Animation_00001"}, {"time": 0.1667, "name": "dg_xl_31.png Animation_00002"}, {"time": 0.2, "name": "dg_xl_31.png Animation_00003"}, {"time": 0.2333, "name": null}]}}, "bones": {"all2": {"rotate": [{"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 11.58, "curve": 0.25, "c3": 0.75}, {"time": 0.2333}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": 13.4, "y": -28.46, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -137.56, "y": -6.7, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": -90.55, "y": -5.22, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "all3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -8.98, "curve": 0.25, "c3": 0.75}, {"time": 0.1667}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": -2.46, "curve": 0.25, "c3": 0.75}, {"time": 0.1667}]}, "zhu_13": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 1.99, "curve": 0.25, "c3": 0.75}, {"time": 0.1667}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": -7.27, "y": -1.4, "curve": 0.25, "c3": 0.75}, {"time": 0.1667}]}, "zhu_0": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 7.81, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -8.64, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -2.22, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": -0.09, "y": 10.93, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 1.04, "y": 4.45, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 0.08, "y": 3.05, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "zhu_15": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 27.87, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -20.23, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 1.98, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "zhu_14": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -44.9, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -35.96, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -44.29, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": -2.66, "y": 14.1, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -4.88, "y": 10.28, "curve": 0.25, "c3": 0.75}, {"time": 0.2333}]}, "zhu_18": {"rotate": [{"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -11.69, "curve": 0.25, "c3": 0.75}, {"time": 0.2333}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": -53.09, "y": 12.39, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 83.34, "y": -31.79, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 69.34, "y": -17.31, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "zhu_2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 2.1, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -13.55, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -4.53, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": 11.03, "y": 1.04, "curve": 0.25, "c3": 0.75}, {"time": 0.1667}]}, "zhu_1": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -29.33, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -29.5, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 1.08, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": -13.78, "y": -18.08, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -29.61, "y": -12.32, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": -0.19, "y": -0.43, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "zhu_3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -5.26, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 8.86, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "zhu_4": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -19.77, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -5.35, "curve": 0.25, "c3": 0.75}, {"time": 0.2333}]}, "zhu_5": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -36.88, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -30.32, "curve": 0.25, "c3": 0.75}, {"time": 0.2333}]}, "zhu_6": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 43.32, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 25.38, "curve": 0.25, "c3": 0.75}, {"time": 0.2333}]}, "zhu_7": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 34.64, "curve": 0.25, "c3": 0.75}, {"time": 0.1667}]}, "all4": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -2.39, "curve": 0.25, "c3": 0.75}, {"time": 0.1667}]}, "all5": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 1.97, "curve": 0.25, "c3": 0.75}, {"time": 0.1667}]}, "all6": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 2.79, "curve": 0.25, "c3": 0.75}, {"time": 0.1667}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": -21.49, "y": -2.6, "curve": 0.25, "c3": 0.75}, {"time": 0.1667}]}, "zhu_21": {"rotate": [{"angle": -0.47, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -19.7, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 5.56, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -0.47}], "translate": [{"x": -2.77, "y": 1.68, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -10.63, "y": 10.29, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": -2.77, "y": 1.68}]}, "zhu_22": {"rotate": [{"angle": -1.64, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "angle": -6.01, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -1.64}]}, "zhu_23": {"rotate": [{"angle": -2, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.1, "curve": "stepped"}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 10.65, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -2}]}, "zhu_24": {"rotate": [{"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 7.77, "curve": 0.25, "c3": 0.75}, {"time": 0.2333}]}, "zhu_25": {"rotate": [{"angle": 1.85, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.1, "angle": 3.34, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 12.11, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 1.85}]}, "zhu_26": {"rotate": [{"angle": 5.54, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "angle": 14.38, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -14.87, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 5.54}]}, "zhu_27": {"rotate": [{"angle": -1.15, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.1333, "angle": -7.42, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 7.21, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -1.15}]}, "zhu_28": {"rotate": [{"angle": 4.56, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "angle": -10.55, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 18.34, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 4.56}]}, "zhu_29": {"rotate": [{"angle": 8.49, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.1333, "angle": -13.5, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 7.43, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 8.49}]}, "zhu_30": {"rotate": [{"angle": -2.29, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.1333, "angle": -5.33, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 1.89, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -2.29}]}, "zhu_31": {"rotate": [{"angle": 2.89, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "angle": -17.61, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 9.61, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 2.89}]}, "zhu_32": {"rotate": [{"angle": 4.08, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.1333, "angle": -10.75, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 11.01, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 4.08}]}, "zhu_33": {"rotate": [{"angle": 21.8, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -4.93, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 18, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 21.8}]}, "zhu_34": {"rotate": [{"angle": 13.03}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": 36.92, "y": -22.15, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 18.91, "y": -8.29, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 17.31, "y": -10.54, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "zhu_35": {"rotate": [{"angle": -3.68}]}, "zhu_36": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -7.3, "curve": 0.25, "c3": 0.75}, {"time": 0.1667}]}, "zhu_37": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -7.63, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -21.35, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -10.93, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "zhu_38": {"rotate": [{"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -8.4, "curve": 0.25, "c3": 0.75}, {"time": 0.2333}]}, "zhu_39": {"rotate": [{"angle": -0.29}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": 34.34, "y": 4.29, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 26.85, "y": -5.5, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 67.98, "y": 4.82, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "zhu_40": {"rotate": [{"angle": 0.01}]}, "zhu_41": {"scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": 0.873, "curve": 0.25, "c3": 0.75}, {"time": 0.1667}]}, "zhu_42": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -22.96, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -5.04, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -25.37, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "y": 1.115, "curve": 0.25, "c3": 0.75}, {"time": 0.1667}]}, "zhu_43": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 3.41, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -28.21, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 6.19, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "t2": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": 9.43, "y": -5, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -54.32, "y": 64.05, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": -15.74, "y": 13.02, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "t1": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": 3.5, "y": -14.77, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -102.95, "y": 7.92, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": -37.87, "y": -2.13, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "4": {"rotate": [{"time": 0.1, "angle": 3.53}, {"time": 0.1333, "angle": 0.35}, {"time": 0.1667, "angle": -1.28}], "translate": [{"time": 0.1, "x": -162.45, "y": 174.97}, {"time": 0.1333, "x": -474.95, "y": 252.8}, {"time": 0.1667, "x": -737.37, "y": 354.23}, {"time": 0.2, "x": -605.25, "y": 319.04}], "scale": [{"time": 0.1, "x": -2.198, "y": 4.349}, {"time": 0.1333, "x": -2.122, "y": 4.712}, {"time": 0.1667, "x": -2.484, "y": 5.268}, {"time": 0.2, "x": -2.982, "y": 4.777}]}, "3": {"rotate": [{"time": 0.1, "angle": -179.44}], "translate": [{"time": 0.1, "x": -105.59, "y": 257.79}, {"time": 0.2333, "x": -351.78, "y": 380.88}], "scale": [{"time": 0.1, "x": 2.791, "y": -3.713}]}, "5": {"rotate": [{"time": 0.1, "angle": 3.53}, {"time": 0.1333, "angle": 0.35}, {"time": 0.1667, "angle": -1.28}], "translate": [{"time": 0.1, "x": -162.45, "y": 174.97}, {"time": 0.1333, "x": -474.95, "y": 252.8}, {"time": 0.1667, "x": -737.37, "y": 354.23}, {"time": 0.2, "x": -605.25, "y": 319.04}], "scale": [{"time": 0.1, "x": -2.198, "y": 4.349}, {"time": 0.1333, "x": -2.122, "y": 4.712}, {"time": 0.1667, "x": -2.484, "y": 5.268}, {"time": 0.2, "x": -2.982, "y": 4.777}]}, "zhu_51": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": -6.75, "y": -10.3, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 41.82, "y": 56.38, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": -10.12, "y": -18, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "zhu_47": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -4.64, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 4.46, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -1.81, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "zhu_48": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -2.77, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 6.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -3.56, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "zhu_49": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -6.08, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 5.79, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -4.21, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "zhu_50": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -8.63, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 9.76, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -22.57, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}}}, "3": {"slots": {"dg_04": {"attachment": [{"time": 0.1, "name": "dg_04"}, {"time": 0.2333, "name": null}]}, "dg_4": {"attachment": [{"time": 0.1, "name": "dg_04"}, {"time": 0.2667, "name": null}]}}, "bones": {"all": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": 2.79, "y": -1.55, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 6.51, "y": 11.78, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "all2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -13.74, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 9.35, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": -24.91, "y": 22.55, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": -58.67, "y": -49.98, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "all3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 6.48, "curve": 0.25, "c3": 0.75}, {"time": 0.2333}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "y": -15.21, "curve": 0.25, "c3": 0.75}, {"time": 0.2333}]}, "zhu_12": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -1.99, "curve": 0.25, "c3": 0.75}, {"time": 0.2333}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": 6.31, "y": 0.57, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": -15.53, "y": 3.14, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "zhu_13": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -13.38, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 6.44, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": 6.33, "y": -19.86, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": -13.56, "y": 5.44, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "zhu_0": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -14.76, "curve": 0.25, "c3": 0.75}, {"time": 0.2333}]}, "zhu_15": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -54.51, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -10.18, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}], "translate": [{"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": -6.4, "y": 1.24, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "zhu_14": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -106.32, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -5.17, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}], "translate": [{"x": -5.45, "y": -6.07, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 6.03, "y": 2.48, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "zhu_18": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": 118.14, "y": -31.82, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 80.48, "y": -18.63, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "zhu_2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 5.57, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 8.29, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": 4.8, "y": 1.66, "curve": 0.25, "c3": 0.75}, {"time": 0.2333}]}, "zhu_1": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -21.07, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 12.76, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": -10.51, "y": -9.34, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": -4.85, "y": 8.05, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "zhu_21": {"rotate": [{"angle": -0.47, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.1, "angle": -42.59, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -14.28, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -0.47}], "translate": [{"x": -2.77, "y": 1.68, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": -22.41, "y": 50.35, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -2.77, "y": 1.68}]}, "zhu_22": {"rotate": [{"angle": -1.64, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "angle": -6.79, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -1.64}]}, "zhu_23": {"rotate": [{"angle": -2, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.1, "angle": -12.04, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -2}]}, "zhu_24": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 13.6, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -4.33, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "zhu_25": {"rotate": [{"angle": 1.85, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.1, "angle": 15.45, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -16.18, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 1.85}]}, "zhu_26": {"rotate": [{"angle": 5.54, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "angle": 38.37, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -12.99, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 5.54}]}, "zhu_27": {"rotate": [{"angle": -1.15, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.1, "angle": 7.52, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -8.36, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -1.15}]}, "zhu_28": {"rotate": [{"angle": 4.56, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "angle": 8.2, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -31.02, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 4.56}]}, "zhu_29": {"rotate": [{"angle": 8.49, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.1, "angle": 19.29, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -23.36, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 8.49}]}, "zhu_30": {"rotate": [{"angle": -2.29, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.1, "angle": 5.54, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -14.18, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -2.29}]}, "zhu_31": {"rotate": [{"angle": 2.89, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "angle": 11.87, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -17.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 2.89}]}, "zhu_32": {"rotate": [{"angle": 4.08, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.1, "angle": 9.93, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -20.45, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 4.08}]}, "zhu_33": {"rotate": [{"angle": 21.8, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 31.52, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 21.8}]}, "zhu_34": {"rotate": [{"angle": 13.03}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": 8.67, "y": -9.7, "curve": 0.25, "c3": 0.75}, {"time": 0.2333}]}, "zhu_35": {"rotate": [{"angle": -3.68}]}, "zhu_36": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 24.92, "curve": 0.25, "c3": 0.75}, {"time": 0.2333}]}, "zhu_37": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -28.63, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -5.21, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "zhu_39": {"rotate": [{"angle": -0.29}]}, "zhu_40": {"rotate": [{"angle": 0.01}]}, "zhu_42": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -5.72, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -1.44, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "zhu_43": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -11.25, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -4.19, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "t2": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": -22.06, "y": 14.44, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": -49.38, "y": -36.32, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "t1": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": -40.62, "y": 27.02, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": -59.11, "y": -60.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "1": {"rotate": [{"time": 0.1}, {"time": 0.1333, "angle": 34.66}, {"time": 0.2333, "angle": -153.43}], "translate": [{"time": 0.1, "y": 712.68}, {"time": 0.1333, "x": -75.14, "y": 741.14}, {"time": 0.1667, "x": -349.13, "y": 612.49}, {"time": 0.2, "x": -370.38, "y": 318.77}, {"time": 0.2333, "x": -364.31, "y": 252.74}], "scale": [{"time": 0.1, "x": 6.769, "y": 6.769}, {"time": 0.2333, "x": 8.295, "y": 8.295}]}, "6": {"rotate": [{"time": 0.1}, {"time": 0.1333, "angle": 34.66}, {"time": 0.2333, "angle": -153.43}], "translate": [{"time": 0.1, "y": 712.68}, {"time": 0.1333, "x": -75.14, "y": 741.14}, {"time": 0.1667, "x": -349.13, "y": 612.49}, {"time": 0.2, "x": -370.38, "y": 318.77}, {"time": 0.2333, "x": -364.31, "y": 252.74}], "scale": [{"time": 0.1, "x": 6.769, "y": 6.769}, {"time": 0.2333, "x": 8.295, "y": 8.295}]}, "zhu_51": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": 62.22, "y": 17.7, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 23.28, "y": 70.42, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}}}, "4": {"slots": {"3": {"attachment": [{"time": 0.3667, "name": "dg_xl_30.png Animation_00000"}, {"time": 0.4, "name": "dg_xl_30.png Animation_00001"}, {"time": 0.4333, "name": "dg_xl_30.png Animation_00002"}, {"time": 0.4667, "name": "dg_xl_30.png Animation_00003"}, {"time": 0.5, "name": "dg_xl_30.png Animation_00004"}, {"time": 0.5333, "name": "dg_xl_30.png Animation_00005"}, {"time": 0.5667, "name": "dg_xl_30.png Animation_00006"}, {"time": 0.6, "name": "dg_xl_30.png Animation_00007"}, {"time": 0.6333, "name": null}]}, "101944w3nhjcmqalolnlnl": {"color": [{"time": 0.3333, "color": "ffffff00"}, {"time": 0.5, "color": "ffffffff"}, {"time": 0.6667, "color": "ffffff00"}], "attachment": [{"time": 0.3333, "name": "101944w3nhjcmqalolnlnl"}]}, "101944w3nhjcmqalolnlnl2": {"color": [{"time": 0.5, "color": "ffffff00"}, {"time": 0.6667, "color": "ffffffff"}, {"time": 1, "color": "ffffff00"}], "attachment": [{"time": 0.5, "name": "101944w3nhjcmqalolnlnl"}]}, "105027tcxrjrcjppgu7uug": {"color": [{"time": 0.3667, "color": "ffffff00"}, {"time": 0.5, "color": "ffffffff", "curve": "stepped"}, {"time": 0.8333, "color": "ffffffff"}, {"time": 1, "color": "ffffff00"}], "attachment": [{"time": 0.3667, "name": "105027tcxrjrcjppgu7uug"}]}, "bz": {"attachment": [{"time": 0.3333, "name": "bao_00001"}, {"time": 0.3667, "name": "bao_00003"}, {"time": 0.4, "name": "bao_00005"}, {"time": 0.4333, "name": "bao_00007"}, {"time": 0.4667, "name": "bao_00009"}, {"time": 0.5, "name": "bao_00011"}, {"time": 0.5333, "name": "bao_00013"}, {"time": 0.5667, "name": "bao_00015"}, {"time": 0.6, "name": "bao_00017"}, {"time": 0.6333, "name": "bao_00019"}, {"time": 0.6667, "name": null}]}, "bz2": {"attachment": [{"time": 0.5667, "name": "bao_00001"}, {"time": 0.6, "name": "bao_00003"}, {"time": 0.6333, "name": "bao_00005"}, {"time": 0.6667, "name": "bao_00007"}, {"time": 0.7, "name": "bao_00009"}, {"time": 0.7333, "name": "bao_00011"}, {"time": 0.7667, "name": "bao_00013"}, {"time": 0.8, "name": "bao_00015"}, {"time": 0.8333, "name": "bao_00017"}, {"time": 0.8667, "name": "bao_00019"}, {"time": 0.9, "name": null}]}, "round_064_00000": {"color": [{"time": 0.4, "color": "ffffff00"}, {"time": 0.5333, "color": "ffffffff"}, {"time": 0.6667, "color": "ffffff00"}], "attachment": [{"time": 0.4, "name": "round_064_00000"}]}, "yunshi": {"attachment": [{"time": 0.3, "name": "yunshi_00000"}, {"time": 0.3333, "name": "yunshi_00001"}, {"time": 0.3667, "name": "yunshi_00002"}, {"time": 0.4, "name": "yunshi_00003"}, {"time": 0.4333, "name": "yunshi_00004"}, {"time": 0.4667, "name": "yunshi_00005"}, {"time": 0.5, "name": "yunshi_00006"}, {"time": 0.5333, "name": "yunshi_00007"}, {"time": 0.5667, "name": "yunshi_00008"}, {"time": 0.6, "name": "yunshi_00009"}, {"time": 0.6333, "name": "yunshi_00010"}, {"time": 0.6667, "name": "yunshi_00011"}, {"time": 0.7, "name": "yunshi_00012"}, {"time": 0.7333, "name": "yunshi_00013"}, {"time": 0.7667, "name": null}]}, "yunshi2": {"attachment": [{"time": 0.5333, "name": "yunshi_00000"}, {"time": 0.5667, "name": "yunshi_00001"}, {"time": 0.6, "name": "yunshi_00002"}, {"time": 0.6333, "name": "yunshi_00003"}, {"time": 0.6667, "name": "yunshi_00004"}, {"time": 0.7, "name": "yunshi_00005"}, {"time": 0.7333, "name": "yunshi_00006"}, {"time": 0.7667, "name": "yunshi_00007"}, {"time": 0.8, "name": "yunshi_00008"}, {"time": 0.8333, "name": "yunshi_00009"}, {"time": 0.8667, "name": "yunshi_00010"}, {"time": 0.9, "name": "yunshi_00011"}, {"time": 0.9333, "name": "yunshi_00012"}, {"time": 0.9667, "name": "yunshi_00013"}, {"time": 1, "name": null}]}, "zxc": {"color": [{"time": 0.3667, "color": "ffffff00"}, {"time": 0.5, "color": "ffffffff"}, {"time": 0.6333, "color": "ffffff00"}], "attachment": [{"time": 0.3667, "name": "zxc"}]}}, "bones": {"all": {"rotate": [{"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -25.25, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 8.78, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -19.73, "curve": 0.25, "c3": 0.75}, {"time": 0.8333}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "y": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 4.81, "y": -6.61, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": -20.64, "y": 100.4, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 0.04, "y": -21.02, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 8.73, "y": 4.82, "curve": 0.25, "c3": 0.75}, {"time": 0.8333}], "scale": [{"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 1.847, "y": 1.847, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 1.257, "y": 1.257, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "all2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -5.48, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -9.85, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 18.87, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 25.24, "curve": "stepped"}, {"time": 0.6667, "angle": 25.24, "curve": 0.25, "c3": 0.75}, {"time": 0.8333}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": 4.18, "y": -34.81, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": 34.81, "y": -51.44, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": -143.48, "y": 358.57, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -139.25, "y": -130.58, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": -132.42, "y": -123.75, "curve": 0.25, "c3": 0.75}, {"time": 0.8333}]}, "zhu_12": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 1.62, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -9.16, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -9.25, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 8.14, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 0.02, "curve": 0.25, "c3": 0.75}, {"time": 0.8}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": -9.79, "y": 1.04, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -2.13, "y": 15.59, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": -10.57, "y": 14.83, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": -7.49, "y": -14.5, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": -6.46, "y": -12.5, "curve": 0.25, "c3": 0.75}, {"time": 0.8}]}, "zhu_13": {"rotate": [{"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -15.09, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -18.91, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.4, "angle": -24.07, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.4667, "angle": 15.35, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 13.16, "curve": 0.25, "c3": 0.75}, {"time": 0.8}], "translate": [{"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": 0.56, "y": -10.4, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": -4.98, "y": 24.22, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": 1.56, "y": 15.4, "curve": 0.25, "c3": 0.75}, {"time": 0.8}]}, "zhu_0": {"rotate": [{"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -16.42, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -22.59, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -12.55, "curve": 0.25, "c3": 0.75}, {"time": 0.8}], "translate": [{"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": 4.07, "y": 4.59, "curve": 0.25, "c3": 0.75}, {"time": 0.8}]}, "zhu_15": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -20.53, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -48.18, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -99.01, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -45.38, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -51.4, "curve": 0.25, "c3": 0.75}, {"time": 0.8}], "translate": [{"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -48.32, "y": -18.25, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": -52.69, "y": 1.66, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": -19.01, "y": -37.62, "curve": "stepped"}, {"time": 0.6333, "x": -19.01, "y": -37.62, "curve": 0.25, "c3": 0.75}, {"time": 0.8}]}, "zhu_14": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -21.51, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -102.17, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -66.27, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -18.76, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -9.88, "curve": 0.25, "c3": 0.75}, {"time": 0.8}]}, "zhu_18": {"rotate": [{"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -1.39, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -17.46, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -17.84, "curve": 0.25, "c3": 0.75}, {"time": 0.8}], "translate": [{"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": 316.91, "y": -94.39, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": 345.79, "y": -94.73, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": 294.94, "y": -174.19, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": 209.98, "y": -124.8, "curve": 0.25, "c3": 0.75}, {"time": 0.8}]}, "zhu_2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -62.94, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -110.96, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -141.07, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -100.35, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -82.73, "curve": 0.25, "c3": 0.75}, {"time": 0.8}], "translate": [{"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": 3.73, "y": -0.82, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": -26.25, "y": 0.05, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": -26.53, "y": -15.69, "curve": 0.25, "c3": 0.75}, {"time": 0.8}]}, "zhu_1": {"rotate": [{"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -14.25, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 5.09, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -11.55, "curve": 0.25, "c3": 0.75}, {"time": 0.8}], "translate": [{"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": 4.14, "y": -13.02, "curve": 0.25, "c3": 0.75}, {"time": 0.8}]}, "all4": {"rotate": [{"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -13.26, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 4.47, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -3.28, "curve": 0.25, "c3": 0.75}, {"time": 0.8}], "scale": [{"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": 0.753, "curve": 0.25, "c3": 0.75}, {"time": 0.4667}]}, "all5": {"rotate": [{"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -8.26, "curve": 0.25, "c3": 0.75}, {"time": 0.4667}]}, "zhu_21": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -2.17, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -7.34, "curve": 0.25, "c3": 0.75}, {"time": 0.6333}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": -13.13, "y": 8.86, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -13.58, "y": 13.88, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": -17.59, "y": 9.37, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": -16.15, "y": 7.61, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": -19.68, "y": 11.67, "curve": 0.25, "c3": 0.75}, {"time": 0.8}]}, "zhu_22": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -5.95, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -12.64, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -9.16, "curve": 0.25, "c3": 0.75}, {"time": 0.8333}]}, "zhu_23": {"rotate": [{"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 25.78, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 14.76, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -15.65, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -7.91, "curve": 0.25, "c3": 0.75}, {"time": 0.8333}]}, "zhu_24": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 8.22, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 11.94, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -4.44, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 7.17, "curve": 0.25, "c3": 0.75}, {"time": 0.6333}], "translate": [{"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": -17.93, "y": -9.88, "curve": 0.25, "c3": 0.75}, {"time": 0.4667}], "scale": [{"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "y": 0.828, "curve": 0.25, "c3": 0.75}, {"time": 0.4667}]}, "zhu_25": {"rotate": [{"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 15.29, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -19.33, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 28.92, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 9.63, "curve": 0.25, "c3": 0.75}, {"time": 0.8333}], "translate": [{"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": -14.9, "y": -9.67, "curve": 0.25, "c3": 0.75}, {"time": 0.5}]}, "zhu_26": {"rotate": [{"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 25.4, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -4.32, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 35.87, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 38.88, "curve": 0.25, "c3": 0.75}, {"time": 0.8333}], "translate": [{"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 12.88, "y": -13.99, "curve": 0.25, "c3": 0.75}, {"time": 0.5}]}, "zhu_27": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -4.09, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 2.14, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -8.25, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -26.56, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 5.93, "curve": 0.25, "c3": 0.75}, {"time": 0.8}]}, "zhu_28": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -7.25, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 13.33, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -10.48, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -22.01, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 10.85, "curve": 0.25, "c3": 0.75}, {"time": 0.8}]}, "zhu_29": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -14.56, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 20.57, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -13.05, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -22.6, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 18.58, "curve": 0.25, "c3": 0.75}, {"time": 0.8}]}, "zhu_30": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -7.47, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 3.48, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -10.51, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -17.69, "curve": 0.25, "c3": 0.75}, {"time": 0.6333}]}, "zhu_31": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -19.54, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 2.98, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -16.82, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -27.07, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 8.83, "curve": 0.25, "c3": 0.75}, {"time": 0.8}]}, "zhu_32": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -13.4, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 6.05, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -9.76, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -20.52, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 9.48, "curve": 0.25, "c3": 0.75}, {"time": 0.8}]}, "zhu_33": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 23.71, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 38.02, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 24.79, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 17.28, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 53.13, "curve": 0.25, "c3": 0.75}, {"time": 0.8}]}, "zhu_34": {"rotate": [{"angle": 13.03}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": -0.31, "y": 3.22, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 18, "y": 2.01, "curve": 0.25, "c3": 0.75}, {"time": 0.5}]}, "zhu_35": {"rotate": [{"angle": -3.68}]}, "zhu_36": {"rotate": [{"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -43.54, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -21.87, "curve": 0.25, "c3": 0.75}, {"time": 0.8333}]}, "zhu_37": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -29.41, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -20.48, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -4.16, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -27.76, "curve": 0.25, "c3": 0.75}, {"time": 0.8333}]}, "zhu_39": {"rotate": [{"angle": -0.29}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": -2.94, "y": 30.62, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": 32.09, "y": 11.06, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 18, "y": 2.01, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 22.13, "y": -11.38, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 18.83, "y": -1.73, "curve": 0.25, "c3": 0.75}, {"time": 0.8333}]}, "zhu_40": {"rotate": [{"angle": 0.01}]}, "zhu_42": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -18.26, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -31.33, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -28.35, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -21.64, "curve": 0.25, "c3": 0.75}, {"time": 0.8333}]}, "zhu_43": {"rotate": [{"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 10.8, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 14.51, "curve": 0.25, "c3": 0.75}, {"time": 0.8333}]}, "t2": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": -1.62, "y": -3.24, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": 61.1, "y": -18.04, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": -40.54, "y": 494.61, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -32.3, "y": -47.79, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": -39.11, "y": -39.38, "curve": 0.25, "c3": 0.75}, {"time": 0.8333}]}, "t1": {"translate": [{"x": 6.48, "y": 3.24, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": -9.72, "y": -12.95, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -9.25, "y": -22.2, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": -47.45, "y": 398.78, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -60.55, "y": -98.2, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": -59.31, "y": -95.42, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 6.48, "y": 3.24}]}, "3": {"rotate": [{"time": 0.3667, "angle": -154.47, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.4, "angle": -136.83, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4333, "angle": -75.91}, {"time": 0.4667, "angle": -30.18}, {"time": 0.5, "angle": -30.58}], "translate": [{"time": 0.3667, "x": 21.33, "y": 1144.83, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": -220.82, "y": 1218.8}, {"time": 0.4333, "x": -634.89, "y": 771.38}, {"time": 0.4667, "x": -689.25, "y": 238.47}, {"time": 0.5, "x": -672.77, "y": 64.48}], "scale": [{"time": 0.3667, "x": -3.533, "y": 3.731, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": -4.16, "y": 4.862}]}, "zhu_51": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": -2.74, "y": 3.03, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": 4.59, "y": 12.89, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": 31.11, "y": 59.17, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": 0.81, "y": -20.35, "curve": 0.25, "c3": 0.75}, {"time": 0.8}]}, "yunshi": {"rotate": [{"time": 0.3}, {"time": 0.4667, "angle": 4.78}], "translate": [{"time": 0.3, "x": -286.14, "y": 390.48}, {"time": 0.4667, "x": -257.03, "y": 260.27}], "scale": [{"time": 0.3, "x": -4.519, "y": 4.519}, {"time": 0.4667, "x": -3.031, "y": 3.031}]}, "bbbb": {"translate": [{"time": 0.4, "x": -270.11, "y": -9.88}, {"time": 0.6667, "x": -284.84, "y": -9.88}], "scale": [{"time": 0.4, "x": 0.736, "y": 0.736}, {"time": 0.5333, "x": 15.106, "y": 15.106}, {"time": 0.6667, "x": 20.088, "y": 20.088}]}, "sss": {"translate": [{"time": 0.3333, "x": -273.9, "y": -31.04}], "scale": [{"time": 0.3333, "x": 1.344, "y": 0.804}, {"time": 0.6667, "x": 16.326, "y": 11.645}]}, "sss2": {"translate": [{"time": 0.5, "x": -299.62, "y": -31.04}], "scale": [{"time": 0.5, "x": 0.863, "y": 0.516}, {"time": 1, "x": 16.02, "y": 12.322}]}, "yunshi2": {"rotate": [{"time": 0.5333}, {"time": 0.7, "angle": 4.61}], "translate": [{"time": 0.5333, "x": -341.29, "y": 451}, {"time": 0.7, "x": -241.19, "y": 280.37}], "scale": [{"time": 0.5333, "x": -5.75, "y": 5.75}, {"time": 0.7, "x": -3.306, "y": 3.306}]}, "zxc": {"translate": [{"time": 0.3667, "x": -274.18}, {"time": 0.6333, "x": -274.18, "y": -7.4}], "scale": [{"time": 0.3667, "x": 0.561, "y": 0.561}, {"time": 0.6333, "x": 10.922, "y": 10.922}]}, "zxct": {"translate": [{"x": 145.24, "y": 20.45}], "scale": [{"x": 1.401, "y": 1.401}]}, "dluie": {"translate": [{"time": 0.3667, "x": -265.12, "y": -42.62}, {"time": 0.5, "x": -284.13, "y": -40.89}, {"time": 1, "x": -296.67, "y": -40.89}], "scale": [{"time": 0.3667, "x": -2.212, "y": 1.591}, {"time": 0.5, "x": -3.438, "y": 2.473}, {"time": 1, "x": -3.898, "y": 2.804}]}, "bz": {"translate": [{"time": 0.3333, "x": -312.69, "y": 25.66}], "scale": [{"time": 0.3333, "x": 3.028, "y": 3.028}]}, "bz2": {"translate": [{"time": 0.5667, "x": -312.69, "y": 25.66}], "scale": [{"time": 0.5667, "x": 5.194, "y": 5.194}]}}, "deform": {"default": {"zhu_4": {"zhu_4": [{"time": 0.1}, {"time": 0.2, "offset": 4, "vertices": [2.65262, 2.16339, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -7.13804, -12.38577, -12.21918, -7.41962, -18.73657, -12.6052, -20.73509, -30.15938, -12.53438, -20.03926, -9.96041, -12.42424, -14.71162, -6.09476, -11.43915, -2.25121, -2.00233, -11.48541, -7.28409, -9.10296]}, {"time": 0.3667}]}, "zhu_15": {"zhu_15": [{"time": 0.6333}]}}}}, "idle": {"bones": {"all3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -1.12, "curve": 0.25, "c3": 0.75}, {"time": 1.6667}]}, "zhu_14": {"rotate": [{"angle": 1.33, "curve": 0.382, "c2": 0.58, "c3": 0.734}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "angle": 4.99, "curve": 0.243, "c3": 0.651, "c4": 0.61}, {"time": 1.6667, "angle": 1.33}]}, "zhu_30": {"rotate": [{"angle": -1.71, "curve": 0.364, "c2": 0.64, "c3": 0.7}, {"time": 0.1, "angle": -1.35, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": -7.52, "curve": 0.244, "c3": 0.704, "c4": 0.81}, {"time": 1.6667, "angle": -1.71}]}, "zhu_27": {"rotate": [{"angle": -1.17, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 0.1333, "angle": -2.44, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": 11.46, "curve": 0.243, "c3": 0.691, "c4": 0.76}, {"time": 1.6667, "angle": -1.17}]}, "zhu_29": {"rotate": [{"angle": -1.66, "curve": 0.377, "c2": 0.51, "c3": 0.749}, {"time": 0.4, "angle": -8.25, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "angle": 5.66, "curve": 0.249, "c3": 0.627, "c4": 0.51}, {"time": 1.6667, "angle": -1.66}]}, "zhu_31": {"rotate": [{"angle": 0.23, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 0.2, "angle": 3.38, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "angle": -14.77, "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 1.6667, "angle": 0.23}]}, "all4": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -2.69, "curve": 0.25, "c3": 0.75}, {"time": 1.6667}]}, "zhu_36": {"rotate": [{"angle": -5.5, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -3.11, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -5.5}]}, "zhu_37": {"rotate": [{"angle": -5.18, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -8.02, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -5.18}]}, "zhu_34": {"rotate": [{"angle": 13.03}]}, "zhu_40": {"rotate": [{"angle": 0.01}]}, "zhu_42": {"rotate": [{"angle": -6.83, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -3.82, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -6.83}]}, "zhu_41": {"rotate": [{"angle": -0.77, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -4.06, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -0.77}]}, "zhu_5": {"rotate": [{"angle": 2.52, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "angle": 14.52, "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 1.6667, "angle": 2.52}]}, "zhu_43": {"rotate": [{"angle": 2.38, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 2.38}]}, "zhu_20": {"rotate": [{"angle": 8.03, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 0.2, "angle": 10.98, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "angle": -5.99, "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 1.6667, "angle": 8.03}]}, "zhu_33": {"rotate": [{"angle": 15.16, "curve": 0.377, "c2": 0.51, "c3": 0.749}, {"time": 0.4, "angle": 19.22, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "angle": 10.65, "curve": 0.249, "c3": 0.627, "c4": 0.51}, {"time": 1.6667, "angle": 15.16}]}, "zhu_35": {"rotate": [{"angle": -3.68}]}, "zhu_15": {"rotate": [{"angle": -2.97, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 0.1333, "angle": -3.27, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "curve": 0.243, "c3": 0.691, "c4": 0.76}, {"time": 1.6667, "angle": -2.97}]}, "all6": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -1.6, "curve": 0.25, "c3": 0.75}, {"time": 1.6667}]}, "zhu_12": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -2.02, "curve": 0.25, "c3": 0.75}, {"time": 1.6667}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 4.58, "y": -0.17, "curve": 0.25, "c3": 0.75}, {"time": 1.6667}]}, "zhu_0": {"rotate": [{"angle": -0.56, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -4.28, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.6667, "angle": -0.56}], "translate": [{"x": 0.53, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 4.05, "y": 0.03, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.6667, "x": 0.53}]}, "zhu_25": {"rotate": [{"angle": -1.29, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "angle": -7.45, "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 1.6667, "angle": -1.29}]}, "zhu_19": {"rotate": [{"angle": 4.24, "curve": 0.364, "c2": 0.64, "c3": 0.7}, {"time": 0.1, "angle": 5.05, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": -8.68, "curve": 0.244, "c3": 0.704, "c4": 0.81}, {"time": 1.6667, "angle": 4.24}]}, "zhu_39": {"rotate": [{"angle": -0.29}]}, "zhu_3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -4.08, "curve": 0.25, "c3": 0.75}, {"time": 1.6667}]}, "zhu_1": {"rotate": [{"angle": -0.62, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": -6.79, "curve": 0.243, "c3": 0.691, "c4": 0.76}, {"time": 1.6667, "angle": -0.62}]}, "all2": {"translate": [{"y": -1.15, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 3.44, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "y": -1.15}]}, "zhu_2": {"rotate": [{"angle": -0.13, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -4.88, "curve": 0.246, "c3": 0.718, "c4": 0.86}, {"time": 1.6667, "angle": -0.13}]}, "zhu_17": {"rotate": [{"angle": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 0.2}]}, "zhu_26": {"rotate": [{"angle": -2.86, "curve": 0.382, "c2": 0.56, "c3": 0.738}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": -9.11, "curve": 0.244, "c3": 0.644, "c4": 0.58}, {"time": 1.6667, "angle": -2.86}]}, "zhu_10": {"rotate": [{"angle": -7.87, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1667, "angle": -9.77, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 4.88, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.6667, "angle": -7.87}]}, "zhu_22": {"rotate": [{"angle": 3.33, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 9.07, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.6667, "angle": 3.33}]}, "zhu_11": {"rotate": [{"angle": -3.41, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.3333, "angle": -10.96, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 9.56, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.6667, "angle": -3.41}]}, "zhu_24": {"rotate": [{"angle": -0.2, "curve": 0.364, "c2": 0.64, "c3": 0.7}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": -3.45, "curve": 0.244, "c3": 0.704, "c4": 0.81}, {"time": 1.6667, "angle": -0.2}]}, "zhu_6": {"rotate": [{"angle": -1.5, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -11.52, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.6667, "angle": -1.5}]}, "zhu_9": {"rotate": [{"angle": -1.25, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -1.25}]}, "zhu_32": {"rotate": [{"angle": -1.58, "curve": 0.382, "c2": 0.56, "c3": 0.738}, {"time": 0.3, "angle": 4.39, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": -14.46, "curve": 0.244, "c3": 0.644, "c4": 0.58}, {"time": 1.6667, "angle": -1.58}]}, "all5": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 1.26, "curve": 0.25, "c3": 0.75}, {"time": 1.6667}]}, "zhu_4": {"rotate": [{"angle": 0.29, "curve": 0.364, "c2": 0.64, "c3": 0.7}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 4.94, "curve": 0.244, "c3": 0.704, "c4": 0.81}, {"time": 1.6667, "angle": 0.29}]}, "zhu_21": {"rotate": [{"angle": 0.33, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 2.53, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.6667, "angle": 0.33}]}, "zhu_23": {"rotate": [{"angle": 9.29, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 14.7, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.6667, "angle": 9.29}]}, "zhu_28": {"rotate": [{"angle": -6.28, "curve": 0.382, "c2": 0.58, "c3": 0.734}, {"time": 0.2667, "angle": -11.9, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "angle": 9.14, "curve": 0.243, "c3": 0.651, "c4": 0.61}, {"time": 1.6667, "angle": -6.28}]}, "zhu_8": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 0.99, "y": -0.05, "curve": 0.25, "c3": 0.75}, {"time": 1.6667}]}, "zhu_51": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 9.63, "y": 12.73, "curve": 0.25, "c3": 0.75}, {"time": 1.6667}]}, "zhu_47": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -4, "curve": 0.25, "c3": 0.75}, {"time": 1.6667}]}, "zhu_48": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -2.3, "curve": 0.25, "c3": 0.75}, {"time": 1.6667}]}, "zhu_49": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -6.05, "curve": 0.25, "c3": 0.75}, {"time": 1.6667}]}, "zhu_50": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -12.94, "curve": 0.25, "c3": 0.75}, {"time": 1.6667}]}}, "deform": {"default": {"zhu_4": {"zhu_4": [{}, {"time": 0.8333, "vertices": [1.5527, -0.05645, 1.43878, -0.58671, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.5527, -0.05645, 0.54206, 1.45616, 2.99246, -3.21857, 1.4679, -2.38572, 4.88104, -0.17749], "curve": 0.25, "c3": 0.75}, {"time": 1.6667}]}, "zhu_12": {"zhu_12": [{}, {"time": 0.8333, "offset": 6, "vertices": [2.2151, 5.11385, 2.35513, 5.05082, 11.38533, 4.78549, 11.51257, 4.46972, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6.63606, 0.36452, 6.64343, 0.1814, 9.86429, -1.26016, 9.82532, -1.53171, 0.22772, -4.14761, 0.11316, -4.15232, 0, 0, 0, 0, 20.92517, 7.57143, 10.58075, 4.49467, 10.70013, 4.20119, 15.26324, 7.51278, 15.46411, 7.08904, 14.66393, -5.89128, 14.49561, -6.2934, 10.22752, -4.43032, 10.10132, -4.71067, 6.68169, -0.46496, 6.6662, -0.64903, 8.99361, -0.13722, 8.98584, -0.38518], "curve": 0.25, "c3": 0.75}, {"time": 1.6667}]}}}}, "jump2": {"slots": {"a_canying": {"attachment": [{"time": 0.0667, "name": "a_canying2"}, {"time": 0.1667, "name": null}]}, "zhu_0": {"attachment": [{"time": 0.0667, "name": null}, {"time": 0.1667, "name": "zhu_0"}]}, "zhu_1": {"attachment": [{"time": 0.0667, "name": null}, {"time": 0.1667, "name": "zhu_1"}]}, "zhu_2": {"attachment": [{"time": 0.0667, "name": null}, {"time": 0.1667, "name": "zhu_2"}]}, "zhu_3": {"attachment": [{"time": 0.0667, "name": null}, {"time": 0.1667, "name": "zhu_3"}]}, "zhu_4": {"attachment": [{"time": 0.0667, "name": null}, {"time": 0.1667, "name": "zhu_4"}]}, "zhu_5": {"attachment": [{"time": 0.0667, "name": null}, {"time": 0.1667, "name": "zhu_5"}]}, "zhu_6": {"attachment": [{"time": 0.0667, "name": null}, {"time": 0.1667, "name": "zhu_6"}]}, "zhu_7": {"attachment": [{"time": 0.0667, "name": null}, {"time": 0.1667, "name": "zhu_7"}]}, "zhu_8": {"attachment": [{"time": 0.0667, "name": null}, {"time": 0.1667, "name": "zhu_8"}]}, "zhu_10": {"attachment": [{"time": 0.0667, "name": null}, {"time": 0.1667, "name": "zhu_10"}]}, "zhu_11": {"attachment": [{"time": 0.0667, "name": null}, {"time": 0.1667, "name": "zhu_11"}]}, "zhu_12": {"attachment": [{"time": 0.0667, "name": null}, {"time": 0.1667, "name": "zhu_12"}]}, "zhu_13": {"attachment": [{"time": 0.0667, "name": null}, {"time": 0.1667, "name": "zhu_13"}]}, "zhu_14": {"attachment": [{"time": 0.0667, "name": null}, {"time": 0.1667, "name": "zhu_14"}]}, "zhu_15": {"attachment": [{"time": 0.0667, "name": null}, {"time": 0.1667, "name": "zhu_15"}]}, "zhu_16": {"attachment": [{"time": 0.0667, "name": null}, {"time": 0.1667, "name": "zhu_16"}]}, "zhu_17": {"attachment": [{"time": 0.0667, "name": null}, {"time": 0.1667, "name": "zhu_17"}]}}, "bones": {"all2": {"rotate": [{}, {"time": 0.0667, "angle": 4.97}, {"time": 0.1667}], "translate": [{}, {"time": 0.0667, "x": -28.79, "y": -19.7}, {"time": 0.1667}]}, "all3": {"rotate": [{}, {"time": 0.0667, "angle": -1.05}, {"time": 0.1667}], "translate": [{}, {"time": 0.0667, "x": -4.03, "y": 0.35}, {"time": 0.1667}]}, "zhu_13": {"rotate": [{}, {"time": 0.0667, "angle": 4.79}, {"time": 0.1667}]}, "zhu_0": {"rotate": [{}, {"time": 0.0667, "angle": 1.61}, {"time": 0.1667}]}, "zhu_15": {"rotate": [{}, {"time": 0.0667, "angle": 13.33}, {"time": 0.1667}], "scale": [{}, {"time": 0.0667, "x": 0.856, "y": 0.856}, {"time": 0.1667}]}, "zhu_14": {"rotate": [{}, {"time": 0.0667, "angle": 8.29}, {"time": 0.1667}]}, "zhu_2": {"rotate": [{"angle": -15.32}, {"time": 0.0667, "angle": -11.05}, {"time": 0.1667}]}, "zhu_1": {"rotate": [{}, {"time": 0.0667, "angle": 16.27}, {"time": 0.1667}], "translate": [{}, {"time": 0.0667, "x": -5.49, "y": 16.98}, {"time": 0.1667}]}, "zhu_21": {"rotate": [{"time": 0.0667}, {"time": 0.1667, "angle": -0.47}], "translate": [{}, {"time": 0.0667, "x": -14.5, "y": 8.37}, {"time": 0.1667, "x": -2.77, "y": 1.68}]}, "zhu_22": {"rotate": [{"time": 0.0667}, {"time": 0.1667, "angle": -1.64}]}, "zhu_23": {"rotate": [{"time": 0.0667}, {"time": 0.1667, "angle": -2}]}, "zhu_24": {"translate": [{}, {"time": 0.0667, "x": -6.48, "y": -4.51}, {"time": 0.1667}]}, "zhu_25": {"rotate": [{"time": 0.0667}, {"time": 0.1667, "angle": 1.85}]}, "zhu_26": {"rotate": [{"time": 0.0667}, {"time": 0.1667, "angle": 5.54}]}, "zhu_27": {"rotate": [{}, {"time": 0.0667, "angle": 12.63}, {"time": 0.1667, "angle": -1.15}]}, "zhu_28": {"rotate": [{}, {"time": 0.0667, "angle": 19.86}, {"time": 0.1667, "angle": 4.56}]}, "zhu_29": {"rotate": [{}, {"time": 0.0667, "angle": 7.44}, {"time": 0.1667, "angle": 8.49}]}, "zhu_30": {"rotate": [{}, {"time": 0.0667, "angle": 10.8}, {"time": 0.1667, "angle": -2.29}]}, "zhu_31": {"rotate": [{}, {"time": 0.0667, "angle": 7.16}, {"time": 0.1667, "angle": 2.89}]}, "zhu_32": {"rotate": [{}, {"time": 0.0667, "angle": 4.48}, {"time": 0.1667, "angle": 4.08}]}, "zhu_33": {"rotate": [{}, {"time": 0.0667, "angle": 43.54}, {"time": 0.1667, "angle": 21.8}]}, "zhu_34": {"rotate": [{"angle": 13.03}]}, "zhu_35": {"rotate": [{"angle": -3.68}]}, "zhu_36": {"rotate": [{}, {"time": 0.0667, "angle": -19.18}, {"time": 0.1667}], "translate": [{}, {"time": 0.0667, "x": -2.24, "y": -3.16}, {"time": 0.1667}]}, "zhu_37": {"rotate": [{}, {"time": 0.0667, "angle": -0.99}, {"time": 0.1667}]}, "zhu_39": {"rotate": [{"angle": -0.29}]}, "zhu_40": {"rotate": [{"angle": 0.01}]}, "zhu_41": {"rotate": [{}, {"time": 0.0667, "angle": 14.79}, {"time": 0.1667}], "translate": [{}, {"time": 0.0667, "x": -14.96, "y": -0.39}, {"time": 0.1667}]}, "zhu_42": {"rotate": [{}, {"time": 0.0667, "angle": -45.84}, {"time": 0.1667}]}, "zhu_43": {"rotate": [{}, {"time": 0.0667, "angle": 27.82}, {"time": 0.1667}]}, "t2": {"translate": [{}, {"time": 0.0667, "x": 30.44, "y": 60.84}, {"time": 0.1667}]}, "t1": {"translate": [{}, {"time": 0.0667, "x": 8.38, "y": 7.48}, {"time": 0.1667}]}, "cy": {"translate": [{"time": 0.0667, "x": 8.32, "y": 277.22}], "scale": [{"time": 0.0667, "x": 3.041, "y": 5.221}, {"time": 0.1667, "x": 3.243, "y": 5.05}]}}}, "move": {"bones": {"all2": {"translate": [{"x": 3.45, "y": -11.5}]}, "all3": {"rotate": [{"angle": 1.75, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 1.59, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": 0.48, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 1.75}], "translate": [{"x": -20.96, "y": -20.91, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": -2.48, "y": -3.72, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": -13.79, "y": -16.8, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "x": -1.19, "y": -3.72, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "x": -20.96, "y": -20.91}]}, "zhu_13": {"rotate": [{"angle": 1.4, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -2.52, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 2.53, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -0.64, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 1.4}]}, "zhu_0": {"rotate": [{"angle": 0.78, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -1.39, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 0.56, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -1.42, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 0.78}]}, "zhu_15": {"rotate": [{"angle": -8.32, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 4.51, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 11.18, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": -8.32}], "translate": [{"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": 4.62, "y": -0.46, "curve": 0.25, "c3": 0.75}, {"time": 0.7}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 0.929, "y": 0.929, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": 0.86, "y": 0.86, "curve": 0.25, "c3": 0.75}, {"time": 0.7}]}, "zhu_14": {"rotate": [{"angle": -4.18, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 2.85, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 10.8, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": -4.18}]}, "zhu_18": {"translate": [{"x": -38.53, "y": 11.86}]}, "zhu_2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -9.89, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -30.62, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -14.77, "curve": 0.25, "c3": 0.75}, {"time": 0.9333}]}, "zhu_1": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -8.85, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -12.99, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -8.82, "curve": 0.25, "c3": 0.75}, {"time": 0.9333}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": -2.46, "y": -5.09, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": -2.1, "y": -8.52, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "x": -2.61, "y": -7.21, "curve": 0.25, "c3": 0.75}, {"time": 0.9333}]}, "zhu_3": {"rotate": [{"angle": 11.01, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -5.34, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 11.01}]}, "zhu_4": {"rotate": [{"angle": 4.97, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -1.63, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 4.97}]}, "zhu_5": {"rotate": [{"angle": 17.79, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -3.01, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 17.79}]}, "zhu_6": {"rotate": [{"angle": 7.57, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -7.78, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 7.57}]}, "zhu_9": {"rotate": [{"angle": 0.61, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -1.52, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 1.32, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -0.97, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 0.61}]}, "zhu_10": {"rotate": [{"angle": 11.38, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 0.0333, "angle": 13.85, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -17.04, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 15.71, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": -18.23, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 0.9333, "angle": 11.38}]}, "zhu_11": {"rotate": [{"angle": 10.88, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.0667, "angle": 17.01, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -16.49, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 17.41, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -10.31, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 0.9333, "angle": 10.88}]}, "zhu_17": {"rotate": [{"angle": 0.13, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -0.66, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "curve": "stepped"}, {"time": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 0.13}]}, "zhu_19": {"rotate": [{"angle": 3.85, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.0667, "angle": 9.77, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -13.26, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 11.16, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -16.62, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 0.9333, "angle": 3.85}]}, "zhu_20": {"rotate": [{"angle": -9.01, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.1333, "angle": 13.85, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -23.71, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 10.61, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -24.6, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 0.9333, "angle": -9.01}]}, "zhu_21": {"rotate": [{"angle": -2.37, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 0.71, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -4.25, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": 2.67, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": -2.37}], "translate": [{"x": -15.1, "y": -24.39, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": -2.72, "y": -0.68, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": -17.54, "y": -12.92, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "x": -15.1, "y": -24.39}]}, "zhu_22": {"rotate": [{"angle": -2.65, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.0667, "angle": -5.55, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 8.91, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -7.3, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 7.4, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 0.9333, "angle": -2.65}]}, "zhu_23": {"rotate": [{"angle": 0.47, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.1333, "angle": -7.22, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 12.28, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -14.38, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 5.72, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 0.9333, "angle": 0.47}]}, "zhu_24": {"rotate": [{"angle": 5.63, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -3.36, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 8.76, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -3.74, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 5.63}], "translate": [{"x": -19.75, "y": -9.29, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": -17.04, "y": -10.03, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "x": -19.75, "y": -9.29}]}, "zhu_25": {"rotate": [{"angle": 3.67, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.1, "angle": 12.54, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 1.22, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 12.67, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": -9.32, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 0.9333, "angle": 3.67}]}, "zhu_26": {"rotate": [{"angle": -10.69, "curve": 0.303, "c2": 0.22, "c3": 0.756}, {"time": 0.2, "angle": 22.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -7.58, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 20.72, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -13.43, "curve": 0.295, "c3": 0.633, "c4": 0.37}, {"time": 0.9333, "angle": -10.69}]}, "zhu_27": {"rotate": [{"angle": -17.07, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 0.0333, "angle": -19.74, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 11.42, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -11.67, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": 14.8, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 0.9333, "angle": -17.07}]}, "zhu_28": {"rotate": [{"angle": -10.76, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.0667, "angle": -17.83, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 15.34, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -25.76, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 13.71, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 0.9333, "angle": -10.76}]}, "zhu_29": {"rotate": [{"angle": -3.15, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.1, "angle": -16.38, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 9.66, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -5.27, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": 16.23, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 0.9333, "angle": -3.15}]}, "zhu_30": {"rotate": [{"angle": -18.37, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.0667, "angle": -25.46, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 5.45, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -16.23, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 6.15, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 0.9333, "angle": -18.37}]}, "zhu_31": {"rotate": [{"angle": -4.14, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.1333, "angle": -21.57, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 9.19, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -21.01, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 7.76, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 0.9333, "angle": -4.14}]}, "zhu_32": {"rotate": [{"angle": 8.19, "curve": 0.303, "c2": 0.22, "c3": 0.756}, {"time": 0.2, "angle": -15.02, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 6.22, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -23.57, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": 10.13, "curve": 0.295, "c3": 0.633, "c4": 0.37}, {"time": 0.9333, "angle": 8.19}]}, "zhu_33": {"rotate": [{"angle": 19.56, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 0.0333, "angle": 20.52, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 32.14, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": 8.09, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 0.9333, "angle": 19.56}]}, "zhu_34": {"rotate": [{"angle": 13.03}], "translate": [{"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "x": 2.79, "y": -22.9, "curve": 0.25, "c3": 0.75}, {"time": 0.9333}]}, "zhu_35": {"rotate": [{"angle": -3.68}]}, "zhu_36": {"rotate": [{"angle": 7.3, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -15.86, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -8.36, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 7.3}], "translate": [{"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": -4.35, "y": -5.31, "curve": 0.25, "c3": 0.75}, {"time": 0.7}]}, "zhu_37": {"rotate": [{"angle": 16.89, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -10.47, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -1.82, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 16.89}]}, "zhu_38": {"rotate": [{"angle": 13.59, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -13.14, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": 0.49, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 13.59}]}, "zhu_39": {"rotate": [{"angle": -0.29}]}, "zhu_40": {"rotate": [{"angle": 0.01}]}, "zhu_41": {"rotate": [{"angle": -6.96, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 1.29, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 11.42, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -3.62, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": -6.96}], "translate": [{"x": -3.98, "y": -1.01, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": -0.76, "y": 1.31, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "x": -4.66, "y": -1.31, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "x": -3.98, "y": -1.01}]}, "zhu_42": {"rotate": [{"angle": -12.86, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -2.31, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 10.33, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -17.19, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": -12.86}]}, "zhu_43": {"rotate": [{"angle": -12.01, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -2.55, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 0.3667, "angle": -15.11, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.4667, "angle": 31.12, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -13.72, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": -12.01}]}, "t2": {"translate": [{"x": -169.8, "y": 3.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": -31.82, "y": -19.35, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": 69.07, "y": 34.61, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "x": -20.05, "y": 28.06, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "x": -169.8, "y": 3.1}]}, "t1": {"translate": [{"x": 87.62, "y": 34.32, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 73.83, "y": 58.8, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": -70.46, "y": -3.38, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "x": 2.72, "y": -34.49, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "x": 87.62, "y": 34.32}]}, "zhu_47": {"rotate": [{"angle": -2.59, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.1333, "angle": 1.99, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -5.72, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 2.02, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -5.72, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 0.9333, "angle": -2.59}]}, "zhu_48": {"rotate": [{"angle": -2.11, "curve": 0.303, "c2": 0.22, "c3": 0.756}, {"time": 0.2, "angle": 2.52, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -2.5, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 3.24, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -2.5, "curve": 0.295, "c3": 0.633, "c4": 0.37}, {"time": 0.9333, "angle": -2.11}]}, "zhu_49": {"rotate": [{"angle": -3.35, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 0.0333, "angle": -4.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 1.49, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -4.1, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": 5.65, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 0.9333, "angle": -3.35}]}, "zhu_50": {"rotate": [{"angle": -8.31, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.1, "angle": -8.87, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 7.31, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -8.87, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": -7.48, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 0.9333, "angle": -8.31}]}, "zhu_51": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 11.36, "y": 10.77, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "x": 14.75, "y": 13.1, "curve": 0.25, "c3": 0.75}, {"time": 0.9333}]}, "zhu_46": {"rotate": [{"angle": 1.61, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.0667, "angle": 3.24, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -4.01, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -4.01, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 0.9333, "angle": 1.61}]}}, "deform": {"default": {"zhu_2": {"zhu_2": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "offset": 14, "vertices": [2.51318, 2.45825, 3.11594, 2.70874], "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "offset": 8, "vertices": [-0.05084, 2.59601, -7.30551, 15.2621, 13.40567, 8.81631, 6.69751, 9.30309, 2.51102, 1.53568], "curve": 0.25, "c3": 0.75}, {"time": 0.7, "offset": 12, "vertices": [1.41867, -0.51331, 4.73117, 3.09769, 4.2785, 4.06311], "curve": 0.25, "c3": 0.75}, {"time": 0.9333}]}, "zhu_10": {"zhu_10": [{"offset": 114, "vertices": [3.93623, -0.70616, 3.59683, 4.57651, 4.39782, 3.8132], "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "offset": 114, "vertices": [4.64217, -0.25363, 1.39141, 6.88896, 1.0676, 6.94651], "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "offset": 6, "vertices": [-3.53197, 8.11424, 5.99088, 5.00641, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5.19042, 2.08102, -2.52807, 6.88666, -3.65781, 5.9121], "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "offset": 6, "vertices": [-1.62332, 7.8203, 4.06555, 6.20907, 0.11924, 8.1967, 4.67609, 5.89525, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5.49661, 3.3849, -4.71707, 6.88538, -6.29691, 5.33438], "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "offset": 114, "vertices": [5.56454, 3.67419, -5.20274, 6.88509, -6.88245, 5.20621], "curve": 0.25, "c3": 0.75}, {"time": 0.7, "offset": 114, "vertices": [4.164, -0.13804, 0.36157, 6.32376, 0.11917, 6.33294], "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "offset": 114, "vertices": [3.93623, -0.70616, 3.59683, 4.57651, 4.39782, 3.8132]}]}, "zhu_11": {"zhu_11": [{"offset": 92, "vertices": [-16.49786, 12.64808, -7.96593, 18.42603, -42.48177, 12.92279, -27.36988, 29.63355, -93.28041, -23.44144, -78.18552, 18.08585, 14.13853, -0.32331], "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "offset": 92, "vertices": [4.01372, 8.18497, 5.39713, 6.37046, -2.14105, 13.23394, 1.99478, 13.18753, -17.42937, 13.11647, -9.96217, 18.19151, 10.3346, -0.34422], "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "offset": 104, "vertices": [13.73391, 4.71052], "curve": 0.25, "c3": 0.75}, {"time": 0.7, "vertices": [-12.77933, -1.70423, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.88306, 3.94114, 3.34232, 0.06855, -9.38678, 45.53839, 34.80631, 19.83284, -62.79279, 45.96748, 24.75623, 71.84194, -93.46248, 0.26447, -17.97929, 90.90341, 12.81943, -1.00996], "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "offset": 92, "vertices": [-16.49786, 12.64808, -7.96593, 18.42603, -42.48177, 12.92279, -27.36988, 29.63355, -93.28041, -23.44144, -78.18552, 18.08585, 14.13853, -0.32331]}]}}}}}}