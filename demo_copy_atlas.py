#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Atlas文件复制脚本 - 演示版本
自动选择第一个atlas文件，不覆盖已存在的文件

需求：
1. 寻找后缀atlas文件，以他为基础
2. 复制多个该文件，数量取决于当前.json文件的数量，忽略已存在和atlas同名的json
3. 并分别根据每个对应json，重命名对应的复制出来的文件
"""

import os
import shutil
import sys
from pathlib import Path


def find_atlas_files(directory="."):
    """查找目录中的.atlas文件"""
    atlas_files = []
    for file in os.listdir(directory):
        if file.endswith('.atlas'):
            atlas_files.append(file)
    return sorted(atlas_files)  # 排序以确保一致性


def find_json_files(directory="."):
    """查找目录中的.json文件"""
    json_files = []
    for file in os.listdir(directory):
        if file.endswith('.json'):
            json_files.append(file)
    return sorted(json_files)  # 排序以确保一致性


def get_base_name(filename):
    """获取文件的基础名称（不包含扩展名）"""
    return os.path.splitext(filename)[0]


def demo_copy_atlas():
    """演示版本的复制函数"""
    print("=== Atlas文件复制脚本 - 演示版本 ===")
    print(f"Python版本: {sys.version}")
    print(f"工作目录: {os.getcwd()}")
    print()
    
    # 查找atlas文件
    atlas_files = find_atlas_files()
    if not atlas_files:
        print("错误: 未找到任何.atlas文件")
        return
    
    print(f"找到的atlas文件: {atlas_files}")
    
    # 使用第一个atlas文件作为基础
    base_atlas = atlas_files[0]
    print(f"使用基础atlas文件: {base_atlas}")
    
    # 查找json文件
    json_files = find_json_files()
    if not json_files:
        print("错误: 未找到任何.json文件")
        return
    
    print(f"找到的json文件: {json_files}")
    
    # 过滤掉与atlas同名的json文件
    base_atlas_name = get_base_name(base_atlas)
    filtered_json_files = [
        json_file for json_file in json_files 
        if get_base_name(json_file) != base_atlas_name
    ]
    
    print(f"过滤后的json文件: {filtered_json_files}")
    print(f"需要复制的数量: {len(filtered_json_files)}")
    print()
    
    # 执行复制操作
    copied_count = 0
    skipped_count = 0
    
    for json_file in filtered_json_files:
        json_base_name = get_base_name(json_file)
        new_atlas_name = f"{json_base_name}.atlas"
        
        try:
            # 检查目标文件是否已存在
            if os.path.exists(new_atlas_name):
                print(f"跳过: 文件 {new_atlas_name} 已存在")
                skipped_count += 1
                continue
                
            # 复制文件
            shutil.copy2(base_atlas, new_atlas_name)
            print(f"成功复制: {base_atlas} -> {new_atlas_name}")
            copied_count += 1
            
        except Exception as e:
            print(f"复制文件时出错: {base_atlas} -> {new_atlas_name}")
            print(f"错误信息: {e}")
    
    print()
    print(f"操作总结: 成功复制 {copied_count} 个文件，跳过 {skipped_count} 个文件")
    print("=== 操作完成 ===")


if __name__ == "__main__":
    demo_copy_atlas()
