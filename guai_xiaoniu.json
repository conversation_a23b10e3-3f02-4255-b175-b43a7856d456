{"skeleton": {"hash": "uEmc0ZAf1QzYkwgZw00FHtuXq0E", "spine": "3.8.99", "x": -71.4, "y": -10.7, "width": 127.15, "height": 155.6}, "bones": [{"name": "root"}, {"name": "all", "parent": "root", "scaleX": 0.7, "scaleY": 0.7}, {"name": "xiaoniu2", "parent": "all", "y": 84}, {"name": "xiaoniu4", "parent": "xiaoniu2", "length": 41.49, "rotation": 103.65, "x": -0.17, "y": 0.61}, {"name": "xiaoniu5", "parent": "xiaoniu4", "length": 38.87, "rotation": 17.09, "x": 41.49}, {"name": "xiaoniu1", "parent": "xiaoniu5", "length": 33.21, "rotation": 102.5, "x": 39.41, "y": 0.35}, {"name": "xiaoniu0", "parent": "xiaoniu5", "length": 60.54, "rotation": -157.23, "x": 26.8, "y": -32.69}, {"name": "xiaoniu3", "parent": "xiaoniu0", "length": 48.92, "rotation": -41.28, "x": 60.54}, {"name": "xiaoniu6", "parent": "xiaoniu3", "length": 14.35, "rotation": -1.83, "x": 48.92}, {"name": "xiaoniu7", "parent": "xiaoniu6", "length": 14.35, "rotation": -48.89, "x": 14.35}, {"name": "xiaoniu9", "parent": "xiaoniu5", "length": 43.49, "rotation": 172.25, "x": 38.62, "y": 39.02}, {"name": "xiaoniu10", "parent": "xiaoniu9", "length": 24.76, "rotation": -52.25, "x": 43.49}, {"name": "xiaoniu11", "parent": "xiaoniu10", "length": 19.72, "rotation": 0.44, "x": 24.76}, {"name": "xiaoniu8", "parent": "xiaoniu2", "x": -6, "y": -6}, {"name": "xiaoniu12", "parent": "xiaoniu8", "length": 17.95, "rotation": -80.58, "x": -15.81, "y": -7.33}, {"name": "xiaoniu13", "parent": "xiaoniu8", "length": 16.94, "rotation": -84.5, "x": 14.35, "y": -1.3}, {"name": "xiaoniu12b", "parent": "xiaoniu12", "length": 17.95, "x": 17.95}, {"name": "xiaoniu12c", "parent": "xiaoniu12b", "length": 17.95, "x": 17.95}, {"name": "xiaoniu13b", "parent": "xiaoniu13", "length": 16.94, "x": 16.94}, {"name": "xiaoniu13c", "parent": "xiaoniu13b", "length": 16.94, "x": 16.94}, {"name": "xiaoniu15", "parent": "xiaoniu2", "length": 40.45, "rotation": -121.46, "x": -19.23, "y": -6.47}, {"name": "xiaoniu16", "parent": "xiaoniu15", "length": 22.38, "rotation": 71.94, "x": 40.45}, {"name": "leg1", "parent": "all", "x": -25.74, "y": 26.04}, {"name": "xiaoniu17", "parent": "leg1", "length": 27.68, "rotation": -130.83, "x": 0.23, "y": 0.45}, {"name": "xiaoniu19", "parent": "xiaoniu2", "length": 51.12, "rotation": -70.28, "x": 18.22, "y": -4.65}, {"name": "xiaoniu20", "parent": "xiaoniu19", "length": 23.99, "rotation": -13.2, "x": 51.12}, {"name": "leg2", "parent": "all", "x": 37.37, "y": 8.38}, {"name": "xiaoniu21", "parent": "leg2", "length": 18.58, "rotation": -63.83}], "slots": [{"name": "xiaoniu6", "bone": "xiaoniu9", "attachment": "xiaoniu6"}, {"name": "xiaoniu5", "bone": "xiaoniu15", "attachment": "xiaoniu5"}, {"name": "xiaoniu4", "bone": "xiaoniu19", "attachment": "xiaoniu4"}, {"name": "xiaoniu3", "bone": "xiaoniu4", "attachment": "xiaoniu3"}, {"name": "xiaoniu2", "bone": "xiaoniu8", "attachment": "xiaoniu2"}, {"name": "xiaoniu1", "bone": "xiaoniu1", "attachment": "xiaoniu1"}, {"name": "xiaoniu0", "bone": "xiaoniu0", "attachment": "xiaoniu0"}], "ik": [{"name": "leg1", "bones": ["xiaoniu15", "xiaoniu16"], "target": "leg1"}, {"name": "leg2", "order": 1, "bones": ["xiaoniu19", "xiaoniu20"], "target": "leg2", "bendPositive": false}], "skins": [{"name": "default", "attachments": {"xiaoniu0": {"xiaoniu0": {"type": "mesh", "uvs": [0.52465, 0.07852, 0.83216, 0.14494, 0.83216, 0.29361, 0.9331, 0.47865, 0.91666, 0.76492, 0.89533, 0.81506, 0.88446, 0.86996, 0.82336, 0.94589, 0.67499, 1, 0.53306, 0.89473, 0.51478, 0.84636, 0.5448, 0.79799, 0.55786, 0.74962, 0.604, 0.73329, 0.46787, 0.56371, 0.4088, 0.47718, 0.25212, 0.47891, 0, 0.25394, 0, 0, 0.25236, 0, 0.14085, 0.17483, 0.40207, 0.30016, 0.65736, 0.42549, 0.71079, 0.59216, 0.76391, 0.76207, 0.77683, 0.82079, 0.78997, 0.87002, 0.74529, 0.90266], "triangles": [9, 10, 11, 27, 25, 26, 7, 26, 6, 27, 26, 7, 8, 27, 7, 27, 11, 12, 27, 9, 11, 8, 9, 27, 5, 24, 4, 25, 24, 5, 6, 25, 5, 26, 25, 6, 13, 25, 12, 25, 13, 24, 25, 27, 12, 22, 2, 3, 14, 15, 22, 23, 22, 3, 14, 22, 23, 13, 14, 23, 4, 24, 23, 13, 23, 24, 3, 4, 23, 20, 18, 19, 17, 18, 20, 0, 20, 19, 21, 20, 0, 0, 2, 21, 2, 0, 1, 22, 21, 2, 15, 21, 22, 21, 17, 20, 16, 21, 15, 16, 17, 21], "vertices": [1, 6, 21.39, 32.31, 1, 2, 6, 50.45, 42.15, 0.99961, 7, -35.39, 25.02, 0.00039, 2, 6, 62.91, 25.3, 0.8402, 7, -14.91, 20.58, 0.1598, 2, 6, 86.13, 10.02, 0.0345, 7, 12.62, 24.42, 0.9655, 2, 7, 51.74, 14.34, 0.39067, 8, 2.36, 14.42, 0.60933, 2, 7, 58.22, 10.86, 0.08962, 8, 8.95, 11.15, 0.91038, 3, 7, 65.57, 8.21, 0.00142, 8, 16.38, 8.74, 0.77691, 9, -5.25, 7.27, 0.22167, 2, 8, 25.86, 1.09, 0.00539, 9, 6.75, 9.39, 0.99461, 2, 8, 30.82, -14.15, 0.00031, 9, 21.49, 3.1, 0.99969, 3, 7, 61.9, -25.16, 0.06407, 8, 13.78, -24.73, 0.24591, 9, 18.26, -16.69, 0.69001, 3, 7, 54.87, -25.41, 0.13317, 8, 6.76, -25.21, 0.3199, 9, 14, -22.29, 0.54693, 3, 7, 48.81, -21.18, 0.29378, 8, 0.57, -21.17, 0.35498, 9, 6.89, -24.3, 0.35124, 3, 7, 42.41, -18.52, 0.51024, 8, -5.92, -18.72, 0.29625, 9, 0.78, -27.58, 0.19352, 3, 7, 41.09, -13.75, 0.72227, 8, -7.39, -13.99, 0.18405, 9, -3.75, -25.58, 0.09368, 2, 6, 57.73, -25.9, 0.17818, 7, 14.98, -21.32, 0.82182, 2, 6, 45.97, -19.43, 0.71819, 7, 1.87, -24.22, 0.28181, 2, 6, 34.14, -28.48, 0.96483, 7, -1.05, -38.81, 0.03517, 1, 6, -3.97, -17.22, 1, 1, 6, -25.27, 11.57, 1, 1, 6, -5.99, 25.83, 1, 1, 6, 0.15, -0.29, 1, 1, 6, 30.61, 0.26, 1, 2, 6, 60.62, 0.47, 0.51928, 7, -0.25, 0.41, 0.48072, 1, 7, 23.79, 0.39, 1, 2, 7, 48.27, 0.24, 0.69655, 8, -0.65, 0.22, 0.30345, 2, 8, 7.71, -0.07, 0.99822, 9, -4.31, -5.05, 0.00178, 2, 8, 14.76, -0.1, 0.38762, 9, 0.35, 0.25, 0.61238, 3, 7, 67.27, -5.69, 0.00011, 8, 18.52, -5.1, 0.00256, 9, 6.59, -0.21, 0.99733], "hull": 20}}, "xiaoniu1": {"xiaoniu1": {"x": 15.98, "y": -20.35, "rotation": 136.76, "width": 92, "height": 89}}, "xiaoniu2": {"xiaoniu2": {"type": "mesh", "uvs": [0.92613, 0.11721, 1, 0.25534, 0.8294, 0.35649, 0.66052, 0.38985, 0.68527, 0.46518, 0.71002, 0.55449, 0.7304, 0.63843, 0.74787, 0.73527, 0.66052, 0.71052, 0.59937, 0.70299, 0.57171, 0.74496, 0.58627, 0.79015, 0.44068, 0.85149, 0.32567, 0.98277, 0.26848, 0.97444, 0.22083, 0.83488, 0.16613, 0.74488, 0.07789, 0.72792, 0.06201, 0.6301, 0.06554, 0.54531, 0.07613, 0.45662, 0.07436, 0.36662, 0, 0.22575, 0, 0, 0.89039, 0, 0.2618, 0.7345, 0.22827, 0.54347, 0.17798, 0.35347, 0.19195, 0.40614, 0.24224, 0.63228, 0.28974, 0.83054, 0.65577, 0.56516, 0.65018, 0.46293, 0.62643, 0.37309, 0.62085, 0.28532, 0.67254, 0.65293, 0.5043, 0.20319], "triangles": [7, 8, 6, 8, 35, 6, 8, 9, 35, 35, 9, 31, 9, 29, 31, 35, 31, 6, 31, 5, 6, 26, 32, 31, 31, 4, 5, 31, 32, 4, 26, 33, 32, 34, 33, 36, 36, 33, 28, 32, 3, 4, 32, 33, 3, 2, 3, 34, 3, 33, 34, 1, 2, 0, 2, 34, 0, 34, 36, 0, 36, 24, 0, 12, 13, 30, 13, 14, 30, 14, 15, 30, 11, 12, 10, 15, 25, 30, 15, 16, 25, 10, 12, 25, 12, 30, 25, 9, 10, 29, 25, 16, 29, 29, 16, 18, 10, 25, 29, 16, 17, 18, 26, 18, 19, 18, 26, 29, 29, 26, 31, 19, 20, 26, 20, 28, 26, 28, 33, 26, 28, 21, 27, 28, 20, 21, 28, 27, 36, 21, 22, 27, 27, 22, 36, 22, 23, 36, 36, 23, 24], "vertices": [2, 13, 34.98, 14.22, 0.0608, 15, -13.47, 22.02, 0.9392, 2, 13, 40, 1.51, 0.00015, 15, -0.34, 25.8, 0.99985, 1, 15, 7.81, 13.36, 1, 1, 15, 9.77, 1.64, 1, 2, 15, 16.83, 2.65, 0.48244, 18, -0.11, 2.65, 0.51756, 5, 13, 20.28, -26.01, 0, 14, 24.34, 32.55, 0, 16, 6.38, 32.55, 1e-05, 17, -11.57, 32.55, 0, 18, 8.23, 3.54, 0.99998, 6, 13, 21.67, -33.74, 0, 14, 32.18, 32.65, 0, 16, 14.23, 32.65, 3e-05, 17, -3.72, 32.65, 0, 18, 16.05, 4.18, 0.69549, 19, -0.89, 4.18, 0.30448, 6, 13, 22.86, -42.65, 0, 14, 41.17, 32.37, 0, 16, 23.21, 32.37, 0.00019, 17, 5.26, 32.37, 0.00027, 18, 25.03, 4.5, 0.00013, 19, 8.1, 4.5, 0.9994, 4, 16, 20, 26.88, 0.00508, 17, 2.04, 26.88, 0.00718, 18, 22.2, -1.19, 0.00309, 19, 5.26, -1.19, 0.98464, 5, 14, 36.58, 22.89, 3e-05, 16, 18.63, 22.89, 0.0866, 17, 0.68, 22.89, 0.13491, 18, 21.11, -5.26, 0.16819, 19, 4.17, -5.26, 0.61027, 4, 16, 22.13, 20.4, 0.12554, 17, 4.18, 20.4, 0.378, 18, 24.77, -7.5, 0.09333, 19, 7.83, -7.5, 0.40313, 4, 16, 26.4, 20.7, 0.1034, 17, 8.44, 20.7, 0.50102, 18, 29, -6.92, 0.04367, 19, 12.07, -6.92, 0.35191, 4, 16, 30.34, 10.01, 0.02128, 17, 12.39, 10.01, 0.86121, 18, 33.67, -17.31, 0.00417, 19, 16.74, -17.31, 0.11333, 1, 17, 23.03, 0.32, 1, 1, 17, 21.63, -3.4, 1, 1, 17, 8.44, -4.49, 1, 2, 16, 17.61, -6.8, 0.71379, 17, -0.34, -6.8, 0.28621, 2, 16, 15.09, -12.47, 0.97707, 17, -2.86, -12.47, 0.02293, 2, 14, 23.98, -12.06, 0.08267, 16, 6.03, -12.06, 0.91733, 2, 14, 16.33, -10.55, 0.54491, 16, -1.62, -10.55, 0.45509, 2, 14, 8.4, -8.5, 0.97642, 16, -9.55, -8.5, 0.02358, 2, 13, -22.94, -8.73, 0.00998, 14, 0.21, -7.26, 0.99002, 2, 13, -28, 4.23, 0.31699, 14, -13.4, -10.13, 0.68301, 2, 13, -28, 25, 0.58522, 14, -33.89, -6.73, 0.41478, 2, 13, 32.55, 25, 0.12797, 15, -24.44, 20.64, 0.87203, 2, 16, 17.73, -0.23, 0.5942, 17, -0.22, -0.23, 0.4058, 6, 13, -12.48, -25, 0.00047, 14, 17.97, 0.4, 0.49098, 16, 0.02, 0.4, 0.50567, 15, 21.02, -28.97, 0.00067, 18, 4.08, -28.97, 0.00215, 19, -12.86, -28.97, 7e-05, 1, 14, 0.17, -0.11, 1, 4, 13, -14.95, -12.36, 6e-05, 14, 5.1, 0.03, 0.99992, 15, 8.2, -30.22, 1e-05, 18, -8.73, -30.22, 1e-05, 4, 16, 8.24, 0, 0.99952, 15, 29.24, -28.81, 1e-05, 18, 12.3, -28.81, 0.00036, 19, -4.63, -28.81, 0.00011, 3, 17, 8.81, 0.2, 0.99934, 18, 30.77, -27.35, 2e-05, 19, 13.83, -27.35, 0.00064, 5, 13, 16.59, -26.99, 5e-05, 14, 24.7, 28.75, 0.00029, 16, 6.75, 28.75, 0.00132, 17, -11.2, 28.75, 0.00013, 18, 8.85, -0.23, 0.9982, 2, 15, 16.39, 0.29, 0.71232, 18, -0.54, 0.29, 0.28768, 4, 13, 14.6, -9.32, 0.00459, 14, 6.94, 29.67, 0.00109, 16, -11.01, 29.67, 0.00014, 15, 8.01, -0.52, 0.99418, 3, 13, 14.22, -1.25, 0.00332, 14, -1.09, 30.62, 0, 15, -0.06, -0.12, 0.99668, 3, 13, 17.73, -35.07, 0, 18, 17, 0.13, 0.47521, 19, 0.06, 0.13, 0.52479, 3, 13, 6.29, 6.31, 0.64095, 14, -9.84, 24.04, 0.00109, 15, -8.35, -7.29, 0.35796], "hull": 25}}, "xiaoniu3": {"xiaoniu3": {"type": "mesh", "uvs": [0.66856, 0.10562, 0.76142, 0.11007, 0.88524, 0.17239, 1, 0.43722, 1, 0.65309, 0.90684, 0.93987, 0.66031, 1, 0.54268, 1, 0.31774, 0.92905, 0.22901, 0.78662, 0.11138, 0.6887, 0, 0.5841, 0, 0, 0.44569, 0, 0.38584, 0.24583, 0.48283, 0.40606, 0.56951, 0.5752, 0.61903, 0.79997], "triangles": [14, 12, 13, 14, 13, 0, 15, 14, 0, 15, 2, 16, 11, 12, 14, 10, 11, 14, 16, 2, 3, 15, 10, 14, 9, 10, 15, 16, 9, 15, 1, 15, 0, 2, 15, 1, 4, 16, 3, 17, 16, 4, 9, 16, 17, 8, 9, 17, 5, 17, 4, 7, 8, 17, 6, 7, 17, 5, 6, 17], "vertices": [2, 3, 85.95, -21.61, 0.05859, 4, 36.14, -33.72, 0.94141, 2, 3, 83.09, -31.43, 0.13019, 4, 30.52, -42.27, 0.86981, 2, 3, 73.7, -43.16, 0.24561, 4, 18.1, -50.73, 0.75439, 2, 3, 44.47, -49.06, 0.64744, 4, -11.57, -47.76, 0.35256, 2, 3, 23.08, -43.86, 0.90903, 4, -30.49, -36.51, 0.09097, 1, 3, -2.93, -27, 1, 2, 3, -2.49, 0.8, 0.99976, 4, -41.8, 13.7, 0.00024, 2, 3, 0.56, 13.38, 0.96668, 4, -35.19, 24.82, 0.03332, 2, 3, 13.43, 35.71, 0.6976, 4, -16.32, 42.38, 0.3024, 2, 3, 29.86, 41.77, 0.39232, 4, 1.16, 43.34, 0.60768, 2, 3, 42.62, 51.99, 0.13961, 4, 16.36, 49.36, 0.86039, 2, 3, 55.87, 61.37, 0.04753, 4, 31.79, 54.43, 0.95247, 1, 4, 82.99, 23.98, 1, 1, 4, 57.93, -18.16, 1, 1, 4, 39.74, 0.32, 1, 1, 4, 20.24, -0.5, 1, 2, 3, 41.97, 0.28, 0.31296, 4, 0.54, 0.13, 0.68704, 2, 3, 18.41, 0.4, 0.99906, 4, -21.95, 7.17, 0.00094], "hull": 14}}, "xiaoniu4": {"xiaoniu4": {"type": "mesh", "uvs": [0.83142, 0.19702, 0.91878, 0.38041, 0.87025, 0.55795, 0.8387, 0.63404, 0.8484, 0.71988, 1, 0.90327, 1, 1, 0.77415, 1, 0.64098, 0.94905, 0.63622, 0.84199, 0.59976, 0.80503, 0.56488, 0.74003, 0.55378, 0.65846, 0.43646, 0.64954, 0.25415, 0.67885, 0, 0.57817, 0, 0, 0.57417, 0, 0.48093, 0.06563, 0.58305, 0.29322, 0.68518, 0.53847, 0.71914, 0.77053, 0.70616, 0.65611, 0.77323, 0.84484, 0.49429, 0.26134, 0.43418, 0.48893, 0.51368, 0.55284, 0.57766, 0.63234, 0.43612, 0.37358, 0.52919, 0.16937, 0.56215, 0.0696, 0.28293, 0.56219, 0.32947, 0.41566, 0.39346, 0.25043, 0.41091, 0.1226], "triangles": [9, 10, 21, 23, 21, 4, 9, 21, 23, 23, 4, 5, 8, 9, 23, 7, 23, 5, 8, 23, 7, 7, 5, 6, 27, 26, 20, 3, 20, 2, 12, 13, 26, 22, 20, 3, 27, 20, 22, 27, 12, 26, 22, 3, 4, 22, 11, 12, 22, 12, 27, 21, 22, 4, 11, 22, 21, 10, 11, 21, 18, 16, 17, 30, 18, 17, 34, 16, 18, 29, 18, 30, 34, 18, 29, 30, 17, 0, 29, 30, 0, 33, 16, 34, 33, 34, 29, 24, 33, 29, 19, 29, 0, 24, 29, 19, 28, 33, 24, 28, 24, 19, 19, 0, 1, 32, 16, 33, 32, 33, 28, 25, 32, 28, 20, 19, 1, 28, 19, 20, 26, 25, 28, 20, 26, 28, 2, 20, 1, 32, 15, 16, 31, 32, 25, 31, 15, 32, 13, 25, 26, 31, 25, 13, 14, 15, 31, 14, 31, 13], "vertices": [1, 24, 22.43, 22.72, 1, 2, 24, 42.45, 23.15, 0.93911, 25, -13.73, 20.56, 0.06089, 2, 24, 58.16, 13.29, 0.32138, 25, 3.81, 14.55, 0.67862, 3, 24, 64.59, 8.24, 0.03811, 25, 11.23, 11.1, 0.94505, 27, -8.1, 14.84, 0.01683, 2, 25, 20.02, 10.89, 0.63053, 27, 0.06, 11.59, 0.36947, 1, 27, 22.37, 14.23, 1, 1, 27, 31.17, 9.76, 1, 1, 27, 22.8, -6.75, 1, 1, 27, 13.23, -14.14, 1, 2, 25, 30.42, -7.81, 0.15305, 27, 3.31, -9.56, 0.84695, 2, 25, 26.33, -10.35, 0.51473, 27, -1.41, -10.52, 0.48527, 3, 24, 67.19, -16.55, 0.01122, 25, 19.42, -12.44, 0.87655, 27, -8.61, -10.07, 0.11223, 3, 24, 59.05, -14.59, 0.19514, 25, 11.05, -12.4, 0.80459, 27, -16.45, -7.12, 0.00027, 2, 24, 54.95, -23.34, 0.5789, 25, 9.05, -21.85, 0.4211, 2, 24, 52.72, -38.43, 0.77336, 25, 10.33, -37.05, 0.22664, 2, 24, 36.02, -54.58, 0.87804, 25, -2.24, -56.59, 0.12196, 1, 24, -19.5, -34.68, 1, 1, 24, -3.61, 9.64, 1, 1, 24, 0.11, 0.19, 1, 1, 24, 24.79, 0.24, 1, 2, 24, 51.16, -0.32, 0.465, 25, 0.11, -0.3, 0.535, 2, 25, 23.95, -0.22, 0.52874, 27, -0.12, -0.2, 0.47126, 2, 25, 12.23, 0.04, 0.99999, 27, -11.01, 4.13, 1e-05, 2, 25, 31.98, 3.32, 0.00069, 27, 8.65, 0.33, 0.99931, 2, 24, 19.27, -5.52, 0.99886, 25, -29.75, -12.65, 0.00114, 2, 24, 39.46, -17.99, 0.82933, 25, -7.24, -20.18, 0.17067, 2, 24, 47.8, -14.05, 0.61739, 25, -0.03, -14.44, 0.38261, 2, 24, 57.2, -11.85, 0.19416, 25, 8.63, -10.15, 0.80584, 2, 24, 28.44, -13.87, 0.96481, 25, -18.92, -18.69, 0.03519, 1, 24, 11.41, 0.34, 1, 1, 24, 2.74, 6.32, 1, 2, 24, 42.31, -32.19, 0.82529, 25, -1.23, -33.35, 0.17471, 2, 24, 29.53, -23.55, 0.94527, 25, -15.64, -27.86, 0.05473, 2, 24, 15.43, -12.93, 0.98961, 25, -31.79, -20.73, 0.01039, 2, 24, 3.64, -7.18, 0.99649, 25, -44.58, -17.83, 0.00351], "hull": 18}}, "xiaoniu5": {"xiaoniu5": {"type": "mesh", "uvs": [0.89024, 0.04823, 1, 0.53062, 0.88879, 0.7007, 0.91641, 0.7577, 0.74069, 0.8462, 0.67041, 0.9512, 0.36165, 1, 0, 1, 0, 0.95355, 0.0634, 0.88265, 0.26532, 0.77692, 0.48389, 0.6886, 0.46932, 0.66746, 0.32153, 0.65626, 0.10296, 0.60526, 0.04883, 0.4846, 0.0863, 0.35773, 0.28057, 0.20127, 0.61496, 0, 0.77642, 0, 0.68595, 0.69173, 0.52269, 0.80125, 0.37983, 0.49028, 0.60636, 0.26589, 0.52473, 0.58784], "triangles": [6, 8, 9, 9, 10, 6, 6, 21, 5, 6, 10, 21, 6, 7, 8, 5, 21, 4, 21, 20, 4, 3, 4, 20, 10, 11, 21, 21, 11, 20, 2, 20, 1, 20, 24, 1, 3, 20, 2, 20, 12, 24, 20, 11, 12, 12, 13, 24, 13, 22, 24, 13, 14, 22, 14, 15, 22, 24, 22, 23, 1, 24, 23, 23, 0, 1, 15, 16, 22, 16, 17, 22, 22, 17, 23, 0, 23, 19, 23, 18, 19, 23, 17, 18], "vertices": [2, 20, -3.3, 2.5, 0.99997, 23, -46.38, -26.8, 3e-05, 2, 20, 27.63, 27.73, 0.27397, 23, -20.06, 3.21, 0.72603, 3, 20, 42.37, 30.36, 0.02556, 21, 29.46, 7.59, 0.4929, 23, -5.96, 8.25, 0.48154, 3, 20, 45.65, 33.95, 0.00296, 21, 33.89, 5.58, 0.84492, 23, -3.32, 12.34, 0.15212, 2, 21, 33.83, -5.68, 0.36604, 23, 7.8, 10.6, 0.63396, 2, 21, 38.14, -13.89, 0.06375, 23, 16.56, 13.66, 0.93625, 1, 23, 29.51, 4.86, 1, 1, 23, 41.13, -8.51, 1, 1, 23, 38.26, -11.01, 1, 1, 23, 31.83, -12.48, 1, 2, 21, 14.38, -19.71, 0.0012, 23, 18.8, -10.7, 0.9988, 2, 21, 15.83, -6.86, 0.51366, 23, 6.31, -7.37, 0.48634, 2, 21, 14.04, -6.28, 0.82286, 23, 5.47, -9.05, 0.17714, 2, 21, 8.65, -11.19, 0.99939, 23, 9.53, -15.11, 0.00061, 2, 20, 55.79, -6.57, 0.07604, 21, -1.49, -16.62, 0.92396, 2, 20, 48.74, -13.99, 0.4331, 21, -10.74, -12.22, 0.5669, 2, 20, 38.9, -17.86, 0.85409, 21, -17.46, -4.07, 0.14591, 1, 20, 22.99, -16.43, 1, 1, 20, 0.36, -11.07, 1, 1, 20, -3.77, -4.32, 1, 2, 21, 22.45, 0.5, 0.24625, 23, 0, 0.27, 0.75375, 2, 21, 24.09, -11.41, 0.00011, 23, 12.03, 0.12, 0.99989, 2, 20, 40.67, 0.08, 0.40338, 21, 0.15, -0.18, 0.59662, 1, 20, 19.18, -0.05, 1, 3, 20, 43.79, 10.31, 0.00033, 21, 10.84, 0.02, 0.99919, 23, -1.24, -11.28, 0.00048], "hull": 20}}, "xiaoniu6": {"xiaoniu6": {"type": "mesh", "uvs": [0.89031, 0.23557, 1, 0.48253, 1, 0.56601, 0.86397, 0.67161, 0.6725, 0.75507, 0.63299, 0.79425, 0.69985, 0.89304, 0.49927, 1, 0.39593, 1, 0.13152, 0.96799, 0, 0.88793, 0, 0.7159, 0.24397, 0.62051, 0.26221, 0.52683, 0.40505, 0.44167, 0.31083, 0.26793, 0.16191, 0.10952, 0.23485, 0, 0.39625, 0, 0.5023, 0.71419, 0.61171, 0.59837, 0.68466, 0.46892, 0.55397, 0.2526, 0.39289, 0.04139, 0.38985, 0.8215], "triangles": [6, 7, 24, 9, 24, 8, 6, 24, 5, 24, 7, 8, 9, 10, 24, 10, 11, 24, 11, 12, 24, 24, 19, 5, 24, 12, 19, 5, 19, 4, 19, 20, 4, 4, 20, 3, 19, 12, 20, 3, 20, 2, 12, 13, 20, 13, 14, 20, 20, 21, 2, 20, 14, 21, 21, 1, 2, 21, 0, 1, 14, 22, 21, 21, 22, 0, 14, 15, 22, 22, 15, 23, 15, 16, 23, 22, 23, 0, 23, 18, 0, 16, 17, 23, 23, 17, 18], "vertices": [1, 10, 26.24, 16.73, 1, 2, 10, 49.11, 13.1, 0.57373, 11, -6.92, 12.46, 0.42627, 2, 10, 56.1, 10.13, 0.22919, 11, -0.29, 16.18, 0.77081, 3, 10, 62.24, -0.01, 0.00513, 11, 11.49, 14.82, 0.99402, 12, -13.16, 14.92, 0.00084, 2, 11, 22.88, 10.01, 0.65549, 12, -1.8, 10.02, 0.34451, 2, 11, 26.98, 9.99, 0.22333, 12, 2.3, 9.98, 0.77667, 2, 11, 33.16, 17.36, 0.00088, 12, 8.53, 17.3, 0.99912, 1, 12, 21.99, 13.02, 1, 1, 12, 24.53, 8.41, 1, 1, 12, 28.48, -4.81, 1, 1, 12, 25.33, -14.2, 1, 2, 11, 36.53, -21.66, 0.03884, 12, 11.61, -21.75, 0.96116, 3, 10, 45.6, -27.3, 0.03103, 11, 22.88, -15.04, 0.55836, 12, -1.99, -15.03, 0.41062, 3, 10, 38.12, -23.11, 0.15486, 11, 14.99, -18.4, 0.77544, 12, -9.91, -18.32, 0.0697, 3, 10, 33.83, -13.38, 0.60971, 11, 4.66, -15.83, 0.38989, 12, -20.21, -15.67, 0.00041, 2, 10, 17.4, -11.62, 0.9984, 11, -6.78, -27.74, 0.0016, 1, 10, 1.16, -12.98, 1, 1, 10, -6.56, -5.66, 1, 1, 10, -3.34, 1.91, 1, 2, 11, 23.88, 0.62, 0.90269, 12, -0.87, 0.63, 0.09731, 1, 11, 11.96, 0.34, 1, 2, 10, 41.69, -1.22, 0.90916, 11, -0.14, -2.17, 0.09084, 1, 10, 20.96, 0.34, 1, 1, 10, 0.06, 0.28, 1, 1, 12, 10.45, 0.31, 1], "hull": 19}}}}], "animations": {"idle": {"bones": {"xiaoniu2": {"translate": [{"y": -2, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "y": 1.11, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "y": -2}]}, "xiaoniu4": {"rotate": [{"angle": 0.61, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -0.97, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 0.61}], "translate": [{"y": -0.45, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "y": 1.52, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.3333, "y": -0.45}]}, "xiaoniu5": {"rotate": [{"angle": 0.75, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1333, "angle": 1.22, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": -2.41, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.3333, "angle": 0.75}], "translate": [{"x": -0.32, "y": 0.08, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1333, "x": -0.43, "y": 0.11, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "x": 0.44, "y": -0.1, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.3333, "x": -0.32, "y": 0.08}]}, "xiaoniu1": {"rotate": [{"angle": 0.06, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2667, "angle": 2.33, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": -3.85, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.3333, "angle": 0.06}]}, "xiaoniu0": {"rotate": [{"angle": -1.85, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.1667, "angle": -3.19, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 4.08, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1.3333, "angle": -1.85}]}, "xiaoniu3": {"rotate": [{"angle": 0.07, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "angle": -3.9, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 4.04, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "angle": 0.07}]}, "xiaoniu6": {"rotate": [{"angle": 6.76, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.5, "angle": -8.48, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 10.21, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 1.3333, "angle": 6.76}]}, "xiaoniu7": {"rotate": [{"angle": 18.18, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -11.38, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 18.18}]}, "xiaoniu9": {"rotate": [{"angle": -0.39, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "angle": 3.08, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -3.87, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "angle": -0.39}]}, "xiaoniu10": {"rotate": [{"angle": -5.26, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.5, "angle": 6.02, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -7.82, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 1.3333, "angle": -5.26}]}, "xiaoniu11": {"rotate": [{"angle": -12.41, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 8.99, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -12.41}]}, "xiaoniu8": {"translate": [{"y": -0.42, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "y": 1.26, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "y": -0.42}]}, "xiaoniu12": {"rotate": [{"angle": -0.84, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2667, "angle": 0.46, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": -3.09, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.3333, "angle": -0.84}]}, "xiaoniu13": {"rotate": [{"angle": 0.43, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": 3.29, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.3333, "angle": 0.43}]}, "xiaoniu12b": {"rotate": [{"angle": -11.1, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.5333, "angle": 5.8, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": -13.63, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.3333, "angle": -11.1}]}, "xiaoniu12c": {"rotate": [{"angle": -17.29, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1333, "angle": -21.01, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": 7.61, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.3333, "angle": -17.29}]}, "xiaoniu13b": {"rotate": [{"angle": 0.24, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.4, "angle": -10.03, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": 6.22, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.3333, "angle": 0.24}]}, "xiaoniu13c": {"rotate": [{"angle": 8.71, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -12.2, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 8.71}]}}}, "move": {"bones": {"xiaoniu2": {"translate": [{"y": -0.22}, {"time": 0.1333, "x": -1.43, "y": -3.1}, {"time": 0.2667, "x": -2.86, "y": 0.49}, {"time": 0.4, "x": -5.71, "y": 4.06}, {"time": 0.5333, "y": -0.22}, {"time": 0.6667, "x": -1.43, "y": -3.08}, {"time": 0.8, "x": -2.86, "y": 0.49}, {"time": 0.9333, "x": -5.71, "y": 4.06}, {"time": 1.0667, "y": -0.22}]}, "xiaoniu4": {"rotate": [{"angle": 0.61, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -0.97, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": 0.61}], "translate": [{"y": -0.45, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "y": 1.52, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "y": -0.45}]}, "xiaoniu5": {"rotate": [{"angle": 0.75, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1, "angle": 1.22, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -2.41, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.0667, "angle": 0.75}], "translate": [{"x": -0.32, "y": 0.08, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1, "x": -0.43, "y": 0.11, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": 0.44, "y": -0.1, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.0667, "x": -0.32, "y": 0.08}]}, "xiaoniu1": {"rotate": [{"angle": 0.06, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2333, "angle": 2.33, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -3.85, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.0667, "angle": 0.06}]}, "xiaoniu0": {"rotate": [{"angle": -61.76, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 3.7, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": -61.76}]}, "xiaoniu3": {"rotate": [{"angle": 1.22}, {"time": 0.5333, "angle": 3.13}, {"time": 0.6667, "angle": 12.6}, {"time": 1.0667, "angle": 1.22}]}, "xiaoniu6": {"rotate": [{"angle": 6.76}, {"time": 0.5333, "angle": 9.79}, {"time": 0.6667, "angle": 18.53}, {"time": 0.8, "angle": 13.12}, {"time": 0.9333, "angle": 17.07}, {"time": 1.0667, "angle": 6.76}]}, "xiaoniu7": {"rotate": [{"angle": 18.18}, {"time": 0.5333, "angle": 22.01}, {"time": 0.6667, "angle": 32.34}, {"time": 0.8, "angle": 27.6}, {"time": 0.9333, "angle": 28.49}, {"time": 1.0667, "angle": 18.18}]}, "xiaoniu9": {"rotate": [{"angle": 36.51, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -10.77, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": 36.51}]}, "xiaoniu10": {"rotate": [{"angle": 18.4}, {"time": 0.5333, "angle": 12.17}, {"time": 1.0667, "angle": 18.4}]}, "xiaoniu11": {"rotate": [{"angle": -12.41}, {"time": 0.5333}, {"time": 0.6667, "angle": -10.81}, {"time": 0.8, "angle": 3.2}, {"time": 0.9333, "angle": -3.2}, {"time": 1.0667, "angle": -12.41}]}, "xiaoniu8": {"translate": [{"y": -0.42}, {"time": 0.5333}, {"time": 0.9333, "y": -0.42}]}, "xiaoniu12": {"rotate": [{"angle": -0.84, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2333, "angle": 4.15, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 0.61, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.0667, "angle": -0.84}]}, "xiaoniu13": {"rotate": [{"angle": 0.43, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 3.29, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.0667, "angle": 0.43}]}, "xiaoniu12b": {"rotate": [{"angle": -11.1, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.4333, "angle": 0.39, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": -8.47, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.0667, "angle": -11.1}]}, "xiaoniu12c": {"rotate": [{"angle": -17.29, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1, "angle": -29.33, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 15.18, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.0667, "angle": -17.29}]}, "xiaoniu13b": {"rotate": [{"angle": 0.24, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.3, "angle": -10.03, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 11.16, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.0667, "angle": 0.24}]}, "xiaoniu13c": {"rotate": [{"angle": 8.71, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -16.51, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": 8.71}]}, "xiaoniu15": {"translate": [{}, {"time": 0.4, "x": 9.12, "y": -7.67}, {"time": 0.5333, "x": 0.26, "y": -4.79}, {"time": 0.8, "x": 2.07, "y": 5.43}, {"time": 0.9333, "x": 1.04, "y": 5.95}, {"time": 1.0667}]}, "xiaoniu19": {"translate": [{}, {"time": 0.1333, "x": -9.12}, {"time": 0.2667, "x": -15.96}, {"time": 0.4, "x": -7.98, "y": 4.37}, {"time": 0.5333, "y": -1.09}, {"time": 0.6667}]}, "leg1": {"rotate": [{"angle": -16.67}, {"time": 0.1333, "angle": -7.41}, {"time": 0.2667, "angle": -3.67}, {"time": 0.4, "angle": 7.23}, {"time": 0.5333, "angle": 27.23}, {"time": 0.6667, "angle": 55.64}, {"time": 0.8, "angle": 60.91}, {"time": 0.9333, "angle": 29.76}, {"time": 1.0667, "angle": -16.67}], "translate": [{"x": -23.36, "y": -3.49}, {"time": 0.1333, "x": -6.02, "y": -9.57}, {"time": 0.2667, "x": 23.91, "y": -12.59}, {"time": 0.4, "x": 33.97, "y": -8.93}, {"time": 0.5333, "x": 48.35, "y": 2.75}, {"time": 0.6667, "x": 40.75, "y": 8.16}, {"time": 0.8, "x": 28.09, "y": 16.81}, {"time": 0.9333, "x": 6.46, "y": 10.51}, {"time": 1.0667, "x": -23.36, "y": -3.49}]}, "leg2": {"rotate": [{}, {"time": 0.1333, "angle": -0.64}, {"time": 0.2667, "angle": 12.65}, {"time": 0.4, "angle": 8.17}, {"time": 0.5333, "angle": -9.19}, {"time": 0.6667}], "translate": [{"x": 6.18, "y": 7.11}, {"time": 0.1333, "x": 5.59, "y": 12.05}, {"time": 0.2667, "x": -8.69, "y": 17.54}, {"time": 0.4, "x": -34.32, "y": 17.77}, {"time": 0.5333, "x": -45.55, "y": 1.02}, {"time": 0.6667, "x": -42.2, "y": -0.97}, {"time": 0.8, "x": -27.02, "y": -2.96}, {"time": 0.9333, "x": -8.69, "y": 4.98}, {"time": 1.0667, "x": 6.18, "y": 7.11}]}}, "deform": {"default": {"xiaoniu3": {"xiaoniu3": [{"offset": 30, "vertices": [-5.19062, -2.8489, -5.81384, -1.12111], "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "offset": 30, "vertices": [-9.667, -3.02481, -10.18257, -0.12982], "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "offset": 30, "vertices": [-5.19062, -2.8489, -5.81384, -1.12111]}]}, "xiaoniu4": {"xiaoniu4": [{"time": 0.2667}, {"time": 0.5333, "offset": 56, "vertices": [-3.1925, 15.8357, -7.99197, 14.03887], "curve": "stepped"}, {"time": 0.8, "offset": 56, "vertices": [-3.1925, 15.8357, -7.99197, 14.03887]}, {"time": 0.9333}]}}}}}}