{"skeleton": {"hash": "p4MG2yQobD3AfdhksYKuJuH50w0", "spine": "3.8.99", "x": -69.37, "y": -23.21, "width": 144.65, "height": 216.73}, "bones": [{"name": "root"}, {"name": "all", "parent": "root", "rotation": -1.03, "scaleX": -0.233, "scaleY": 0.233}, {"name": "zong", "parent": "all", "x": 21.89, "y": 481.76}, {"name": "tie_5", "parent": "zong", "length": 68.1, "rotation": 88.16, "x": 8.63, "y": -26.58}, {"name": "tie_13", "parent": "tie_5", "length": 142.27, "rotation": 11.86, "x": 68.15, "y": 0.35}, {"name": "tie_14", "parent": "tie_13", "length": 38.21, "rotation": -8.02, "x": 143.64, "y": -0.99}, {"name": "tie_12", "parent": "tie_14", "length": 103.43, "rotation": -2.33, "x": 28.28, "y": -0.01}, {"name": "tie_15", "parent": "tie_12", "length": 39.51, "rotation": 51.32, "x": 70.04, "y": 44.04}, {"name": "tie_16", "parent": "tie_15", "length": 49.24, "rotation": -21.07, "x": 39.51}, {"name": "tie_17", "parent": "tie_12", "length": 33.23, "rotation": -54.08, "x": 67.76, "y": -41.01}, {"name": "tie_18", "parent": "tie_17", "length": 42.61, "rotation": 25.07, "x": 33.23}, {"name": "tie_19", "parent": "tie_12", "length": 19.07, "rotation": 176.38, "x": 14.65, "y": 36.01}, {"name": "tie_20", "parent": "tie_12", "length": 19.11, "rotation": -178.42, "x": 10.53, "y": -32.55}, {"name": "tie_21", "parent": "tie_12", "x": 25.07, "y": -4.75}, {"name": "tie_4", "parent": "tie_13", "length": 136.68, "rotation": 158.92, "x": 132.09, "y": 78.37}, {"name": "tie_6", "parent": "tie_4", "length": 75.6, "rotation": -82.52, "x": 136.68}, {"name": "tie_3", "parent": "tie_6", "length": 26.32, "rotation": -9.08, "x": 76.71, "y": -0.98}, {"name": "tie_7", "parent": "tie_3", "length": 10.52, "rotation": -18.47, "x": 35.9, "y": -10.86}, {"name": "tie_8", "parent": "tie_7", "length": 16.71, "rotation": 18.78, "x": 10.52}, {"name": "tie_9", "parent": "tie_3", "length": 13.26, "rotation": 11.18, "x": 32.58, "y": -1.5}, {"name": "tie_10", "parent": "tie_9", "length": 14.83, "rotation": 29.5, "x": 13.26}, {"name": "tie_11", "parent": "tie_3", "length": 12.8, "rotation": 52.26, "x": 26.06, "y": 5.05}, {"name": "tie_22", "parent": "tie_11", "length": 15.76, "rotation": 34.1, "x": 12.8}, {"name": "tie_23", "parent": "tie_6", "length": 33.23, "rotation": 81.89, "x": 46, "y": 28.81}, {"name": "tie_24", "parent": "tie_23", "length": 30.2, "rotation": 3.86, "x": 33.23}, {"name": "tie_25", "parent": "tie_24", "length": 28.89, "rotation": 3.37, "x": 29.83, "y": -0.05}, {"name": "tie_26", "parent": "tie_25", "length": 28.92, "rotation": -0.74, "x": 28.89}, {"name": "tie_27", "parent": "tie_13", "x": 64.51, "y": 20.47}, {"name": "tie_28", "parent": "tie_13", "x": 56.84, "y": -45.62}, {"name": "tie_30", "parent": "tie_13", "length": 147.17, "rotation": -166.9, "x": 115.23, "y": -49.38}, {"name": "tie_2", "parent": "tie_30", "length": 69.24, "rotation": -87.24, "x": 157.66, "y": 9.66}, {"name": "tie_0", "parent": "tie_2", "length": 26.28, "rotation": -36.01, "x": 81.49, "y": 0.56}, {"name": "tie_1", "parent": "tie_0", "length": 19.77, "rotation": 28.28, "x": 26.28}, {"name": "tie_29", "parent": "tie_1", "length": 20.52, "rotation": 36.01, "x": 19.77}, {"name": "tie_31", "parent": "tie_2", "length": 27.21, "rotation": 48.97, "x": 73.68, "y": 31.43}, {"name": "tie_32", "parent": "tie_31", "length": 27.51, "rotation": 0.17, "x": 27.21}, {"name": "tie_33", "parent": "tie_32", "length": 28.06, "rotation": 7.38, "x": 27.29, "y": -0.38}, {"name": "tie_34", "parent": "tie_33", "length": 30.02, "rotation": -1.29, "x": 28.06}, {"name": "tie_36", "parent": "tie_0", "length": 92.11, "rotation": -124.29, "x": 28.66, "y": 7.76, "scaleX": 1.2163}, {"name": "zong2", "parent": "zong", "length": 242.56, "rotation": -91.34, "x": -1.98, "y": -14.41}, {"name": "zong3", "parent": "zong2", "length": 309.87, "rotation": 0.14, "x": 245.12, "y": 0.06}, {"name": "tie_35", "parent": "zong2", "length": 49.9, "rotation": -29.16, "x": -16.18, "y": -48.95}, {"name": "tie_37", "parent": "tie_35", "length": 54.06, "rotation": 6.73, "x": 49.9}, {"name": "tie_38", "parent": "tie_37", "length": 64.65, "rotation": 6.82, "x": 54.06}, {"name": "tie_39", "parent": "tie_38", "length": 54.76, "rotation": -1.88, "x": 64.65}, {"name": "tie_40", "parent": "tie_39", "length": 49, "rotation": -8.36, "x": 54.76}, {"name": "tie_41", "parent": "tie_40", "length": 33.74, "rotation": -18.53, "x": 49}, {"name": "tie_42", "parent": "tie_41", "length": 35.29, "rotation": -10.87, "x": 33.74}, {"name": "tie_43", "parent": "zong2", "length": 45.94, "rotation": 16.32, "x": -23.83, "y": 43.11}, {"name": "tie_44", "parent": "tie_43", "length": 47.8, "rotation": -3.67, "x": 45.94}, {"name": "tie_45", "parent": "tie_44", "length": 48.21, "rotation": 2.19, "x": 48.42, "y": -0.12}, {"name": "tie_46", "parent": "tie_45", "length": 46.85, "rotation": -4.28, "x": 48.21}, {"name": "tie_47", "parent": "tie_46", "length": 43.05, "rotation": 4.22, "x": 46.85}, {"name": "tie_48", "parent": "tie_47", "length": 40.53, "rotation": 6.4, "x": 42.44, "y": 0.15}, {"name": "tie_49", "parent": "tie_48", "length": 37.46, "rotation": 8.02, "x": 40.53}, {"name": "tie_50", "parent": "zong2", "length": 89.21, "rotation": 2.75, "x": 2.79, "y": 1.7}, {"name": "tie_51", "parent": "tie_50", "length": 80.46, "rotation": 0.67, "x": 89.21}, {"name": "tie_52", "parent": "tie_51", "length": 62.88, "rotation": -0.75, "x": 80.46}, {"name": "tie_53", "parent": "tie_52", "length": 65.85, "rotation": 1.21, "x": 62.88}, {"name": "tie_54", "parent": "tie_53", "length": 70.21, "rotation": -0.75, "x": 63.6, "y": -1.36}, {"name": "tie_55", "parent": "zong2", "length": 62.06, "rotation": 1.88, "x": -5.5, "y": -24.14}, {"name": "tie_56", "parent": "tie_55", "length": 52.2, "rotation": -1.17, "x": 61.48, "y": 0.01}, {"name": "tie_57", "parent": "tie_56", "length": 53.99, "rotation": -1.83, "x": 52.2}, {"name": "tie_58", "parent": "tie_57", "length": 48.2, "rotation": -0.3, "x": 53.99}, {"name": "tie_59", "parent": "tie_58", "length": 60.36, "rotation": 0.56, "x": 48.2}, {"name": "tie_60", "parent": "tie_59", "length": 58.58, "rotation": 1.64, "x": 60.39, "y": -0.58}, {"name": "tie_61", "parent": "zong2", "length": 50.73, "rotation": 7.25, "x": -11.48, "y": 33.16}, {"name": "tie_62", "parent": "tie_61", "length": 46.43, "rotation": -3.76, "x": 50.09, "y": -0.52}, {"name": "tie_63", "parent": "tie_62", "length": 57.61, "rotation": 2.47, "x": 46.43}, {"name": "tie_64", "parent": "tie_63", "length": 55.21, "rotation": -1.01, "x": 57.61}, {"name": "tie_65", "parent": "tie_64", "length": 43.64, "rotation": 0.96, "x": 53.4, "y": -1.05}, {"name": "tie_66", "parent": "tie_65", "length": 43.02, "rotation": -0.71, "x": 43.64}, {"name": "tie_67", "parent": "tie_66", "length": 43.64, "rotation": 0.71, "x": 42.44, "y": 0.04}, {"name": "tie_36b", "parent": "tie_36", "length": 92.11, "x": 92.11}, {"name": "tie_36c", "parent": "tie_36b", "length": 92.11, "x": 92.11}, {"name": "zong4", "parent": "zong2", "length": 200.11, "rotation": 2.37, "x": 116.42, "y": 29.96}, {"name": "zong5", "parent": "zong4", "length": 199.18, "rotation": -0.81, "x": 197.36, "y": 0.31}, {"name": "zong6", "parent": "zong5", "length": 43.8, "rotation": 67.78, "x": 214.83, "y": 6.97}, {"name": "zong7", "parent": "zong2", "length": 201.67, "rotation": -0.88, "x": 111.91, "y": -56.91}, {"name": "zong8", "parent": "zong7", "length": 181.29, "rotation": -6.63, "x": 201.67, "scaleX": 1.2071}, {"name": "zong9", "parent": "zong8", "length": 31.77, "rotation": -41.4, "x": 186.84, "y": 9.93}, {"name": "zong10", "parent": "zong2", "length": 54.54, "rotation": -32.35, "x": 409.09, "y": -175.67}, {"name": "zong11", "parent": "zong10", "length": 62.56, "rotation": -35.54, "x": 54.55}, {"name": "zong12", "parent": "zong11", "length": 58.5, "rotation": -21.76, "x": 61.62, "y": 0.36}, {"name": "zong13", "parent": "zong2", "length": 76.36, "rotation": 35.03, "x": 446.28, "y": 131.87}], "slots": [{"name": "tie_14", "bone": "tie_30", "attachment": "tie_14"}, {"name": "tie_13", "bone": "tie_13", "attachment": "tie_13"}, {"name": "tie_12", "bone": "tie_12", "attachment": "tie_12"}, {"name": "tie_11", "bone": "zong2", "attachment": "tie_11"}, {"name": "tie_10", "bone": "tie_50", "attachment": "tie_10"}, {"name": "tie_9", "bone": "tie_35", "attachment": "tie_9"}, {"name": "tie_8", "bone": "tie_43", "attachment": "tie_8"}, {"name": "tie_7", "bone": "tie_55", "attachment": "tie_7"}, {"name": "tie_6", "bone": "tie_61", "attachment": "tie_6"}, {"name": "tie_5", "bone": "tie_5", "attachment": "tie_5"}, {"name": "tie_4", "bone": "tie_4", "attachment": "tie_4"}, {"name": "tie_3", "bone": "tie_3", "attachment": "tie_3"}, {"name": "tie_2", "bone": "tie_2", "attachment": "tie_2"}, {"name": "tie_1", "bone": "tie_36", "attachment": "tie_1"}, {"name": "tie_0", "bone": "tie_0", "attachment": "tie_0"}], "skins": [{"name": "default", "attachments": {"tie_0": {"tie_0": {"type": "mesh", "uvs": [0.93035, 0.20092, 0.54721, 0.01025, 0.46405, 0.01681, 0.18055, 0.22438, 0.00392, 0.51481, 0.02644, 0.57881, 0.09536, 0.58021, 0.17353, 0.51908, 0.12528, 0.62984, 0.12105, 0.9489, 0.21636, 0.98975, 0.39914, 0.84293, 0.60789, 0.72422, 0.8167, 0.76773, 0.99999, 0.73868, 0.98904, 0.41224, 0.58142, 0.35722, 0.30855, 0.49042], "triangles": [9, 8, 10, 10, 8, 11, 11, 8, 17, 8, 7, 17, 11, 17, 12, 7, 6, 4, 6, 5, 4, 17, 7, 3, 7, 4, 3, 17, 2, 16, 17, 3, 2, 14, 13, 15, 15, 13, 16, 17, 16, 12, 15, 16, 0, 16, 13, 12, 2, 1, 16, 16, 1, 0], "vertices": [2, 31, 1.4, -13.61, 0.78664, 32, -28.36, -0.2, 0.21336, 2, 31, 30.4, -19.08, 0.52049, 32, -5.41, -18.76, 0.47951, 3, 31, 36.24, -17.68, 0.18716, 32, 0.39, -20.28, 0.69704, 33, -27.6, -5.01, 0.1158, 3, 31, 54.32, -2.85, 0.06719, 32, 23.34, -15.79, 0.48394, 33, -6.4, -14.88, 0.44888, 2, 32, 40.41, -4.58, 0.21779, 33, 14, -15.83, 0.78221, 2, 32, 39.96, -0.73, 0.00026, 33, 15.9, -12.46, 0.99974, 2, 32, 35.27, 0.89, 0.0063, 33, 13.06, -8.39, 0.9937, 2, 32, 28.87, -0.55, 0.0063, 33, 7.04, -5.8, 0.9937, 2, 32, 34.07, 4.16, 0.00631, 33, 14.01, -5.05, 0.99369, 3, 31, 51.53, 37.13, 3e-05, 32, 39.83, 20.74, 0.00482, 33, 28.42, 4.98, 0.99514, 3, 31, 44.38, 38.14, 0.01872, 32, 34.01, 25.01, 0.09731, 33, 26.22, 11.86, 0.88397, 3, 31, 32.85, 27.87, 0.20338, 32, 18.99, 21.44, 0.21473, 33, 11.97, 17.8, 0.58189, 3, 31, 19.2, 18.8, 0.52333, 32, 2.67, 19.91, 0.22292, 33, -2.12, 26.16, 0.25375, 3, 31, 3.98, 18.52, 0.83797, 32, -10.87, 26.87, 0.13044, 33, -8.99, 39.75, 0.03159, 3, 31, -8.73, 14.62, 0.98665, 32, -23.91, 29.47, 0.013, 33, -18.01, 49.51, 0.00035, 2, 31, -4.8, -2.91, 1, 32, -28.75, 12.16, 0, 1, 31, 24.62, -0.73, 1, 3, 31, 42.68, 9.93, 0.00046, 32, 19.15, 0.98, 0.66185, 33, 0.07, 1.16, 0.3377], "hull": 16}}, "tie_1": {"tie_1": {"type": "mesh", "uvs": [0.34872, 0.58175, 0.15742, 0.77628, 0.07191, 0.81863, 1e-05, 0.89939, 0, 1, 0.09374, 0.99999, 0.16672, 0.91587, 0.20628, 0.82909, 0.40418, 0.63497, 0.60614, 0.61957, 0.63841, 0.57709, 0.64416, 0.48431, 0.74911, 0.5097, 0.87477, 0.40355, 0.9651, 0.37881, 0.99999, 0.33238, 0.9994, 0.27419, 0.89229, 0.0224, 0.57618, 0, 0.51737, 0.02691, 0.49524, 0.10742, 0.50918, 0.14225, 0.45633, 0.17199, 0.42137, 0.27282, 0.45581, 0.3469, 0.35937, 0.36447, 0.3332, 0.42267], "triangles": [18, 21, 20, 16, 13, 17, 14, 16, 15, 17, 21, 18, 14, 13, 16, 17, 13, 21, 18, 20, 19, 24, 23, 22, 13, 11, 21, 24, 22, 21, 11, 24, 21, 12, 11, 13, 24, 11, 8, 10, 9, 11, 25, 24, 26, 9, 8, 11, 24, 0, 26, 8, 0, 24, 7, 1, 0, 7, 0, 8, 6, 1, 7, 2, 1, 6, 6, 3, 2, 5, 3, 6, 4, 3, 5], "vertices": [2, 38, 53.29, 11.25, 0.85437, 73, -38.82, 11.25, 0.14563, 2, 38, -26.95, 15.26, 0.99983, 73, -119.07, 15.26, 0.00017, 1, 38, -53.81, 27.79, 1, 1, 38, -85.52, 27.45, 1, 1, 38, -105.84, 3.23, 1, 1, 38, -85.78, -21.68, 1, 1, 38, -53.16, -20.82, 1, 2, 38, -27.17, -10.44, 0.99973, 73, -119.28, -10.44, 0.00027, 2, 38, 54.41, -16.3, 0.73906, 73, -37.7, -16.3, 0.26094, 2, 38, 100.75, -66.25, 0.45166, 73, 8.63, -66.25, 0.54834, 3, 38, 116.24, -64.6, 0.12306, 73, 24.12, -64.6, 0.86875, 74, -67.99, -64.6, 0.00819, 3, 38, 136.21, -43.79, 0.05039, 73, 44.1, -43.79, 0.86994, 74, -48.02, -43.79, 0.07967, 3, 38, 153.54, -77.8, 0.00446, 73, 61.43, -77.8, 0.66625, 74, -30.68, -77.8, 0.32929, 2, 73, 109.77, -85.63, 0.36981, 74, 17.66, -85.63, 0.63019, 2, 73, 134.1, -103.68, 0.12214, 74, 41.99, -103.68, 0.87786, 2, 73, 150.95, -101.77, 0.04653, 74, 58.84, -101.77, 0.95347, 2, 73, 162.58, -87.61, 0.02229, 74, 70.47, -87.61, 0.97771, 2, 73, 190.52, 1.47, 0.07615, 74, 98.41, 1.47, 0.92385, 2, 73, 127.39, 90.86, 0.16145, 74, 35.28, 90.86, 0.83855, 2, 73, 109.37, 100.01, 0.30162, 74, 17.25, 100.01, 0.69838, 2, 73, 88.37, 86.51, 0.42121, 74, -3.75, 86.51, 0.57879, 2, 73, 84.31, 74.42, 0.59304, 74, -7.8, 74.42, 0.40696, 3, 38, 159.11, 81.3, 0.00042, 73, 66.99, 81.3, 0.76853, 74, -25.12, 81.3, 0.23105, 3, 38, 131.25, 66.32, 0.02205, 73, 39.14, 66.32, 0.89101, 74, -52.97, 66.32, 0.08694, 3, 38, 123.66, 39.33, 0.15616, 73, 31.54, 39.33, 0.825, 74, -60.57, 39.33, 0.01884, 3, 38, 99.47, 60.73, 0.34361, 73, 7.35, 60.73, 0.65481, 74, -84.76, 60.73, 0.00158, 2, 38, 82.11, 53.67, 0.65515, 73, -10.01, 53.67, 0.34485], "hull": 27}}, "tie_2": {"tie_2": {"type": "mesh", "uvs": [0.48613, 0.00038, 0.17236, 0.15191, 0.14099, 0.27791, 0.05732, 0.41668, 0.00502, 0.57458, 0, 0.75322, 0.0547, 0.89518, 0.13314, 1, 0.23512, 1, 0.38939, 0.90315, 0.56981, 0.76598, 0.75807, 0.60329, 0.89665, 0.42625, 1, 0.2369, 1, 0.13003, 0.78986, 0.00403, 0.33998, 0.32612, 0.29638, 0.484, 0.23825, 0.65736, 0.20434, 0.83414], "triangles": [9, 8, 19, 19, 8, 6, 8, 7, 6, 9, 19, 10, 6, 5, 19, 19, 18, 10, 19, 5, 18, 10, 18, 11, 5, 4, 18, 18, 4, 17, 18, 17, 11, 11, 17, 12, 12, 17, 16, 4, 3, 17, 17, 3, 16, 12, 16, 13, 16, 0, 15, 13, 16, 15, 3, 2, 16, 2, 1, 16, 16, 1, 0, 15, 14, 13], "vertices": [1, 30, 27.7, -38.5, 1, 4, 30, 75.37, -27.95, 0.94237, 34, -43.68, -40.26, 0.05169, 35, -71.01, -40.05, 0.00142, 36, -102.58, -26.72, 0.00451, 4, 30, 89.82, -6.94, 0.68286, 34, -18.36, -37.37, 0.21355, 35, -45.67, -37.24, 0.03743, 36, -77.09, -27.18, 0.06616, 4, 30, 111.12, 13.57, 0.35339, 34, 11.1, -39.98, 0.24477, 35, -16.23, -39.93, 0.07985, 36, -48.23, -33.63, 0.32199, 5, 30, 130.65, 39.19, 0.07768, 34, 43.25, -37.89, 0.19308, 35, 15.93, -37.93, 0.07843, 36, -16.09, -35.78, 0.60814, 37, -43.33, -36.77, 0.04268, 5, 30, 146.8, 71.07, 0.00386, 34, 77.9, -29.14, 0.03123, 35, 50.6, -29.29, 0.04242, 36, 19.41, -31.67, 0.59739, 37, -7.93, -31.86, 0.3251, 2, 36, 46.67, -21.3, 0.34156, 37, 19.09, -20.88, 0.65844, 2, 36, 66.18, -9.05, 0.05091, 37, 38.32, -8.19, 0.94909, 3, 35, 90.87, 11.17, 0.03008, 36, 64.54, 3.29, 0.02936, 37, 36.4, 4.1, 0.94057, 4, 34, 94.44, 24.54, 0.0156, 35, 67.3, 24.35, 0.29084, 36, 42.85, 19.38, 0.07073, 37, 14.36, 19.71, 0.62283, 5, 30, 85.37, 103.72, 0.0126, 34, 62.21, 38.63, 0.1593, 35, 35.11, 38.52, 0.46787, 36, 12.75, 37.57, 0.07073, 37, -16.15, 37.22, 0.28949, 5, 30, 50.5, 84.47, 0.1405, 34, 24.8, 52.3, 0.31902, 35, -2.26, 52.3, 0.48351, 36, -22.54, 56.04, 0.04137, 37, -51.84, 54.89, 0.0156, 3, 30, 19.83, 60, 0.43354, 34, -13.8, 59.37, 0.33996, 35, -40.83, 59.49, 0.2265, 3, 30, -8.05, 31.43, 0.7477, 34, -53.65, 61.65, 0.20262, 35, -80.68, 61.89, 0.04968, 3, 30, -17.38, 12.2, 0.95314, 34, -74.28, 56.07, 0.0429, 35, -101.32, 56.36, 0.00396, 3, 30, -5.32, -21.66, 0.99342, 34, -91.91, 24.74, 0.00636, 35, -119.04, 25.09, 0.00021, 3, 30, 72.19, 12.33, 0.61345, 34, -15.39, -11.42, 0.38389, 36, -70.75, -1.84, 0.00266, 4, 30, 90.76, 38.41, 0.01792, 34, 16.48, -8.3, 0.85853, 35, -10.76, -8.27, 0.07688, 36, -38.74, -2.94, 0.04667, 1, 36, -3.44, -5.39, 1, 2, 36, 32.15, -4.82, 0.20744, 37, 4.21, -4.73, 0.79256], "hull": 16}}, "tie_3": {"tie_3": {"type": "mesh", "uvs": [0.60843, 0.74482, 0.60762, 0.89582, 0.9193, 0.84191, 0.99999, 0.71554, 0.99416, 0.50094, 0.90268, 0.31382, 0.68757, 0.23421, 0.41068, 0.08299, 0.227, 0.07734, 0.04485, 0.03802, 1e-05, 0.09244, 0.05393, 0.1943, 0.22388, 0.27319, 0.37842, 0.30103, 0.22911, 0.31378, 0.01168, 0.50897, 0.06755, 0.59269, 0.31996, 0.4995, 0.39346, 0.49687, 0.30166, 0.60336, 0.22721, 0.90016, 0.27378, 0.95731, 0.3495, 0.92003, 0.39594, 0.82731, 0.38574, 0.94652, 0.48009, 0.94481, 0.54002, 0.76381, 0.46134, 0.65593, 0.46152, 0.55077, 0.66653, 0.46886, 0.70204, 0.62855, 0.55263, 0.66983], "triangles": [21, 20, 22, 25, 24, 23, 25, 23, 26, 22, 20, 23, 20, 19, 23, 23, 27, 26, 23, 19, 27, 26, 31, 0, 26, 27, 31, 27, 19, 28, 31, 27, 28, 28, 19, 18, 31, 28, 29, 28, 18, 13, 17, 16, 14, 16, 15, 14, 17, 13, 18, 17, 14, 13, 13, 12, 8, 12, 11, 8, 10, 9, 11, 11, 9, 8, 13, 7, 6, 7, 13, 8, 2, 0, 30, 2, 1, 0, 2, 30, 3, 0, 31, 30, 30, 4, 3, 29, 30, 31, 30, 29, 4, 29, 28, 13, 29, 5, 4, 29, 6, 5, 29, 13, 6], "vertices": [3, 16, 15.42, 17.02, 0.4771, 21, 2.95, 15.74, 0.43552, 22, 0.67, 18.55, 0.08738, 3, 16, 13.37, 26.46, 0.66465, 21, 9.16, 23.14, 0.32466, 22, 9.96, 21.2, 0.01069, 2, 16, -8.38, 18.05, 0.9267, 21, -10.81, 35.19, 0.0733, 2, 16, -12.44, 8.85, 0.97646, 21, -20.56, 32.77, 0.02354, 1, 16, -9.01, -4.46, 1, 2, 16, 0.22, -14.66, 0.98869, 17, -32.64, -14.92, 0.01131, 2, 16, 16.87, -16.15, 0.77432, 17, -16.38, -11.05, 0.22568, 3, 16, 38.98, -21.1, 0.0062, 17, 6.16, -8.74, 0.97119, 18, -6.94, -6.87, 0.02261, 1, 18, 6.41, -4.32, 1, 1, 18, 20.12, -3.9, 1, 1, 18, 22.61, 0.21, 1, 1, 18, 17.32, 5.73, 1, 2, 17, 11.71, 8.82, 0.22129, 18, 3.96, 7.97, 0.77871, 3, 17, 0.99, 4.44, 0.56203, 18, -7.59, 7.27, 0.00104, 19, 4.5, -6.46, 0.43693, 3, 17, 10.03, 10.85, 0.00178, 19, 15.53, -5.36, 0.45353, 20, -0.67, -5.78, 0.54469, 1, 20, 19.4, -2.32, 1, 1, 20, 18.27, 4.35, 1, 3, 19, 8.5, 6.35, 0.78509, 20, -1.02, 7.87, 0.16995, 21, 9.39, -9.97, 0.04496, 4, 16, 34.42, 5.02, 0.00408, 19, 3.07, 6.04, 0.52312, 20, -5.9, 10.28, 0.00145, 21, 5.09, -6.63, 0.47135, 3, 19, 9.68, 13.03, 0.00904, 21, 14.67, -5.71, 0.51454, 22, -1.65, -5.78, 0.47642, 1, 22, 18.12, -5.74, 1, 1, 22, 20.67, -1.41, 1, 1, 22, 16.81, 3.3, 1, 3, 16, 29.61, 25.62, 0, 21, 18.43, 9.78, 0.05755, 22, 10.15, 4.94, 0.94244, 2, 21, 23.88, 15.17, 0.04525, 22, 17.68, 6.35, 0.95475, 2, 21, 18.43, 19.54, 0.07022, 22, 15.62, 13.02, 0.92978, 3, 16, 20.1, 19.32, 0.11695, 21, 7.63, 13.45, 0.47511, 22, 3.26, 14.03, 0.40794, 3, 16, 27.29, 13.85, 0.01094, 21, 7.71, 4.41, 0.79164, 22, -1.74, 6.51, 0.19742, 3, 16, 28.75, 7.28, 0.00342, 19, -2.06, 9.36, 0.02842, 21, 3.41, -0.76, 0.96816, 2, 16, 15.1, -1.15, 0.99481, 17, -22.81, 2.61, 0.00519, 3, 16, 10.29, 8.24, 0.80901, 21, -7.13, 14.42, 0.18256, 22, -8.41, 23.11, 0.00844, 3, 16, 20.5, 13.24, 0.2086, 21, 3.08, 9.41, 0.6513, 22, -2.78, 13.24, 0.1401], "hull": 27}}, "tie_4": {"tie_4": {"type": "mesh", "uvs": [0.99808, 1e-05, 0.82354, 0.00459, 0.71384, 0.10678, 0.51676, 0.06048, 0.54041, 0.23777, 0.54122, 0.37429, 0.48258, 0.40856, 0.35975, 0.3861, 0.19224, 0.37623, 0.1102, 0.4413, 0.10126, 0.59202, 0.07265, 0.66248, 0.06053, 0.75722, 0.1078, 0.85752, 0.1636, 0.94863, 0.20805, 0.99597, 0.26116, 0.99523, 0.43092, 0.96265, 0.53195, 0.88605, 0.67302, 0.81664, 0.76417, 0.74584, 0.84293, 0.62677, 0.88738, 0.55881, 0.91332, 0.43422, 0.93459, 0.29252, 1, 0.10414, 0.30945, 0.43831, 0.32754, 0.59773, 0.29073, 0.68203, 0.25965, 0.77454, 0.25359, 0.86459], "triangles": [14, 13, 30, 17, 30, 18, 16, 14, 30, 17, 16, 30, 15, 14, 16, 30, 13, 29, 18, 30, 29, 12, 11, 28, 29, 12, 28, 19, 29, 28, 13, 12, 29, 19, 18, 29, 28, 10, 27, 11, 10, 28, 20, 27, 21, 28, 27, 20, 20, 19, 28, 26, 8, 7, 9, 8, 26, 6, 27, 26, 6, 26, 7, 21, 27, 6, 22, 21, 6, 10, 26, 27, 26, 10, 9, 1, 0, 25, 2, 1, 25, 4, 3, 2, 24, 2, 25, 4, 2, 24, 5, 4, 24, 23, 5, 24, 23, 6, 5, 22, 6, 23], "vertices": [1, 14, -23.75, 16.95, 1, 1, 14, -17.02, -10.19, 1, 1, 14, 17.04, -21.42, 1, 2, 14, 9.18, -55.09, 0.99916, 15, 38.03, -133.58, 0.00084, 2, 14, 61.7, -40.97, 0.89386, 15, 30.87, -79.67, 0.10614, 2, 14, 102.67, -32.84, 0.64165, 15, 28.13, -37.99, 0.35835, 2, 14, 114.76, -40.03, 0.31636, 15, 36.84, -26.94, 0.68364, 2, 14, 111.78, -60.64, 0.08832, 15, 56.89, -32.57, 0.91168, 3, 14, 113.96, -87.52, 0.0072, 15, 83.83, -33.91, 0.9926, 23, -56.76, -46.3, 0.0002, 4, 15, 95.68, -13.22, 0.78965, 23, -34.6, -55.12, 0.16043, 24, -71.39, -50.43, 0.04847, 25, -104.01, -44.33, 0.00145, 4, 15, 94.24, 32.9, 0.4865, 23, 10.85, -47.18, 0.30537, 24, -25.5, -45.56, 0.18696, 25, -57.92, -42.18, 0.02118, 5, 15, 97.46, 54.7, 0.15476, 23, 32.89, -47.29, 0.34556, 24, -3.52, -47.16, 0.36892, 25, -36.06, -45.06, 0.12896, 26, -64.36, -45.9, 0.0018, 5, 15, 97.59, 83.76, 0.03158, 23, 61.67, -43.32, 0.18611, 24, 25.47, -45.13, 0.3705, 25, -7.01, -44.75, 0.32455, 26, -35.32, -45.21, 0.08727, 5, 15, 88.12, 113.92, 0.0014, 23, 90.2, -29.69, 0.04116, 24, 54.84, -33.46, 0.23227, 25, 23, -34.82, 0.32988, 26, -5.43, -34.89, 0.39528, 4, 23, 115.69, -15.3, 0.00077, 24, 81.24, -20.82, 0.05031, 25, 50.1, -23.75, 0.22221, 26, 21.52, -23.48, 0.72671, 3, 24, 94.63, -11.8, 0.00025, 25, 63.99, -15.54, 0.02518, 26, 35.31, -15.08, 0.97457, 4, 15, 61, 154.44, 0.00013, 24, 93.25, -3.41, 0.00022, 25, 63.11, -7.08, 0.01882, 26, 34.31, -6.64, 0.98083, 5, 15, 34.52, 142.8, 0.00819, 23, 111.23, 27.45, 0.00827, 24, 79.67, 22.14, 0.04528, 25, 51.06, 19.22, 0.19464, 26, 21.92, 19.5, 0.74362, 5, 15, 19.85, 118.4, 0.05307, 23, 85, 38.54, 0.06915, 24, 54.25, 34.96, 0.16253, 25, 26.43, 33.52, 0.30006, 26, -2.89, 33.48, 0.41518, 6, 14, 231.48, 13.81, 0.00103, 15, -1.36, 95.79, 0.14834, 23, 59.62, 56.34, 0.17287, 24, 30.13, 54.43, 0.25326, 25, 3.5, 54.37, 0.32359, 26, -26.08, 54.04, 0.1009, 6, 14, 207.42, 23.97, 0.057, 15, -14.56, 73.26, 0.31101, 23, 35.45, 66.23, 0.24054, 24, 6.68, 65.93, 0.23318, 25, -19.23, 67.23, 0.15338, 26, -48.98, 66.6, 0.00489, 5, 14, 169.24, 29.35, 0.24232, 15, -24.86, 36.11, 0.37993, 23, -2.78, 71.18, 0.20638, 24, -31.13, 73.45, 0.12258, 25, -56.53, 76.96, 0.04879, 5, 14, 147.47, 32.34, 0.57462, 15, -30.67, 14.91, 0.28453, 23, -24.58, 73.94, 0.10266, 24, -52.7, 77.66, 0.03162, 25, -77.82, 82.43, 0.00656, 5, 14, 109.26, 29.11, 0.85199, 15, -32.43, -23.4, 0.1138, 23, -62.76, 70.28, 0.02672, 24, -91.04, 76.58, 0.00665, 25, -116.15, 83.61, 0.00084, 1, 14, 66.05, 24.14, 1, 1, 14, 7.47, 23.36, 1, 2, 14, 129.01, -65.48, 0.00362, 15, 63.92, -16.12, 0.99638, 3, 15, 57.99, 32.38, 0.09768, 23, 5.23, -11.37, 0.8924, 24, -28.7, -9.46, 0.00992, 4, 15, 62.26, 58.5, 0.00842, 23, 31.68, -11.91, 0.58876, 24, -2.34, -11.78, 0.39708, 25, -32.81, -9.81, 0.00574, 4, 23, 60.41, -11.04, 0.01362, 24, 26.38, -12.85, 0.66544, 25, -4.2, -12.57, 0.32093, 26, -32.92, -13, 1e-05, 3, 24, 53.81, -10.05, 0.02085, 25, 23.35, -11.4, 0.74625, 26, -5.39, -11.47, 0.2329], "hull": 26}}, "tie_5": {"tie_5": {"type": "mesh", "uvs": [0.08373, 0, 0.05232, 0.21101, 0.00674, 0.35165, 0.03584, 0.44419, 0.32901, 0.54307, 0.30625, 0.69336, 0.27939, 0.84232, 0.34389, 0.96593, 0.37945, 0.97067, 0.47161, 0.83994, 0.48264, 0.70842, 0.48724, 0.65407, 0.66256, 0.82816, 0.7416, 0.73508, 0.74881, 0.85713, 0.84172, 0.99705, 0.87065, 0.99671, 0.94103, 0.85902, 0.91035, 0.70273, 0.88717, 0.5537, 0.99197, 0.46236, 1, 0.27199, 0.95897, 0.09109, 0.83571, 0.10272, 0.69629, 0.16958, 0.65226, 0.13421, 0.59138, 0.17689, 0.39188, 0.07548, 0.44162, 0.33482, 0.82864, 0.32523, 0.66914, 0.47873], "triangles": [29, 24, 23, 30, 26, 24, 24, 26, 25, 28, 27, 26, 1, 0, 27, 23, 22, 21, 29, 23, 21, 1, 27, 28, 3, 2, 1, 3, 1, 28, 20, 29, 21, 30, 24, 29, 28, 26, 30, 4, 3, 28, 11, 4, 28, 19, 29, 20, 30, 29, 19, 30, 11, 28, 5, 4, 11, 10, 5, 11, 13, 30, 19, 13, 19, 18, 11, 30, 13, 13, 12, 11, 9, 5, 10, 6, 5, 9, 14, 13, 18, 14, 18, 17, 8, 7, 6, 9, 8, 6, 17, 15, 14, 16, 15, 17], "vertices": [1, 4, 36.59, 72.02, 1, 1, 3, 60.96, 81.72, 1, 1, 3, 42.07, 87.32, 1, 1, 3, 29.89, 82.97, 1, 1, 3, 18.03, 42.69, 1, 1, 3, -2.05, 45.15, 1, 1, 3, -21.97, 48.16, 1, 1, 3, -38.12, 38.87, 1, 1, 3, -38.6, 34.02, 1, 1, 3, -20.82, 22.04, 1, 1, 3, -3.28, 21.11, 1, 1, 3, 3.96, 20.71, 1, 1, 3, -18.42, -3.86, 1, 1, 3, -5.7, -14.21, 1, 1, 3, -21.89, -15.71, 1, 1, 3, -40.09, -28.93, 1, 1, 3, -39.92, -32.86, 1, 1, 3, -21.3, -41.84, 1, 1, 3, -0.66, -37.01, 1, 1, 3, 19.05, -33.22, 1, 1, 3, 31.65, -47.08, 1, 1, 3, 56.99, -47.36, 1, 1, 4, 3.94, -43.09, 1, 1, 4, 5.33, -26.31, 1, 1, 4, -0.12, -6.09, 1, 1, 4, 5.55, -1.01, 1, 1, 4, 1.4, 8.13, 1, 1, 4, 19.41, 32.5, 1, 1, 3, 46.2, 28.28, 1, 1, 3, 49.16, -24.29, 1, 1, 3, 28.06, -3.26, 1], "hull": 28}}, "tie_6": {"tie_6": {"type": "mesh", "uvs": [0.36732, 1, 0.29693, 0.83741, 0.25469, 0.68843, 0.24062, 0.55185, 0.17022, 0.37522, 0.07871, 0.20102, 0, 0.00032, 0.47291, 0, 0.59258, 0.19482, 0.65593, 0.3707, 0.74744, 0.54451, 0.82487, 0.68609, 0.87414, 0.83093, 1, 0.99233], "triangles": [0, 12, 13, 0, 1, 12, 12, 2, 11, 12, 1, 2, 2, 3, 11, 3, 10, 11, 10, 3, 4, 10, 4, 9, 4, 5, 9, 5, 8, 9, 5, 7, 8, 5, 6, 7], "vertices": [3, 70, 133.27, -16.93, 0.11074, 71, 89.83, -15.83, 0.27053, 72, 47.19, -16.45, 0.61873, 4, 69, 130.4, -15.77, 0.10327, 70, 76.74, -16.01, 0.22932, 71, 33.3, -15.6, 0.33408, 72, -9.33, -15.53, 0.33333, 5, 68, 136.01, -16.04, 0.11111, 69, 78.67, -14.67, 0.20653, 70, 25.04, -14.04, 0.3479, 71, -18.42, -14.27, 0.22335, 72, -61.04, -13.55, 0.11111, 5, 67, 135.62, -9.1, 0.11111, 68, 88.71, -12.94, 0.22222, 69, 31.33, -12.39, 0.3098, 70, -22.26, -10.98, 0.24501, 71, -65.76, -11.79, 0.11186, 6, 66, 123.49, -15.76, 0.11111, 67, 74.24, -10.39, 0.22222, 68, 27.33, -11.58, 0.33333, 69, -30.07, -12.11, 0.20653, 70, -83.64, -9.67, 0.12643, 71, -127.15, -11.24, 0.00038, 5, 66, 62.89, -14.18, 0.33333, 67, 13.66, -12.79, 0.33333, 68, -33.29, -11.37, 0.22222, 69, -90.69, -12.96, 0.10327, 70, -144.27, -9.5, 0.00785, 3, 66, -6.8, -11, 0.5816, 67, -56.09, -14.19, 0.30729, 68, -103.04, -9.76, 0.11111, 3, 66, -4.43, 13, 0.60764, 67, -55.29, 9.91, 0.28125, 68, -101.2, 14.29, 0.11111, 4, 66, 63.44, 12.11, 0.41146, 67, 12.49, 13.48, 0.25521, 68, -33.33, 14.93, 0.22222, 69, -91.18, 13.33, 0.11111, 6, 66, 124.48, 9.05, 0.1632, 67, 73.6, 14.42, 0.17014, 68, 27.76, 13.23, 0.33333, 69, -30.07, 12.71, 0.23128, 70, -83.23, 15.15, 0.10177, 71, -127.05, 13.58, 0.00028, 7, 66, 184.95, 7.48, 0.02604, 67, 134.04, 16.82, 0.08507, 68, 88.25, 13.03, 0.22222, 69, 30.41, 13.56, 0.35145, 70, -22.74, 14.99, 0.20354, 71, -66.56, 14.17, 0.0485, 72, -108.81, 15.48, 0.06318, 5, 68, 137.54, 13.01, 0.11111, 69, 79.69, 14.41, 0.24939, 70, 26.55, 15.01, 0.30531, 71, -17.27, 14.8, 0.09672, 72, -59.53, 15.5, 0.23747, 4, 69, 130.01, 13.75, 0.12923, 70, 76.85, 13.51, 0.20354, 71, 33.04, 13.92, 0.14437, 72, -9.23, 13.99, 0.52287, 4, 69, 186.31, 16.62, 0.00906, 70, 133.19, 15.44, 0.10177, 71, 89.35, 16.55, 0.20726, 72, 47.11, 15.93, 0.68191], "hull": 14}}, "tie_7": {"tie_7": {"type": "mesh", "uvs": [0.32157, 0, 0.31235, 0.13554, 0.26624, 0.29295, 0.20168, 0.46857, 0.1279, 0.6546, 0.05412, 0.83202, 0, 1, 0.93024, 1, 0.8749, 0.83106, 0.92101, 0.65648, 0.94868, 0.47469, 1, 0.29152, 1, 0.12845, 1, 0], "triangles": [5, 4, 8, 5, 8, 7, 6, 5, 7, 9, 4, 10, 8, 4, 9, 3, 2, 10, 4, 3, 10, 10, 2, 11, 0, 13, 12, 1, 0, 12, 1, 12, 11, 2, 1, 11], "vertices": [3, 60, -6.49, -11.59, 0.88559, 61, -67.73, -12.98, 0.11363, 62, -119.45, -16.8, 0.00078, 4, 60, 40.67, -12.36, 0.66172, 61, -20.56, -12.79, 0.22561, 62, -72.31, -15.1, 0.10733, 63, -126.22, -15.76, 0.00533, 5, 60, 95.43, -14.53, 0.33004, 61, 34.24, -13.84, 0.3343, 62, -17.51, -14.41, 0.21388, 63, -71.43, -14.78, 0.02922, 64, -119.76, -13.61, 0.09256, 6, 60, 156.52, -17.43, 0.10946, 61, 95.37, -15.49, 0.22232, 62, 43.64, -14.1, 0.31888, 63, -10.27, -14.16, 0.05311, 64, -58.6, -13.59, 0.18512, 65, -119.31, -9.61, 0.11111, 5, 61, 160.14, -17.42, 0.11034, 62, 108.44, -13.98, 0.21233, 63, 54.52, -13.69, 0.06632, 64, 6.19, -13.75, 0.27768, 65, -54.55, -11.62, 0.33333, 4, 62, 170.24, -13.98, 0.10578, 63, 116.32, -13.37, 0.04244, 64, 67.99, -14.04, 0.18512, 65, 7.21, -13.67, 0.66667, 3, 63, 174.8, -12.51, 0.01855, 64, 126.48, -13.74, 0.18973, 65, 65.69, -15.04, 0.79172, 3, 63, 173.19, 20.94, 0.02591, 64, 125.19, 19.73, 0.27954, 65, 65.36, 18.45, 0.69455, 4, 62, 168.63, 15.53, 0.10296, 63, 114.56, 16.12, 0.05997, 64, 66.52, 15.48, 0.46191, 65, 6.59, 15.88, 0.37516, 5, 61, 160.48, 11.13, 0.11111, 62, 107.86, 14.58, 0.20593, 63, 53.8, 14.86, 0.09402, 64, 5.74, 14.8, 0.44995, 65, -54.18, 16.93, 0.13899, 6, 60, 158.9, 9.44, 0.11111, 61, 97.21, 11.43, 0.22222, 62, 44.62, 12.85, 0.30889, 63, -9.44, 12.81, 0.07626, 64, -57.51, 13.37, 0.26757, 65, -117.45, 17.3, 0.01394, 5, 60, 95.18, 11.88, 0.33333, 61, 33.45, 12.57, 0.33333, 62, -19.15, 11.96, 0.20593, 63, -73.2, 11.58, 0.0422, 64, -121.28, 12.76, 0.0852, 4, 60, 38.43, 12.41, 0.66667, 61, -23.3, 11.94, 0.22222, 62, -75.84, 9.52, 0.10296, 63, -129.88, 8.85, 0.00815, 2, 60, -6.27, 12.83, 0.88724, 61, -68, 11.44, 0.11276], "hull": 14}}, "tie_8": {"tie_8": {"type": "mesh", "uvs": [0.15609, 0, 0, 0.00095, 0.00956, 0.13833, 0.04607, 0.317, 0.16419, 0.46866, 0.3013, 0.61088, 0.47098, 0.75308, 0.65502, 0.87582, 0.94536, 0.99873, 0.98814, 0.9981, 0.98997, 0.98454, 0.79354, 0.85958, 0.68456, 0.73365, 0.623, 0.59552, 0.60207, 0.44717, 0.52617, 0.29456, 0.34511, 0.12269], "triangles": [8, 7, 10, 9, 8, 10, 11, 10, 7, 7, 6, 11, 6, 12, 11, 12, 6, 13, 6, 5, 13, 5, 4, 13, 4, 14, 13, 14, 4, 15, 4, 3, 15, 3, 16, 15, 3, 2, 16, 2, 0, 16, 2, 1, 0], "vertices": [3, 48, -3.7, 10.2, 0.86009, 49, -50.18, 7, 0.09624, 50, -98.26, 10.88, 0.04367, 3, 48, -7.09, -3.6, 0.86374, 49, -52.69, -6.99, 0.04504, 50, -101.29, -3, 0.09122, 4, 48, 33.75, -13.62, 0.64517, 49, -11.29, -14.38, 0.06127, 50, -60.21, -11.97, 0.26421, 51, -107.22, -20.03, 0.02934, 5, 48, 87.42, -24.55, 0.319, 49, 42.97, -21.84, 0.07399, 50, -6.27, -21.49, 0.43721, 51, -52.72, -25.5, 0.15888, 52, -101.18, -18.11, 0.01092, 5, 48, 135.03, -26.16, 0.10395, 49, 90.58, -20.4, 0.04694, 50, 41.36, -21.87, 0.42775, 51, -5.19, -22.32, 0.28842, 52, -53.54, -18.43, 0.13294, 6, 49, 135.71, -16.7, 0.01989, 50, 86.59, -19.9, 0.25476, 51, 39.77, -16.97, 0.35927, 52, -8.32, -16.41, 0.25497, 53, -52.29, -10.79, 0.1087, 54, -93.42, 2.26, 0.00242, 5, 50, 132.51, -15.04, 0.08177, 51, 85.19, -8.7, 0.22973, 52, 37.59, -11.49, 0.35517, 53, -6.12, -11.03, 0.21739, 54, -47.73, -4.41, 0.11594, 4, 51, 124.95, 1.82, 0.10019, 52, 78.01, -3.93, 0.23314, 53, 34.89, -8.01, 0.32609, 54, -6.7, -7.15, 0.34058, 3, 52, 120.73, 13.03, 0.11111, 53, 79.24, 4.08, 0.21739, 54, 38.9, -1.36, 0.6715, 2, 53, 80.38, 7.81, 0.21659, 54, 40.55, 2.18, 0.78341, 3, 52, 117.45, 17.99, 0.07501, 53, 76.53, 9.37, 0.2519, 54, 36.96, 4.26, 0.67309, 4, 51, 122.06, 15.06, 0.1006, 52, 76.11, 9.49, 0.16054, 53, 34.5, 5.53, 0.39589, 54, -5.2, 6.32, 0.34297, 5, 50, 131.26, 5.25, 0.08378, 51, 82.43, 11.44, 0.22853, 52, 36.33, 8.79, 0.24607, 53, -5.12, 9.27, 0.32409, 54, -43.91, 15.55, 0.11754, 6, 49, 136.84, 12.93, 0.06744, 50, 88.86, 9.67, 0.21123, 51, 39.81, 12.68, 0.35645, 52, -6.09, 13.16, 0.18157, 53, -46.78, 18.34, 0.18009, 54, -83.9, 30.34, 0.00321, 6, 48, 138.98, 14.03, 0.10029, 49, 91.95, 19.96, 0.1457, 50, 44.27, 18.41, 0.33868, 51, -5.3, 18.06, 0.28319, 52, -50.68, 21.85, 0.09604, 53, -90.13, 31.95, 0.0361, 5, 48, 92.08, 19.43, 0.3117, 49, 44.8, 22.35, 0.22396, 50, -2.75, 22.59, 0.29857, 51, -52.5, 18.72, 0.15526, 52, -97.71, 25.98, 0.01051, 4, 48, 37.02, 17.11, 0.63421, 49, -10, 16.5, 0.16734, 50, -57.74, 18.84, 0.17112, 51, -107.05, 10.87, 0.02733], "hull": 17}}, "tie_9": {"tie_9": {"type": "mesh", "uvs": [0.74293, 0.00012, 0.5742, 0.12833, 0.44536, 0.33432, 0.36565, 0.5471, 0.31749, 0.71601, 0.24794, 0.82391, 0.14645, 0.90586, 0.00649, 0.9788, 0, 1, 0.03288, 0.99886, 0.20204, 0.95719, 0.33766, 0.87588, 0.47935, 0.76316, 0.64292, 0.60019, 0.80715, 0.41451, 0.94135, 0.23635, 0.99999, 0.08098], "triangles": [8, 7, 9, 10, 9, 6, 9, 7, 6, 10, 6, 11, 6, 5, 11, 11, 5, 12, 5, 4, 12, 12, 4, 13, 4, 3, 13, 14, 13, 2, 13, 3, 2, 15, 14, 1, 14, 2, 1, 15, 1, 16, 16, 1, 0], "vertices": [3, 41, -3.41, -23.12, 0.76163, 42, -55.65, -16.72, 0.16124, 43, -110.92, -3.57, 0.07713, 4, 41, 47.2, -30.72, 0.54565, 42, -6.29, -30.19, 0.18937, 43, -63.51, -22.81, 0.18392, 44, -127.35, -26.99, 0.08107, 5, 41, 114.71, -19.51, 0.25281, 42, 62.07, -26.97, 0.18305, 43, 4.75, -27.73, 0.2909, 44, -58.96, -29.68, 0.18701, 45, -108.2, -45.89, 0.08624, 6, 41, 179.27, 0.85, 0.07085, 42, 128.58, -14.31, 0.10861, 43, 72.29, -23.07, 0.24401, 44, 8.38, -22.8, 0.29295, 45, -42.57, -29.3, 0.2535, 46, -77.51, -56.88, 0.03009, 6, 42, 180.2, -1.62, 0.03417, 43, 125.05, -16.6, 0.13703, 44, 60.91, -14.61, 0.23676, 45, 8.21, -13.56, 0.42076, 46, -34.36, -25.82, 0.14822, 47, -62.01, -38.2, 0.02306, 5, 43, 160.92, -19.55, 0.03005, 44, 96.86, -16.39, 0.13082, 45, 44.03, -10.1, 0.41554, 46, -1.5, -11.16, 0.26635, 47, -32.5, -17.6, 0.15724, 4, 44, 127.16, -26.54, 0.02488, 45, 75.49, -15.74, 0.24828, 46, 30.12, -6.51, 0.32431, 47, -2.33, -7.08, 0.40253, 3, 45, 107.81, -29.19, 0.08102, 46, 65.04, -8.99, 0.20618, 47, 32.44, -2.93, 0.7128, 2, 46, 70.52, -5.15, 0.15455, 47, 37.09, 1.87, 0.84545, 3, 45, 111.04, -21.87, 0.05975, 46, 65.78, -1.02, 0.18437, 47, 31.66, 5.04, 0.75588, 4, 44, 138.79, -11.36, 0.02673, 45, 84.79, 0.97, 0.20388, 46, 33.63, 12.3, 0.30223, 47, -2.43, 12.06, 0.46715, 5, 43, 171.34, 1.53, 0.04403, 44, 106.58, 5.02, 0.12055, 45, 50.54, 12.5, 0.34801, 46, -2.51, 12.34, 0.28709, 47, -37.93, 5.29, 0.20032, 7, 41, 225.96, 53.55, 0.00062, 42, 181.12, 32.56, 0.04722, 43, 130.02, 17.23, 0.15133, 44, 64.77, 19.36, 0.21436, 45, 7.09, 20.61, 0.37264, 46, -46.29, 6.23, 0.16922, 47, -79.76, -8.97, 0.04461, 6, 41, 166.57, 54.83, 0.06608, 42, 122.29, 40.79, 0.14051, 43, 72.59, 32.39, 0.25882, 44, 6.87, 32.63, 0.25472, 45, -52.12, 25.33, 0.22851, 46, -103.93, -8.12, 0.05136, 5, 41, 101.05, 52.64, 0.24242, 42, 56.97, 46.29, 0.23404, 43, 8.38, 45.62, 0.27826, 44, -57.73, 43.75, 0.1609, 45, -117.66, 26.94, 0.08438, 4, 41, 40.46, 46.7, 0.52863, 42, -3.91, 47.48, 0.23312, 43, -51.92, 54.04, 0.17116, 44, -118.28, 50.19, 0.06708, 3, 41, -6.73, 31.9, 0.75601, 42, -52.5, 38.32, 0.18032, 43, -101.26, 50.71, 0.06367], "hull": 17}}, "tie_10": {"tie_10": {"type": "mesh", "uvs": [0.06801, 0, 0, 0.11831, 0.08835, 0.15962, 0.04006, 0.30568, 0.08835, 0.4716, 0.18747, 0.61218, 0.26881, 0.73206, 0.34506, 0.8377, 0.38064, 0.99338, 0.74918, 0.98226, 0.76951, 0.82976, 0.82922, 0.72607, 0.90547, 0.60601, 0.96139, 0.47371, 0.97155, 0.32151, 0.95376, 0.14754, 0.86989, 0], "triangles": [8, 7, 9, 9, 7, 10, 7, 6, 10, 10, 6, 11, 6, 5, 11, 11, 5, 12, 12, 5, 13, 13, 5, 4, 13, 4, 14, 4, 3, 14, 3, 2, 14, 2, 15, 14, 1, 0, 2, 2, 16, 15, 2, 0, 16], "vertices": [2, 55, -6.55, -51.55, 0.98083, 56, -96.36, -50.42, 0.01917, 3, 55, 38.67, -60.83, 0.89283, 56, -51.25, -60.23, 0.10461, 57, -130.92, -61.95, 0.00256, 4, 55, 54.78, -50.62, 0.68743, 56, -35.02, -50.21, 0.23774, 57, -114.81, -51.72, 0.07473, 58, -178.75, -47.95, 0.0001, 5, 55, 110.71, -57.79, 0.38719, 56, 20.82, -58.04, 0.35471, 57, -58.87, -58.82, 0.21823, 58, -122.97, -56.23, 0.03681, 59, -185.84, -57.32, 0.00306, 5, 55, 174.55, -53.56, 0.15052, 56, 84.7, -54.56, 0.30212, 57, 4.96, -54.5, 0.35861, 58, -59.07, -53.27, 0.12962, 59, -121.97, -53.51, 0.05913, 5, 55, 228.81, -43, 0.02377, 56, 139.09, -44.64, 0.16792, 57, 59.2, -43.87, 0.35976, 58, -4.61, -43.78, 0.22224, 59, -67.64, -43.31, 0.2263, 4, 56, 185.44, -36.56, 0.04163, 57, 105.45, -35.18, 0.21828, 58, 41.81, -36.07, 0.24163, 59, -21.33, -34.99, 0.49846, 4, 56, 226.31, -28.89, 0.00012, 57, 146.22, -26.98, 0.07533, 58, 82.74, -28.73, 0.14883, 59, 19.5, -27.12, 0.77572, 3, 57, 206.08, -24.1, 0.00201, 58, 142.66, -27.12, 0.13577, 59, 79.39, -24.72, 0.86222, 3, 57, 202.84, 20.21, 0.00089, 58, 140.35, 17.25, 0.26955, 59, 76.5, 19.62, 0.72956, 4, 56, 225.12, 22.12, 1e-05, 57, 144.35, 24.02, 0.0692, 58, 81.96, 22.29, 0.5031, 59, 18.05, 23.89, 0.4277, 4, 56, 185.59, 30.73, 0.03369, 57, 104.71, 32.1, 0.21454, 58, 42.5, 31.21, 0.57774, 59, -21.53, 32.29, 0.17402, 5, 55, 228.56, 43.19, 0.01284, 56, 139.85, 41.55, 0.16182, 57, 58.83, 42.32, 0.36193, 58, -3.15, 42.4, 0.43195, 59, -67.32, 42.88, 0.03146, 4, 55, 177.93, 51.15, 0.12813, 56, 89.32, 50.1, 0.29859, 57, 8.2, 50.21, 0.37451, 58, -53.61, 51.36, 0.19877, 4, 55, 119.54, 53.81, 0.35453, 56, 30.96, 53.44, 0.36801, 57, -50.2, 52.79, 0.233, 58, -111.94, 55.17, 0.04447, 4, 55, 52.7, 53.31, 0.66635, 56, -35.88, 53.74, 0.24855, 57, -117.03, 52.21, 0.08471, 58, -178.77, 56.01, 0.00039, 3, 55, -4.18, 44.64, 0.88321, 56, -92.86, 45.74, 0.11295, 57, -173.91, 43.47, 0.00383], "hull": 17}}, "tie_11": {"tie_11": {"type": "mesh", "uvs": [0.49084, 0, 0.41056, 0.11143, 0.3636, 0.26955, 0.34286, 0.42824, 0.31012, 0.56886, 0.26219, 0.69873, 0.20903, 0.7854, 0.14491, 0.82704, 0.04833, 0.8419, 0, 0.86758, 1e-05, 0.90204, 0.05629, 0.9263, 0.15261, 0.94709, 0.27798, 0.97123, 0.43794, 0.98304, 0.48237, 0.96424, 0.53915, 0.98303, 0.60309, 0.98691, 0.67153, 0.9651, 0.75754, 0.94206, 0.80137, 0.95776, 0.83234, 0.95936, 0.89234, 0.94053, 0.93155, 0.94779, 1, 0.92084, 1, 0.86758, 0.93008, 0.81182, 0.84773, 0.72274, 0.80907, 0.57947, 0.79209, 0.43754, 0.78435, 0.26821, 0.73159, 0.11077, 0.70064, 0, 0.52529, 0.72408, 0.5049, 0.82921, 0.54577, 0.5872, 0.56466, 0.44065, 0.57598, 0.27147, 0.57906, 0.11394, 0.65947, 0.11136, 0.67221, 0.27123, 0.6807, 0.44141, 0.68852, 0.59189, 0.69177, 0.72819, 0.69376, 0.83217, 0.49225, 0.11461, 0.47241, 0.27401, 0.45718, 0.4371, 0.43123, 0.58187, 0.40116, 0.71404, 0.37359, 0.81493, 0.72329, 0.27056, 0.74047, 0.44028, 0.75335, 0.58978, 0.77267, 0.72485, 0.81051, 0.82764], "triangles": [18, 44, 19, 22, 21, 55, 55, 21, 20, 24, 23, 25, 20, 19, 55, 25, 23, 22, 22, 55, 26, 22, 26, 25, 44, 33, 43, 43, 35, 42, 43, 33, 35, 19, 44, 55, 44, 54, 55, 44, 43, 54, 55, 27, 26, 55, 54, 27, 43, 53, 54, 43, 42, 53, 54, 28, 27, 54, 53, 28, 42, 36, 41, 42, 35, 36, 42, 52, 53, 28, 53, 52, 42, 41, 52, 28, 52, 29, 37, 40, 41, 52, 41, 51, 41, 40, 51, 29, 52, 30, 52, 51, 30, 40, 39, 51, 51, 31, 30, 15, 14, 50, 16, 15, 34, 13, 7, 6, 14, 13, 50, 13, 6, 50, 33, 44, 34, 15, 50, 34, 11, 7, 12, 13, 12, 7, 11, 9, 8, 11, 8, 7, 11, 10, 9, 50, 49, 34, 34, 49, 33, 6, 5, 50, 50, 5, 49, 35, 33, 48, 33, 49, 48, 5, 4, 49, 49, 4, 48, 36, 35, 47, 35, 48, 47, 4, 3, 48, 48, 3, 47, 47, 46, 36, 36, 46, 37, 46, 47, 2, 47, 3, 2, 37, 46, 45, 45, 46, 1, 46, 2, 1, 44, 18, 17, 34, 17, 16, 44, 17, 34, 36, 37, 41, 40, 37, 39, 37, 38, 39, 37, 45, 38, 51, 39, 31, 38, 45, 0, 32, 39, 38, 0, 45, 1, 39, 32, 31, 32, 38, 0], "vertices": [2, 39, -27.89, -66.65, 0.84106, 78, -139.64, -11.88, 0.15894, 2, 39, 40.14, -106.65, 0.35565, 78, -71, -50.84, 0.64435, 1, 78, 25.06, -71.46, 1, 2, 78, 120.94, -78.49, 0.97091, 79, -67.43, -77.8, 0.02909, 3, 40, 73.73, -152.4, 0, 78, 206.18, -92.16, 0.40235, 79, 3.09, -92.2, 0.59765, 4, 40, 152.41, -175.59, 2e-05, 78, 285.27, -113.94, 0.02356, 79, 68.44, -114.65, 0.97485, 80, -6.41, -171.75, 0.00157, 4, 40, 205.15, -202.03, 5e-05, 79, 112.34, -140.61, 0.88704, 80, 43.68, -162.18, 0.02491, 82, 1.74, -28.29, 0.088, 5, 40, 230.91, -234.71, 6e-05, 79, 133.92, -173.05, 0.65911, 80, 81.33, -172.24, 0.03299, 82, 42.83, -21.68, 0.16, 83, -9.28, -27.43, 0.14784, 4, 40, 240.9, -284.54, 7e-05, 79, 142.58, -222.78, 0.64016, 80, 120.71, -203.82, 0.03176, 83, 41.41, -23.89, 0.328, 4, 40, 256.88, -309.24, 6e-05, 79, 156, -247.34, 0.54153, 80, 147.02, -213.36, 0.02641, 83, 67.96, -11.2, 0.432, 4, 40, 277.62, -308.8, 7e-05, 79, 173.18, -246.71, 0.57193, 80, 159.49, -201.53, 0.028, 83, 70.17, 9.43, 0.4, 4, 40, 291.61, -279.35, 8e-05, 79, 184.55, -217.13, 0.69945, 80, 148.45, -171.83, 0.03647, 83, 42.75, 27.07, 0.264, 5, 40, 303.08, -229.2, 6e-05, 79, 193.66, -166.88, 0.61684, 80, 122.06, -128.1, 0.04581, 82, 73.11, 44.06, 0.128, 83, -5.53, 44.85, 0.20928, 4, 40, 316.25, -163.98, 7e-05, 79, 204.08, -101.53, 0.68076, 80, 86.65, -72.2, 0.13518, 82, 22.52, 87.29, 0.184, 2, 79, 207.89, -18.49, 0.07627, 80, 34.59, -7.4, 0.92373, 3, 40, 305.79, -83.29, 0.18249, 79, 197.54, 8.78, 0.03913, 80, 8.79, 6.21, 0.77837, 2, 40, 320.51, -28.57, 0.37477, 80, -1.06, 31.04, 0.62523, 3, 40, 322.16, 4.6, 0.96585, 80, -22.16, 56.66, 0.00876, 77, -30.05, -50.39, 0.02539, 3, 40, 308.29, 39.76, 0.27138, 76, 237.12, -4.4, 0.02071, 77, -2.1, -24.94, 0.70791, 3, 40, 291.18, 83.97, 0.11145, 76, 223.67, 40.2, 0.02575, 77, 34.11, 4.37, 0.8628, 2, 76, 232.91, 62.87, 0.02927, 77, 58.59, 4.39, 0.97073, 3, 76, 233.99, 78.91, 0.0738, 77, 73.85, 9.46, 0.7502, 84, 76.44, -63.54, 0.176, 3, 76, 222.78, 110.04, 0.08273, 77, 98.42, 31.61, 0.66357, 84, 84.25, -31.4, 0.25371, 3, 76, 227.23, 130.33, 0.08851, 77, 118.9, 35.16, 0.60749, 84, 99.15, -16.92, 0.304, 3, 76, 211.14, 165.85, 0.09373, 77, 145.69, 63.48, 0.55427, 84, 105.32, 21.58, 0.352, 3, 76, 179.08, 165.98, 0.12218, 77, 133.69, 93.21, 0.56582, 84, 78.64, 39.37, 0.312, 3, 76, 145.37, 129.89, 0.32511, 77, 87.54, 110.78, 0.54689, 84, 30.62, 27.85, 0.128, 2, 76, 91.58, 87.44, 0.8315, 77, 27.9, 144.53, 0.1685, 3, 75, 203.56, 67.99, 0.08234, 76, 5.25, 67.76, 0.91634, 77, -22.96, 217, 0.00132, 2, 75, 117.98, 60.73, 0.89024, 76, -80.22, 59.3, 0.10976, 1, 75, 15.99, 58.56, 1, 2, 39, 35.85, 59.59, 0.78305, 75, -79.27, 32.94, 0.21695, 2, 39, -30.44, 42, 0.99757, 75, -146.23, 18.11, 0.00243, 2, 40, 164.82, -39.01, 0.37173, 79, 77.68, 22.03, 0.62827, 2, 40, 228.31, -48.24, 0.18426, 79, 130.34, 13.38, 0.81574, 3, 40, 82.2, -30.13, 0.54748, 78, 212.49, 30.24, 0.10486, 79, 9.17, 30.15, 0.34766, 3, 39, 236.41, -22.21, 0.48927, 40, -6.2, -22.19, 0.20925, 78, 123.95, 36.61, 0.30148, 2, 39, 134.45, -18.73, 0.74735, 78, 21.96, 38.52, 0.25265, 2, 39, 39.61, -19.35, 0.91658, 78, -72.87, 36.44, 0.08342, 2, 39, 37.08, 22.25, 0.93026, 75, -79.59, -4.42, 0.06974, 1, 75, 16.75, 0.45, 1, 2, 75, 119.27, 3, 0.99707, 76, -78.12, 1.58, 0.00293, 1, 76, 12.48, 5.28, 1, 2, 76, 94.54, 6.64, 0.9968, 77, -45.78, 111.23, 0.0032, 2, 76, 157.14, 7.43, 0.9783, 77, -21.38, 53.58, 0.0217, 2, 39, 41.07, -64.3, 0.55651, 78, -70.72, -8.48, 0.44349, 1, 78, 25.56, -15.03, 1, 2, 78, 123.98, -19.11, 0.99953, 79, -64.5, -18.44, 0.00047, 3, 40, 80.24, -89.52, 0, 78, 211.58, -29.17, 0.30637, 79, 8, -29.25, 0.69363, 3, 40, 160.12, -103.42, 0, 78, 291.69, -41.65, 0.00497, 79, 74.28, -42.42, 0.99503, 3, 40, 221.14, -116.43, 2e-05, 79, 124.93, -54.86, 0.9923, 80, -3.59, -89.55, 0.00768, 1, 75, 16.83, 26.91, 1, 2, 75, 119.14, 33.97, 0.92651, 76, -78.68, 32.55, 0.07349, 3, 75, 209.25, 39.02, 0.03601, 76, 11.34, 38.87, 0.9633, 77, -47.4, 200.44, 0.00069, 2, 76, 92.7, 48.56, 0.92554, 77, -7.67, 128.79, 0.07446, 3, 76, 154.65, 67.92, 0.57153, 77, 33.67, 78.75, 0.37247, 84, 4.19, -28.97, 0.056], "hull": 33}}, "tie_12": {"tie_12": {"type": "mesh", "uvs": [0.36003, 0.30951, 0.24305, 0.21596, 0.14284, 0.10756, 0.06955, 0.00842, 0.00551, 0.00281, 0.00331, 0.11946, 0.04496, 0.26764, 0.12735, 0.44162, 0.24664, 0.57128, 0.26878, 0.6659, 0.30447, 0.73439, 0.3029, 0.83372, 0.30318, 0.89045, 0.31586, 0.96669, 0.37465, 0.96656, 0.38384, 0.8938, 0.39812, 0.85136, 0.43616, 0.91723, 0.51098, 0.98866, 0.55925, 0.99018, 0.61549, 0.93546, 0.65265, 0.88284, 0.66, 0.97305, 0.68745, 0.99999, 0.7241, 0.98668, 0.73076, 0.88871, 0.74088, 0.83622, 0.73096, 0.72136, 0.76896, 0.67148, 0.77696, 0.59907, 0.89262, 0.48647, 0.95693, 0.33278, 0.99999, 0.17739, 0.99999, 0.09068, 0.9527, 0.07486, 0.85903, 0.19289, 0.77203, 0.26694, 0.68238, 0.31292, 0.56607, 0.20907, 0.50913, 0.2058, 0.36463, 0.72251, 0.37052, 0.61233, 0.43497, 0.52363, 0.45181, 0.44542, 0.48295, 0.43899, 0.50821, 0.4947, 0.54441, 0.52685, 0.58397, 0.49792, 0.59997, 0.43899, 0.6227, 0.44113, 0.63953, 0.5472, 0.69425, 0.59435, 0.69425, 0.69185, 0.67826, 0.79899, 0.42318, 0.57935, 0.49642, 0.61149, 0.58566, 0.61685, 0.65216, 0.59435, 0.5653, 0.68004, 0.54444, 0.76634, 0.51121, 0.74313, 0.501, 0.78074, 0.53916, 0.80492, 0.57564, 0.77708, 0.52005, 0.67908, 0.40133, 0.62844, 0.43437, 0.61863, 0.46522, 0.62276, 0.4223, 0.67149, 0.45497, 0.69754, 0.48455, 0.69113, 0.58515, 0.67781, 0.60794, 0.64976, 0.63219, 0.63872, 0.67615, 0.65735, 0.66076, 0.69181, 0.63583, 0.70603, 0.60851, 0.7019, 0.48772, 0.84957, 0.5207, 0.83135, 0.55527, 0.83346, 0.58883, 0.85998, 0.51591, 0.89061, 0.55372, 0.89257, 0.52204, 0.8617, 0.55268, 0.8621, 0.4326, 0.75888, 0.44989, 0.84989, 0.48829, 0.91266, 0.53641, 0.93989, 0.60108, 0.9007, 0.64145, 0.77356, 0.50879, 0.66534, 0.49079, 0.64052, 0.65744, 0.64116, 0.54268, 0.683, 0.54333, 0.5725, 0.46391, 0.58965, 0.61993, 0.60048, 0.4764, 0.54411, 0.60245, 0.54649, 0.31067, 0.4645, 0.18334, 0.33087, 0.08761, 0.19697, 0.03614, 0.06939, 0.7459, 0.45388, 0.84577, 0.36225, 0.92179, 0.26692, 0.97827, 0.14903, 0.42144, 0.35485, 0.53941, 0.33329, 0.6465, 0.36254, 0.37118, 0.46726, 0.32642, 0.55204, 0.31914, 0.63947, 0.69175, 0.47124, 0.72818, 0.55337, 0.54502, 0.42401, 0.49058, 0.37244, 0.58969, 0.37973, 0.53763, 0.27415, 0.39383, 0.56273], "triangles": [106, 107, 31, 106, 35, 107, 31, 107, 32, 107, 108, 32, 107, 35, 108, 35, 34, 108, 32, 108, 33, 108, 34, 33, 105, 106, 30, 30, 106, 31, 105, 36, 106, 106, 36, 35, 6, 103, 102, 6, 5, 103, 103, 2, 1, 5, 104, 103, 103, 104, 2, 5, 4, 104, 104, 3, 2, 104, 4, 3, 7, 102, 101, 0, 102, 1, 7, 6, 102, 102, 103, 1, 23, 22, 24, 24, 22, 25, 22, 21, 25, 26, 25, 53, 14, 13, 15, 15, 13, 12, 16, 15, 11, 11, 15, 12, 101, 102, 0, 29, 105, 30, 105, 37, 36, 116, 105, 29, 25, 21, 53, 11, 10, 40, 40, 16, 11, 28, 51, 29, 29, 51, 116, 116, 115, 105, 50, 115, 116, 105, 115, 111, 21, 91, 53, 53, 91, 52, 53, 52, 27, 8, 101, 113, 112, 101, 0, 10, 9, 114, 40, 114, 41, 113, 101, 112, 41, 113, 121, 18, 89, 19, 20, 19, 89, 20, 89, 90, 90, 89, 83, 17, 88, 18, 18, 88, 89, 88, 82, 89, 89, 82, 83, 83, 84, 85, 84, 83, 82, 20, 90, 21, 17, 87, 88, 87, 78, 88, 17, 16, 87, 88, 78, 82, 83, 81, 90, 21, 90, 81, 76, 91, 77, 77, 91, 63, 83, 85, 81, 82, 78, 84, 85, 80, 81, 80, 85, 79, 78, 79, 84, 85, 84, 79, 80, 63, 81, 63, 91, 81, 16, 86, 87, 78, 87, 61, 61, 87, 86, 61, 86, 69, 78, 61, 79, 60, 61, 69, 79, 62, 80, 80, 62, 63, 79, 61, 62, 62, 59, 63, 62, 61, 59, 61, 60, 59, 60, 69, 70, 59, 58, 63, 77, 58, 71, 77, 63, 58, 91, 76, 75, 60, 95, 59, 59, 95, 58, 86, 68, 69, 60, 64, 95, 60, 70, 64, 64, 70, 92, 40, 65, 68, 76, 72, 73, 75, 73, 94, 75, 76, 73, 71, 72, 77, 76, 77, 72, 69, 68, 67, 70, 69, 93, 67, 68, 66, 75, 94, 74, 70, 93, 92, 93, 69, 67, 56, 58, 95, 71, 58, 56, 56, 95, 96, 71, 56, 72, 64, 92, 95, 68, 65, 66, 93, 55, 92, 95, 92, 96, 92, 55, 96, 72, 98, 73, 72, 56, 98, 73, 57, 94, 93, 67, 55, 73, 98, 57, 41, 121, 65, 65, 54, 66, 65, 121, 54, 66, 97, 67, 67, 97, 55, 66, 54, 97, 56, 100, 98, 56, 96, 100, 97, 99, 55, 55, 99, 96, 98, 50, 57, 98, 100, 50, 99, 97, 42, 97, 54, 42, 54, 121, 42, 96, 99, 46, 96, 46, 100, 46, 99, 45, 42, 121, 112, 46, 47, 100, 50, 100, 47, 49, 50, 47, 49, 47, 48, 99, 42, 45, 45, 43, 44, 43, 45, 42, 47, 46, 117, 42, 112, 43, 46, 45, 117, 47, 117, 48, 45, 44, 117, 43, 112, 109, 109, 112, 0, 44, 43, 118, 111, 49, 119, 44, 118, 117, 118, 43, 109, 117, 119, 48, 49, 48, 119, 118, 110, 117, 117, 110, 119, 111, 119, 120, 110, 118, 120, 38, 37, 111, 120, 118, 109, 120, 109, 39, 120, 119, 110, 111, 120, 38, 109, 0, 39, 120, 39, 38, 121, 113, 112, 114, 8, 113, 114, 113, 41, 9, 8, 114, 40, 41, 65, 40, 68, 86, 16, 40, 86, 10, 114, 40, 21, 81, 91, 52, 91, 75, 75, 74, 52, 53, 27, 26, 115, 49, 111, 115, 50, 49, 57, 50, 51, 94, 57, 51, 27, 52, 28, 28, 52, 51, 52, 74, 51, 74, 94, 51, 51, 50, 116, 111, 37, 105, 8, 7, 101], "vertices": [2, 6, 91.76, 33.62, 0.912, 7, 5.43, -23.46, 0.088, 3, 6, 106.03, 56.63, 0.00198, 7, 32.32, -20.23, 0.71097, 8, 0.56, -21.46, 0.28705, 2, 7, 58.09, -20.84, 0.00779, 8, 24.83, -12.76, 0.99221, 1, 8, 45.23, -7.93, 1, 1, 8, 52.24, 2.52, 1, 1, 8, 36.88, 11.85, 1, 2, 7, 57.48, 10.39, 0.01721, 8, 13.03, 16.16, 0.98279, 3, 11, -53.67, -46.58, 0.00021, 7, 28.07, 21.05, 0.97428, 8, -18.24, 15.53, 0.0255, 2, 6, 51.32, 55.61, 0.92699, 7, -2.67, 21.85, 0.07301, 1, 6, 36.77, 51.19, 1, 2, 6, 26.27, 44.14, 0.89201, 11, -11.07, -8.85, 0.10799, 2, 11, 4.21, -8.1, 0.99841, 7, -36.68, 46.31, 0.00159, 1, 11, 12.92, -7.44, 1, 1, 11, 24.46, -4.16, 1, 1, 11, 23.65, 7.34, 1, 2, 6, 1.8, 28.44, 0.04188, 11, 12.35, 8.36, 0.95812, 2, 6, 8.36, 25.68, 0.91708, 11, 5.63, 10.71, 0.08292, 1, 6, -1.75, 18.17, 1, 1, 6, -12.66, 3.44, 1, 1, 6, -12.84, -6.02, 1, 1, 6, -4.35, -17, 1, 2, 6, 3.79, -24.23, 0.90972, 12, 6.51, -8.5, 0.09028, 2, 6, -10.09, -25.75, 3e-05, 12, 20.43, -7.37, 0.99997, 1, 12, 24.69, -2.08, 1, 1, 12, 22.8, 5.15, 1, 1, 12, 7.75, 6.78, 1, 2, 12, -0.29, 8.94, 0.99175, 9, -32.88, -46.19, 0.00825, 2, 6, 28.74, -39.44, 0.92, 12, -18.02, 7.39, 0.08, 1, 6, 36.47, -46.85, 1, 2, 6, 47.63, -48.35, 0.91464, 9, -5.87, -20.61, 0.08536, 3, 12, -53.49, 39.86, 0.00057, 9, 22.66, -19.71, 0.9926, 10, -17.92, -13.37, 0.00683, 2, 9, 46.68, -7.8, 0.03393, 10, 8.89, -12.77, 0.96607, 1, 10, 33.88, -8.4, 1, 1, 10, 45.52, -1.86, 1, 1, 10, 43.11, 7.41, 1, 2, 9, 43.62, 20.89, 0.0744, 10, 18.27, 14.52, 0.9256, 3, 6, 98.77, -47.09, 0.03155, 9, 23.12, 21.54, 0.75457, 10, -0.03, 23.79, 0.21388, 3, 6, 91.59, -29.56, 0.90932, 9, 4.71, 26.01, 0.08359, 10, -14.81, 35.64, 0.0071, 1, 6, 107.45, -6.68, 1, 1, 6, 107.89, 4.49, 1, 2, 6, 28.16, 32.36, 0.92, 13, 3.09, 37.11, 0.08, 2, 6, 45.14, 31.3, 0.92, 13, 20.06, 36.05, 0.08, 2, 6, 58.87, 18.74, 0.92, 13, 33.79, 23.49, 0.08, 2, 6, 70.93, 15.51, 0.92, 13, 45.86, 20.26, 0.08, 2, 6, 71.95, 9.41, 0.88, 13, 46.88, 14.16, 0.12, 2, 6, 63.4, 4.41, 0.88, 13, 38.33, 9.17, 0.12, 2, 6, 58.49, -2.71, 0.88, 13, 33.42, 2.04, 0.12, 2, 6, 62.99, -10.44, 0.88, 13, 37.92, -5.69, 0.12, 2, 6, 72.08, -13.52, 0.88, 13, 47.01, -8.77, 0.12, 2, 6, 71.78, -17.98, 0.92, 13, 46.71, -13.23, 0.08, 2, 6, 55.46, -21.37, 0.92, 13, 30.39, -16.62, 0.08, 2, 6, 48.26, -32.14, 0.88, 13, 23.19, -27.39, 0.12, 2, 6, 33.25, -32.22, 0.92, 13, 8.18, -27.47, 0.08, 2, 6, 16.73, -29.18, 0.92, 13, -8.34, -24.43, 0.08, 2, 6, 50.27, 21.01, 0.88, 13, 25.2, 25.76, 0.12, 2, 6, 45.4, 6.62, 0.84, 13, 20.33, 11.37, 0.16, 2, 6, 44.68, -10.87, 0.88, 13, 19.61, -6.12, 0.12, 2, 6, 48.22, -23.89, 0.88, 13, 23.14, -19.14, 0.12, 2, 6, 34.92, -6.94, 0.92, 13, 9.85, -2.19, 0.08, 2, 6, 21.61, -2.92, 0.84, 13, -3.46, 1.83, 0.16, 2, 6, 25.15, 3.61, 0.92, 13, 0.08, 8.36, 0.08, 3, 6, 19.35, 5.58, 0.91815, 7, -61.71, 15.54, 0.00185, 13, -5.73, 10.33, 0.08, 2, 6, 15.66, -1.92, 0.92, 13, -9.41, 2.83, 0.08, 2, 6, 19.99, -9.05, 0.92, 13, -5.08, -4.3, 0.08, 2, 6, 35.02, 1.93, 0.92, 13, 9.95, 6.68, 0.08, 2, 6, 42.69, 25.25, 0.92, 13, 17.62, 30, 0.08, 2, 6, 44.24, 18.78, 0.92, 13, 19.16, 23.53, 0.08, 2, 6, 43.63, 12.73, 0.92, 13, 18.56, 17.48, 0.08, 2, 6, 36.08, 21.1, 0.92, 13, 11.01, 25.85, 0.08, 2, 6, 32.11, 14.67, 0.92, 13, 7.04, 19.42, 0.08, 2, 6, 33.13, 8.88, 0.92, 13, 8.06, 13.63, 0.08, 2, 6, 35.29, -10.83, 0.92, 13, 10.22, -6.08, 0.08, 2, 6, 39.63, -15.27, 0.92, 13, 14.56, -10.52, 0.08, 2, 6, 41.36, -20.01, 0.92, 13, 16.29, -15.26, 0.08, 2, 6, 38.54, -28.64, 0.92, 13, 13.47, -23.89, 0.08, 2, 6, 33.22, -25.66, 0.92, 13, 8.15, -20.91, 0.08, 2, 6, 31, -20.78, 0.92, 13, 5.93, -16.03, 0.08, 2, 6, 31.61, -15.42, 0.92, 13, 6.53, -10.67, 0.08, 2, 6, 8.73, 8.12, 0.92, 13, -16.34, 12.87, 0.08, 2, 6, 11.57, 1.67, 0.92, 13, -13.5, 6.42, 0.08, 2, 6, 11.29, -5.11, 0.92, 13, -13.79, -0.36, 0.08, 2, 6, 7.24, -11.7, 0.92, 13, -17.83, -6.95, 0.08, 2, 6, 2.44, 2.56, 0.92, 13, -22.63, 7.31, 0.08, 2, 6, 2.18, -4.85, 0.92, 13, -22.89, -0.1, 0.08, 2, 6, 6.9, 1.38, 0.92, 13, -18.17, 6.13, 0.08, 2, 6, 6.87, -4.62, 0.92, 13, -18.2, 0.13, 0.08, 2, 6, 22.64, 19, 0.88, 13, -2.44, 23.75, 0.12, 3, 6, 8.64, 15.54, 0.87971, 7, -60.63, 30.12, 0.00029, 13, -16.43, 20.29, 0.12, 2, 6, -0.98, 7.95, 0.88, 13, -26.06, 12.7, 0.12, 2, 6, -5.12, -1.5, 0.88, 13, -30.2, 3.25, 0.12, 2, 6, 0.98, -14.14, 0.88, 13, -24.09, -9.39, 0.12, 2, 6, 20.61, -21.94, 0.88, 13, -4.47, -17.19, 0.12, 2, 6, 37.13, 4.15, 0.92, 13, 12.05, 8.9, 0.08, 2, 6, 40.93, 7.7, 0.92, 13, 15.86, 12.45, 0.08, 2, 6, 41.01, -24.96, 0.92, 13, 15.94, -20.21, 0.08, 2, 6, 34.44, -2.51, 0.88, 13, 9.37, 2.24, 0.12, 2, 6, 51.46, -2.54, 0.92, 13, 26.39, 2.21, 0.08, 2, 6, 48.73, 13.02, 0.88, 13, 23.66, 17.77, 0.12, 2, 6, 47.24, -17.57, 0.88, 13, 22.16, -12.82, 0.12, 2, 6, 55.76, 10.61, 0.92, 13, 30.69, 15.36, 0.08, 2, 6, 55.53, -14.1, 0.92, 13, 30.46, -9.35, 0.08, 2, 6, 67.83, 43.16, 0.92, 13, 42.76, 47.91, 0.08, 2, 7, 30.28, 0.89, 0.92, 13, 63.2, 72.98, 0.08, 2, 8, 18.3, 3.49, 0.92, 13, 83.71, 91.86, 0.08, 2, 8, 40.36, 2.43, 0.92, 13, 103.3, 102.06, 0.08, 2, 6, 69.95, -42.14, 0.92, 13, 44.88, -37.39, 0.08, 4, 6, 84.17, -61.63, 0.00011, 9, 26.33, 1.19, 0.90329, 10, -5.74, 4, 0.0166, 13, 59.1, -56.88, 0.08, 2, 10, 14.35, -1.79, 0.92, 13, 73.87, -71.7, 0.08, 2, 10, 35.6, -2.55, 0.92, 13, 92.08, -82.66, 0.08, 2, 6, 84.84, 21.54, 0.88, 13, 59.77, 26.29, 0.12, 2, 6, 88.29, -1.56, 0.88, 13, 63.22, 3.19, 0.12, 2, 6, 83.91, -22.57, 0.88, 13, 58.84, -17.82, 0.12, 2, 6, 67.48, 31.3, 0.88, 13, 42.4, 36.05, 0.12, 2, 6, 54.37, 39.99, 0.88, 13, 29.3, 44.74, 0.12, 2, 6, 40.9, 41.35, 0.88, 13, 15.83, 46.1, 0.12, 2, 6, 67.22, -31.54, 0.88, 13, 42.15, -26.79, 0.12, 2, 6, 54.61, -38.75, 0.88, 13, 29.54, -34, 0.12, 3, 6, 74.33, -2.74, 0.87995, 12, -64.6, -28.04, 5e-05, 13, 49.26, 2.01, 0.12, 2, 6, 82.21, 7.98, 0.88, 13, 57.14, 12.73, 0.12, 3, 6, 81.2, -11.46, 0.87999, 12, -71.23, -19.14, 1e-05, 13, 56.13, -6.7, 0.12, 2, 6, 97.4, -1.16, 0.92, 13, 72.33, 3.59, 0.08, 2, 6, 52.8, 26.77, 0.92, 13, 27.73, 31.52, 0.08], "hull": 40}}, "tie_13": {"tie_13": {"type": "mesh", "uvs": [0.33578, 0, 0.33781, 0.13844, 0.22571, 0.22728, 0, 0.2822, 0, 0.36014, 0.0556, 0.60204, 0.20759, 0.90924, 0.65442, 1, 0.90164, 0.91646, 0.90896, 0.80965, 1, 0.74181, 1, 0.62057, 0.94009, 0.49789, 0.86541, 0.38366, 0.83744, 0.29671, 0.74944, 0.26803, 0.62735, 0.15111, 0.62878, 0, 0.21752, 0.60947, 0.28097, 0.72199, 0.40787, 0.79979, 0.59645, 0.82479, 0.68634, 0.78173, 0.71278, 0.62336, 0.68634, 0.53585, 0.74098, 0.83452, 0.84144, 0.83035, 0.54358, 0.63448, 0.88198, 0.62197, 0.79157, 0.48378, 0.39626, 0.49056], "triangles": [8, 7, 25, 6, 20, 7, 28, 11, 10, 28, 12, 11, 9, 26, 28, 9, 28, 10, 28, 29, 12, 28, 23, 29, 29, 13, 12, 30, 18, 4, 8, 25, 26, 20, 21, 7, 25, 21, 22, 25, 7, 21, 8, 26, 9, 25, 22, 26, 28, 26, 22, 22, 23, 28, 22, 21, 27, 21, 20, 27, 22, 27, 23, 27, 24, 23, 27, 30, 24, 23, 24, 29, 29, 24, 15, 4, 2, 30, 2, 1, 30, 24, 30, 15, 30, 16, 15, 30, 1, 16, 13, 15, 14, 13, 29, 15, 4, 3, 2, 16, 1, 17, 1, 0, 17, 18, 5, 4, 6, 19, 20, 19, 5, 18, 19, 6, 5, 20, 19, 27, 19, 18, 27, 18, 30, 27], "vertices": [2, 4, 193.85, 19.5, 0.99998, 27, 129.33, -0.97, 2e-05, 1, 4, 166.12, 24.07, 1, 1, 4, 151.48, 44.88, 1, 1, 4, 146.79, 82.38, 1, 1, 4, 131.21, 85.13, 1, 1, 4, 81.3, 84.92, 1, 1, 3, 68.7, 73.87, 1, 1, 3, 52.58, 1.82, 1, 1, 3, 70.8, -37.17, 1, 2, 4, 16.03, -42.19, 0.96, 28, -40.81, 3.42, 0.04, 2, 4, 27.06, -58.94, 0.96, 28, -29.78, -13.32, 0.04, 2, 4, 51.29, -63.22, 0.96, 28, -5.55, -17.6, 0.04, 2, 4, 77.49, -58.12, 0.96, 28, 20.65, -12.5, 0.04, 1, 4, 102.4, -50.39, 1, 1, 4, 120.56, -49.05, 1, 1, 4, 128.75, -36.2, 1, 2, 4, 155.52, -21.1, 0.99569, 28, 98.68, 24.52, 0.00431, 1, 4, 185.69, -26.66, 1, 2, 4, 75.31, 59.67, 0.96, 27, 10.8, 39.2, 0.04, 2, 4, 51.05, 53.65, 0.96, 27, -13.47, 33.18, 0.04, 2, 4, 31.96, 36.41, 0.96, 27, -32.55, 15.94, 0.04, 2, 4, 21.71, 7.58, 0.96, 27, -42.8, -12.89, 0.04, 3, 4, 27.81, -8.11, 0.9216, 27, -36.7, -28.58, 0.0384, 28, -29.02, 37.51, 0.04, 3, 4, 58.74, -17.87, 0.9216, 27, -5.78, -38.34, 0.0384, 28, 1.9, 27.75, 0.04, 3, 4, 76.97, -16.8, 0.9216, 27, 12.45, -37.27, 0.0384, 28, 20.13, 28.82, 0.04, 2, 4, 15.74, -14.85, 0.96, 28, -41.1, 30.77, 0.04, 2, 4, 13.78, -30.82, 0.96, 28, -43.06, 14.79, 0.04, 2, 4, 61.23, 9.18, 0.8, 27, -3.29, -11.29, 0.2, 2, 4, 54.3, -44.58, 0.8, 28, -2.54, 1.04, 0.2, 2, 4, 84.44, -35.21, 0.96, 28, 27.6, 10.4, 0.04, 2, 4, 94.1, 27.31, 0.96, 27, 29.59, 6.84, 0.04], "hull": 18}}, "tie_14": {"tie_14": {"type": "mesh", "uvs": [0.04445, 0, 0, 0.06595, 0.00801, 0.11541, 0.21752, 0.48075, 0.3997, 1, 0.49306, 1, 1, 0.81709, 0.89613, 0.66129, 0.78682, 0.16747, 0.29722, 0.02156], "triangles": [9, 2, 1, 0, 9, 1, 3, 9, 8, 3, 2, 9, 3, 8, 7, 4, 3, 7, 5, 4, 7, 6, 5, 7], "vertices": [-11.44, -7.89, -1.92, -16.84, 6.86, -19.71, 77.66, -26.93, 173.71, -47.94, 177.41, -39.27, 166.23, 21.18, 135.46, 22.91, 46.66, 48.83, 2.28, 14.01], "hull": 10}}}}], "animations": {"idle": {"bones": {"zong": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 0.52, "curve": 0.25, "c3": 0.75}, {"time": 1.6667}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "y": 4.66, "curve": 0.25, "c3": 0.75}, {"time": 1.6667}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "y": 1.004, "curve": 0.25, "c3": 0.75}, {"time": 1.6667}]}, "tie_5": {"rotate": [{"angle": -0.77, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 0.38, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -0.77}], "translate": [{"x": -1.36, "y": -2.66, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 0.02, "y": 2.66, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": -1.36, "y": -2.66}]}, "tie_13": {"rotate": [{"angle": 0.18, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1333, "angle": 0.16, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": 0.36, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 1.6667, "angle": 0.18}], "translate": [{"x": 0.43, "y": 0.01, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "x": 4.43, "y": 0.07, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 1.6667, "x": 0.43, "y": 0.01}]}, "tie_14": {"rotate": [{"angle": -1.47, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.2667, "angle": -2.2, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": 0.37, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.6667, "angle": -1.47}]}, "tie_12": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 0.83, "curve": 0.25, "c3": 0.75}, {"time": 1.6667}], "translate": [{"x": -1.77, "y": 0.04, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 0.89, "y": -0.05, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": -1.77, "y": 0.04}]}, "tie_15": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -1.35, "curve": 0.25, "c3": 0.75}, {"time": 1.6667}]}, "tie_16": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -3.57, "curve": 0.25, "c3": 0.75}, {"time": 1.6667}]}, "tie_17": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 2.79, "curve": 0.25, "c3": 0.75}, {"time": 1.6667}]}, "tie_18": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 3.52, "curve": 0.25, "c3": 0.75}, {"time": 1.6667}]}, "tie_19": {"rotate": [{"angle": -10.01, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 4.06, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -10.01}]}, "tie_20": {"rotate": [{"angle": 10.76, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -10.09, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 10.76}]}, "tie_21": {"translate": [{"x": -2.29, "y": -21.38, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.2667, "x": -18.49, "y": -27.6, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "x": 38.59, "y": -5.67, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.6667, "x": -2.29, "y": -21.38}]}, "tie_4": {"rotate": [{"angle": 0.13, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 0.55, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 0.13}]}, "tie_6": {"rotate": [{"angle": 0.05, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1333, "angle": -0.49, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": 5.11, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 1.6667, "angle": 0.05}]}, "tie_3": {"rotate": [{"angle": -3.32, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 9.04, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -3.32}], "translate": [{"x": -3.13, "y": -0.63, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": -2.58, "y": 0.15, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": -3.13, "y": -0.63}]}, "tie_7": {"rotate": [{"angle": 0.34, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 8.03, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 1.6667, "angle": 0.34}]}, "tie_8": {"rotate": [{"angle": 2.57, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 19.77, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.6667, "angle": 2.57}]}, "tie_9": {"rotate": [{"angle": 0.2, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 4.66, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 1.6667, "angle": 0.2}]}, "tie_10": {"rotate": [{"angle": 1.36, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 10.45, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.6667, "angle": 1.36}]}, "tie_11": {"rotate": [{"angle": -0.23, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": -5.35, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 1.6667, "angle": -0.23}]}, "tie_22": {"rotate": [{"angle": -1.1, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -8.46, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.6667, "angle": -1.1}]}, "tie_23": {"rotate": [{"angle": -5.19, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1, "angle": -5.51, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 1.9, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 1.6667, "angle": -5.19}], "translate": [{"x": -0.03, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "x": -0.77, "y": 0.05, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 1.6667, "x": -0.03}]}, "tie_24": {"rotate": [{"angle": -5.88, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1667, "angle": -7.4, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 4.26, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.6667, "angle": -5.88}], "translate": [{"x": -0.03, "y": 0.6, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": -0.23, "y": 4.65, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.6667, "x": -0.03, "y": 0.6}]}, "tie_25": {"rotate": [{"angle": -12.54, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.2667, "angle": -19.39, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "angle": 8.9, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1.6667, "angle": -12.54}], "translate": [{"x": 0.05, "y": 0.56, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "x": 0.21, "y": 2.32, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1.6667, "x": 0.05, "y": 0.56}]}, "tie_26": {"rotate": [{"angle": -5.34, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.3333, "angle": -16.94, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 14.59, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.6667, "angle": -5.34}], "translate": [{"x": 0.52, "y": 1.63, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": 1.4, "y": 4.44, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.6667, "x": 0.52, "y": 1.63}]}, "tie_27": {"translate": [{"x": -2.69, "y": -1.88, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.2667, "x": -12.08, "y": 4.22, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "x": 21.01, "y": -17.28, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.6667, "x": -2.69, "y": -1.88}]}, "tie_28": {"translate": [{"x": -5.28, "y": -1.47, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.2667, "x": -18.47, "y": -4.16, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "x": 27.99, "y": 5.33, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.6667, "x": -5.28, "y": -1.47}]}, "tie_30": {"rotate": [{"angle": 2.08, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -0.89, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 2.08}]}, "tie_2": {"rotate": [{"angle": 1.44, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1333, "angle": 1.64, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": -0.42, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 1.6667, "angle": 1.44}]}, "tie_0": {"rotate": [{"angle": 1.49, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -2.22, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 1.49}]}, "tie_1": {"rotate": [{"angle": 2.65, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 2.65}]}, "tie_29": {"rotate": [{"angle": 7.27, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 7.27}]}, "tie_31": {"rotate": [{"angle": 3.38, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1, "angle": 3.61, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": -1.81, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 1.6667, "angle": 3.38}], "translate": [{"x": -2.12, "y": 1.18, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1, "x": -2.22, "y": 1.23, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 1.6667, "x": -2.12, "y": 1.18}]}, "tie_32": {"rotate": [{"angle": 4.25, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1667, "angle": 5.21, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -2.19, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.6667, "angle": 4.25}]}, "tie_33": {"rotate": [{"angle": 1.35, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.2667, "angle": 5.06, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "angle": -10.24, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1.6667, "angle": 1.35}]}, "tie_34": {"rotate": [{"angle": -1.89, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.3333, "angle": 5.68, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -14.91, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.6667, "angle": -1.89}]}, "zong3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -0.75, "curve": 0.25, "c3": 0.75}, {"time": 1.6667}]}, "tie_35": {"rotate": [{"angle": -0.94, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -0.94}]}, "tie_37": {"rotate": [{"angle": -0.4, "curve": 0.352, "c2": 0.65, "c3": 0.687}, {"time": 0.0667, "angle": -0.41, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "curve": 0.246, "c3": 0.723, "c4": 0.88}, {"time": 1.6667, "angle": -0.4}]}, "tie_38": {"rotate": [{"angle": -1.06, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.1, "angle": -1.21, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": 0.9, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 1.6667, "angle": -1.06}]}, "tie_39": {"rotate": [{"angle": -1.14, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1667, "angle": -1.4, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 0.58, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.6667, "angle": -1.14}]}, "tie_40": {"rotate": [{"angle": -0.53, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.2333, "angle": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": 4.08, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 1.6667, "angle": -0.53}]}, "tie_41": {"rotate": [{"angle": -0.2, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.2667, "angle": -1.76, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": 3.72, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.6667, "angle": -0.2}]}, "tie_42": {"rotate": [{"angle": 0.44, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.3333, "angle": -0.7, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 2.39, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.6667, "angle": 0.44}]}, "tie_44": {"rotate": [{"angle": 1.18, "curve": 0.352, "c2": 0.65, "c3": 0.687}, {"time": 0.0667, "angle": 1.21, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "curve": 0.246, "c3": 0.723, "c4": 0.88}, {"time": 1.6667, "angle": 1.18}]}, "tie_45": {"rotate": [{"angle": 1.44, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.1, "angle": 1.62, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": -0.93, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 1.6667, "angle": 1.44}]}, "tie_46": {"rotate": [{"angle": 1.01, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1667, "angle": 1.4, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -1.58, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.6667, "angle": 1.01}]}, "tie_47": {"rotate": [{"angle": 0.32, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.2333, "angle": 0.91, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": -2.01, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 1.6667, "angle": 0.32}]}, "tie_48": {"rotate": [{"angle": -0.2, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.2667, "angle": 0.69, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": -2.44, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.6667, "angle": -0.2}]}, "tie_49": {"rotate": [{"angle": 0.48, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.3333, "angle": 3.84, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -5.29, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.6667, "angle": 0.48}]}, "tie_50": {"rotate": [{"angle": 0.41, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1333, "angle": 0.55, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": -0.91, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 1.6667, "angle": 0.41}]}, "tie_51": {"rotate": [{"angle": 0.47, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.2667, "angle": 2.01, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": -3.42, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.6667, "angle": 0.47}]}, "tie_52": {"rotate": [{"angle": -0.03, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4333, "angle": 2.78, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": -2.85, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.6667, "angle": -0.03}]}, "tie_53": {"rotate": [{"angle": -0.07, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.5667, "angle": 3.71, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": -1.57, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.6667, "angle": -0.07}]}, "tie_54": {"rotate": [{"angle": -3.15, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.7, "angle": 14.24, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "angle": -5, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 1.6667, "angle": -3.15}]}, "tie_56": {"rotate": [{"angle": 0.58, "curve": 0.352, "c2": 0.65, "c3": 0.687}, {"time": 0.0667, "angle": 0.62, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -1.15, "curve": 0.246, "c3": 0.723, "c4": 0.88}, {"time": 1.6667, "angle": 0.58}]}, "tie_57": {"rotate": [{"angle": 1.28, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1, "angle": 1.4, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": -1.53, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 1.6667, "angle": 1.28}]}, "tie_58": {"rotate": [{"angle": 1.95, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.1, "angle": 2.26, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": -2.16, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 1.6667, "angle": 1.95}]}, "tie_59": {"rotate": [{"angle": 2.52, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1333, "angle": 3.11, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": -3, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 1.6667, "angle": 2.52}]}, "tie_60": {"rotate": [{"angle": 1.18, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1667, "angle": 2, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -4.25, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.6667, "angle": 1.18}]}, "tie_62": {"rotate": [{"angle": 1.25, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.1, "angle": 1.39, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": -0.73, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 1.6667, "angle": 1.25}]}, "tie_63": {"rotate": [{"angle": 1.45, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1667, "angle": 1.92, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -1.75, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.6667, "angle": 1.45}]}, "tie_64": {"rotate": [{"angle": 1.26, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.2333, "angle": 2.03, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": -1.78, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 1.6667, "angle": 1.26}]}, "tie_65": {"rotate": [{"angle": 0.85, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.2667, "angle": 2.05, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": -2.18, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.6667, "angle": 0.85}]}, "tie_66": {"rotate": [{"angle": 1.11, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.3333, "angle": 3.73, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -3.4, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.6667, "angle": 1.11}]}, "tie_67": {"rotate": [{"angle": -0.91, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.4, "angle": 2.1, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "angle": -4.5, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 1.6667, "angle": -0.91}]}, "tie_36b": {"rotate": [{"angle": -0.56, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": -5.84, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 1.6667, "angle": -0.56}], "scale": [{"y": 0.995, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "y": 0.943, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 1.6667, "y": 0.995}], "shear": [{"y": 0.27, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "y": 2.8, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 1.6667, "y": 0.27}]}, "tie_36c": {"rotate": [{"angle": -1.67, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": -5.88, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.6667, "angle": -1.67}], "scale": [{"y": 0.96, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "y": 0.86, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.6667, "y": 0.96}], "shear": [{"y": 2.04, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "y": 7.19, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.6667, "y": 2.04}]}}, "deform": {"default": {"tie_11": {"tie_11": [{"offset": 192, "vertices": [-0.07404, 3.53425, -0.08832, 3.53343, -0.07404, 3.53425, -0.08832, 3.53343, -0.07404, 3.53425, -0.13693, 3.53239, -0.08832, 3.53343, -0.08273, 3.53407, -0.07404, 3.53425, -0.13693, 3.53239, -0.08273, 3.53407, -0.13693, 3.53239, 0, 0, 0, 0, 0, 0, 0, 0, -0.08353, -4.66325, -0.08353, -4.66325, -0.01779, -4.66396, -0.01779, -4.66396, -0.01779, -4.66396, -4.32447, -1.74695, -0.01779, -4.66396, -4.32447, -1.74695], "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "offset": 192, "vertices": [-5.71449, -5.00955, -4.75033, -4.98707, -5.71449, -5.00955, -4.75033, -4.98707, -5.71449, -5.00955, -5.69119, -5.03607, -4.75033, -4.98707, -5.76773, -4.94821, -5.71449, -5.00955, -5.69119, -5.03607, -5.76773, -4.94821, -5.69119, -5.03607, 0, 0, 0, 0, 0, 0, 0, 0, -2.75992, 5.91488, -2.75992, 5.91488, -2.84302, 5.87539, -2.84302, 5.87539, -2.84302, 5.87539, 4.3643, 4.85343, -2.84302, 5.87539, 4.3643, 4.85343, -0.81534, -3.47205, -0.76199, -3.48414, -0.76199, -3.48414, -0.76199, -3.48414, -0.65598, -3.47748, -0.77817, -3.48057, -0.76199, -3.48414, -0.65598, -3.47748, -0.77817, -3.48057, -0.76199, -3.48414, -0.65598, -3.47748, -0.77817, -3.48057, -0.65598, -3.47748, 1.80785, -3.04218, 0.07907, 2.91792, 0.07907, 2.91792, 0.03793, 2.91874, 0.07907, 2.91792, 0.03793, 2.91874, 2.71642, 1.06844, 0.03793, 2.91874, 2.71642, 1.06844, 0.03793, 2.91874, 2.71642, 1.06844, 1.64114, 2.41394], "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "offset": 192, "vertices": [-0.07404, 3.53425, -0.08832, 3.53343, -0.07404, 3.53425, -0.08832, 3.53343, -0.07404, 3.53425, -0.13693, 3.53239, -0.08832, 3.53343, -0.08273, 3.53407, -0.07404, 3.53425, -0.13693, 3.53239, -0.08273, 3.53407, -0.13693, 3.53239, 0, 0, 0, 0, 0, 0, 0, 0, -0.08353, -4.66325, -0.08353, -4.66325, -0.01779, -4.66396, -0.01779, -4.66396, -0.01779, -4.66396, -4.32447, -1.74695, -0.01779, -4.66396, -4.32447, -1.74695]}]}}}}, "move": {"bones": {"zong": {"translate": [{"x": -3.8, "y": -21.05, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": 1.27, "y": -6.61, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "x": -1.27, "y": -30.71, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "x": 2.53, "y": -6.56, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "x": -3.8, "y": -21.05}]}, "tie_5": {"rotate": [{"angle": -0.44, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 0.61, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -0.29, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": 1.09, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": -0.44}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": -2.09, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "x": -2.09, "curve": 0.25, "c3": 0.75}, {"time": 1.2}]}, "tie_13": {"rotate": [{"angle": 0.54, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1, "angle": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 0.77, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": 0.53, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 0.89, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.2, "angle": 0.54}]}, "tie_14": {"rotate": [{"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 0.85, "curve": 0.25, "c3": 0.75}, {"time": 0.8}]}, "tie_12": {"rotate": [{"angle": 0.16, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -0.43, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 0.16, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": 0.63, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": 0.16}], "translate": [{"x": -1.12, "y": 0.03, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "x": -1.12, "y": 0.03, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "x": -1.12, "y": 0.03}]}, "tie_15": {"rotate": [{"angle": -3.64, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -1.58, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": -3.64}]}, "tie_16": {"rotate": [{"angle": -4.77, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -7.01, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": -4.77}]}, "tie_17": {"rotate": [{"angle": 4.42, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 3.08, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": 4.42}]}, "tie_18": {"rotate": [{"angle": 8.01, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 7.93, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": 8.01}]}, "tie_21": {"translate": [{"x": 4.77, "y": -1.99, "curve": 0.369, "c2": 0.47, "c3": 0.753}, {"time": 0.1667, "x": -9.31, "y": -0.03, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": 15.26, "y": -3.44, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "x": -9.31, "y": -0.03, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "x": 15.26, "y": -3.44, "curve": 0.254, "c3": 0.621, "c4": 0.47}, {"time": 1.2, "x": 4.77, "y": -1.99}]}, "tie_4": {"rotate": [{"angle": 1.19, "curve": 0.369, "c2": 0.47, "c3": 0.753}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 2.05, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": 2.07, "curve": 0.254, "c3": 0.621, "c4": 0.47}, {"time": 1.2, "angle": 1.19}]}, "tie_6": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 5.27, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": 3.61, "curve": 0.25, "c3": 0.75}, {"time": 1.2}]}, "tie_3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 4.26, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": 4.71, "curve": 0.25, "c3": 0.75}, {"time": 1.2}]}, "tie_7": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 4.26, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": 3.05, "curve": 0.25, "c3": 0.75}, {"time": 1.2}]}, "tie_8": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 5.05, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": 3.16, "curve": 0.25, "c3": 0.75}, {"time": 1.2}]}, "tie_9": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 2.43, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": 0.92, "curve": 0.25, "c3": 0.75}, {"time": 1.2}]}, "tie_10": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 1.63, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -0.61, "curve": 0.25, "c3": 0.75}, {"time": 1.2}]}, "tie_11": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -7.63, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -8.72, "curve": 0.25, "c3": 0.75}, {"time": 1.2}]}, "tie_22": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -2.44, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": 2.05, "curve": 0.25, "c3": 0.75}, {"time": 1.2}]}, "tie_23": {"rotate": [{"angle": -2.7, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.0667, "angle": -3.24, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 0.83, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -1.45, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": 0.26, "curve": 0.243, "c3": 0.674, "c4": 0.69}, {"time": 1.2, "angle": -2.7}]}, "tie_24": {"rotate": [{"angle": -2.93, "curve": 0.379, "c2": 0.53, "c3": 0.746}, {"time": 0.1333, "angle": -8.22, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 2.76, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": -6.78, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "angle": 4.19, "curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 1.2, "angle": -2.93}]}, "tie_25": {"rotate": [{"angle": 0.53, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2, "angle": -9.49, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 5.71, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": -15.08, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "angle": 4.51, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.2, "angle": 0.53}]}, "tie_26": {"rotate": [{"angle": 4.52, "curve": 0.293, "c2": 0.18, "c3": 0.755}, {"time": 0.2667, "angle": -11.33, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 5.03, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": -13.81, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 5.39, "curve": 0.302, "c3": 0.638, "c4": 0.36}, {"time": 1.2, "angle": 4.52}]}, "tie_27": {"translate": [{"x": -0.08, "y": 2.48, "curve": 0.369, "c2": 0.47, "c3": 0.753}, {"time": 0.1667, "x": -10.68, "y": -2.6, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": 5.4, "y": 8.96, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "x": -7.7, "y": -0.26, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "x": 7.81, "y": 6.25, "curve": 0.254, "c3": 0.621, "c4": 0.47}, {"time": 1.2, "x": -0.08, "y": 2.48}]}, "tie_28": {"translate": [{"x": -7.09, "y": -0.27, "curve": 0.369, "c2": 0.47, "c3": 0.753}, {"time": 0.1667, "x": -13.78, "y": 3.61, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": 3.16, "y": -9.25, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "x": -10.87, "y": 1.45, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "x": -2.11, "y": -3.15, "curve": 0.254, "c3": 0.621, "c4": 0.47}, {"time": 1.2, "x": -7.09, "y": -0.27}]}, "tie_30": {"rotate": [{"angle": 0.6, "curve": 0.369, "c2": 0.47, "c3": 0.753}, {"time": 0.1667, "angle": 1.94, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 2.09, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": -0.39, "curve": 0.254, "c3": 0.621, "c4": 0.47}, {"time": 1.2, "angle": 0.6}]}, "tie_2": {"rotate": [{"angle": 2.8, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 2.1, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -1.64, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": 2.8}]}, "tie_32": {"rotate": [{"angle": 4.94, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.0667, "angle": 6.77, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -3.15, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 4.33, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": -5.09, "curve": 0.243, "c3": 0.674, "c4": 0.69}, {"time": 1.2, "angle": 4.94}]}, "tie_33": {"rotate": [{"angle": -2.23, "curve": 0.379, "c2": 0.53, "c3": 0.746}, {"time": 0.1333, "angle": 6.42, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -16.03, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": 4.94, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "angle": -13.86, "curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 1.2, "angle": -2.23}]}, "tie_34": {"rotate": [{"angle": -3.86, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2, "angle": 12.79, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -14.82, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": 10.54, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "angle": -10.45, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.2, "angle": -3.86}]}, "tie_37": {"rotate": [{"angle": -1.03, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.0667, "angle": -1.45, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 1.01, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": 1.24, "curve": 0.243, "c3": 0.674, "c4": 0.69}, {"time": 1.2, "angle": -1.03}]}, "tie_38": {"rotate": [{"angle": -0.33, "curve": 0.379, "c2": 0.53, "c3": 0.746}, {"time": 0.1333, "angle": -2.45, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 4.53, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "angle": 2.51, "curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 1.2, "angle": -0.33}]}, "tie_39": {"rotate": [{"angle": 1.58, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2, "angle": 0.38, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 8.85, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "angle": 2.06, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.2, "angle": 1.58}]}, "tie_40": {"rotate": [{"angle": 9.22, "curve": 0.293, "c2": 0.18, "c3": 0.755}, {"time": 0.2667, "angle": -0.29, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 12.24, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 9.74, "curve": 0.302, "c3": 0.638, "c4": 0.36}, {"time": 1.2, "angle": 9.22}]}, "tie_41": {"rotate": [{"angle": 14.06, "curve": 0.362, "c2": 0.64, "c3": 0.698}, {"time": 0.0333, "angle": 14.82, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 2.84, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 24.65, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "curve": 0.245, "c3": 0.707, "c4": 0.82}, {"time": 1.2, "angle": 14.06}]}, "tie_42": {"rotate": [{"angle": 15.03, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1, "angle": 20.99, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -8.61, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": 13.66, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.2, "angle": 15.03}]}, "tie_44": {"rotate": [{"angle": 5.72, "curve": 0.362, "c2": 0.64, "c3": 0.698}, {"time": 0.0333, "angle": 6.03, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -0.97, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.9333, "curve": 0.245, "c3": 0.707, "c4": 0.82}, {"time": 1.2, "angle": 5.72}]}, "tie_45": {"rotate": [{"angle": 1.33, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.0667, "angle": 1.99, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 2.08, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": -2.29, "curve": 0.243, "c3": 0.674, "c4": 0.69}, {"time": 1.2, "angle": 1.33}]}, "tie_46": {"rotate": [{"angle": 0.43, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1, "angle": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -6.78, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "curve": "stepped"}, {"time": 1, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.2, "angle": 0.43}]}, "tie_47": {"rotate": [{"angle": -1.33, "curve": 0.379, "c2": 0.53, "c3": 0.746}, {"time": 0.1333, "angle": -0.01, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -5.48, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "angle": -3.11, "curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 1.2, "angle": -1.33}]}, "tie_48": {"rotate": [{"angle": -5.1, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2, "angle": 3.92, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -8.67, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "angle": -8.67, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.2, "angle": -5.1}]}, "tie_49": {"rotate": [{"angle": -24.55, "curve": 0.326, "c2": 0.31, "c3": 0.757}, {"time": 0.2333, "angle": 1.27, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -16.65, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": -29.26, "curve": 0.28, "c3": 0.623, "c4": 0.39}, {"time": 1.2, "angle": -24.55}]}, "tie_50": {"rotate": [{"angle": 1.02, "curve": 0.362, "c2": 0.64, "c3": 0.698}, {"time": 0.0333, "angle": 1.13, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 1.46, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": -0.97, "curve": 0.245, "c3": 0.707, "c4": 0.82}, {"time": 1.2, "angle": 1.02}]}, "tie_51": {"rotate": [{"angle": 1.94, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.0667, "angle": 2.61, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -1.19, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 1.62, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": -1.71, "curve": 0.243, "c3": 0.674, "c4": 0.69}, {"time": 1.2, "angle": 1.94}]}, "tie_52": {"rotate": [{"angle": 1.02, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1, "angle": 2.11, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -3.07, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": 2.57, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -1.75, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.2, "angle": 1.02}]}, "tie_53": {"rotate": [{"angle": -0.84, "curve": 0.379, "c2": 0.53, "c3": 0.746}, {"time": 0.1333, "angle": 1.61, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -6.29, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": 4.89, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "angle": -4.12, "curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 1.2, "angle": -0.84}]}, "tie_54": {"rotate": [{"angle": 0.07, "curve": 0.369, "c2": 0.47, "c3": 0.753}, {"time": 0.1667, "angle": 6.31, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -4.18, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 2.72, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": -4.56, "curve": 0.254, "c3": 0.621, "c4": 0.47}, {"time": 1.2, "angle": 0.07}]}, "tie_56": {"rotate": [{"angle": 1.06, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1, "angle": 2.3, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -1.02, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": 1.69, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -2.07, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.2, "angle": 1.06}]}, "tie_57": {"rotate": [{"angle": -0.91, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2, "angle": 4.16, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -2.54, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": 3.78, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "angle": -2.92, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.2, "angle": -0.91}]}, "tie_58": {"rotate": [{"angle": -3.8, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 7.09, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -3.69, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": 6.12, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": -3.8}]}, "tie_59": {"rotate": [{"angle": -1.99, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1, "angle": -4.04, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 2.01, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -6.06, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 3.19, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.2, "angle": -1.99}]}, "tie_60": {"rotate": [{"angle": 4.62, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2, "angle": -5.03, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 4.52, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": -7.03, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "angle": 8.44, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.2, "angle": 4.62}]}, "tie_62": {"rotate": [{"angle": 1.15, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.0667, "angle": 1.77, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -2.03, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 1.15, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": -2.25, "curve": 0.243, "c3": 0.674, "c4": 0.69}, {"time": 1.2, "angle": 1.15}]}, "tie_63": {"rotate": [{"angle": 0.53, "curve": 0.379, "c2": 0.53, "c3": 0.746}, {"time": 0.1333, "angle": 2.55, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -5.13, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": 1.59, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "angle": -2.17, "curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 1.2, "angle": 0.53}]}, "tie_64": {"rotate": [{"angle": -1.41, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2, "angle": 5.76, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -4.76, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": 4.51, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "angle": -4.25, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.2, "angle": -1.41}]}, "tie_65": {"rotate": [{"angle": -4.95, "curve": 0.293, "c2": 0.18, "c3": 0.755}, {"time": 0.2667, "angle": 4.47, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -4.43, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": 4.65, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -5.47, "curve": 0.302, "c3": 0.638, "c4": 0.36}, {"time": 1.2, "angle": -4.95}]}, "tie_66": {"rotate": [{"angle": -2.56, "curve": 0.362, "c2": 0.64, "c3": 0.698}, {"time": 0.0333, "angle": -2.94, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 2.94, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -3.89, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 4.53, "curve": 0.245, "c3": 0.707, "c4": 0.82}, {"time": 1.2, "angle": -2.56}]}, "tie_67": {"rotate": [{"angle": -1.89, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1, "angle": -6.15, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 4.13, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -9.12, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 8.87, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.2, "angle": -1.89}]}, "zong4": {"rotate": [{"angle": 6.95, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -2.09, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -3.34, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -4.88, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": 6.95}]}, "zong5": {"rotate": [{"angle": -0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -2.8, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -12.87, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": -0.3}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": 11.24, "y": -0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "x": -11.18, "y": -1.16, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "x": -11.79, "y": -10.81, "curve": 0.25, "c3": 0.75}, {"time": 1.2}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": 1.02, "curve": 0.25, "c3": 0.75}, {"time": 0.6}]}, "zong6": {"rotate": [{"angle": 5.52, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 8.17, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "curve": "stepped"}, {"time": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": 5.52}], "translate": [{"x": -8.68, "y": 1.05, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": 9.99, "y": -0.04, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "curve": "stepped"}, {"time": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "x": -8.68, "y": 1.05}]}, "zong7": {"rotate": [{"angle": -2.31, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 12.88, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 12.77, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": 2.11, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": -2.31}]}, "zong8": {"rotate": [{"angle": -3.28, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -25.43, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 8.47, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": 6.28, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": -3.28}], "translate": [{"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "x": -12.28, "y": 2.29, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "x": 11.24, "y": 0.02, "curve": 0.25, "c3": 0.75}, {"time": 1.2}], "scale": [{"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "x": 1.059, "curve": 0.25, "c3": 0.75}, {"time": 1.2}]}, "zong9": {"rotate": [{"angle": -3.65, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "curve": "stepped"}, {"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": 5.19, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": -3.65}], "translate": [{"x": -17.03, "y": -5.3, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": -12.73, "y": -8.71, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "curve": "stepped"}, {"time": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "x": -17.03, "y": -5.3}]}, "zong10": {"rotate": [{"angle": 0.37, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -9.51, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 6.29, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -17.82, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": 0.37}], "translate": [{"x": -1.91, "y": -1.01, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "x": -0.7, "y": 29.93, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "x": -1.91, "y": -1.01}]}, "zong12": {"rotate": [{"angle": -26.39, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -9.36, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 8.13, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": 23.47, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": -26.39}], "translate": [{"x": -46.57, "y": 26.44, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "x": -48.73, "y": 44.27, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "x": -52.49, "y": 23.12, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "x": -46.57, "y": 26.44}]}, "zong11": {"rotate": [{"angle": -17.21, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -31.39, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 10.98, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -24.18, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": -17.21}], "translate": [{"x": -7.26, "y": -4.77, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": -8.54, "y": 54.24, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "x": -7.11, "y": 13.72, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "x": -16.12, "y": 44.89, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "x": -7.26, "y": -4.77}]}, "zong13": {"rotate": [{"angle": 31.8, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -26.57, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "curve": "stepped"}, {"time": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": 31.8}], "translate": [{"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "x": 2, "y": -85.35, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "x": 2.89, "y": -123.62, "curve": 0.25, "c3": 0.75}, {"time": 1.2}]}}, "deform": {"default": {"tie_11": {"tie_11": [{"offset": 36, "vertices": [3.41191, 0.64233, 2.62935, 1.40684, 0.86175, 2.85485, 1.037, 3.3133, -1.62419, 3.06836, 6.27705, 0.13144, 5.0367, 1.56586, 2.44981, 4.6711, -1.985, 5.95622, 5.15973, -1.03376, 4.35683, 0.17569, 2.95346, 3.20784, -0.54114, 5.23425, 4.01839, -1.05768, 3.44101, -0.10905, 2.50806, 2.35849, -0.18015, 4.15126, 7.44234, -0.986, 6.18845, 0.74508, 3.84436, 4.90648, -1.263, 7.40025, 4.5414, 1.23694, 3.42731, 2.24435, 0.83263, 4.01129, 1.04317, 4.58971, -2.52669, 3.97098], "curve": 0.25, "c3": 0.75}, {"time": 0.3, "offset": 10, "vertices": [2.33359, 3.072, 0.28703, 3.84213, 0, 0, 0, 0, 0, 0, 11.80954, 12.90185, 14.20844, 10.20065, 5.48811, 16.18717, -6.58906, 15.77121, 6.18016, 7.03189, 2.79179, 8.7339, -3.68216, 8.39748, -4.20882, 8.36189, 1.14127, 0.0239, 0.88034, 0.41669, 0.38476, 0.89479, -0.39281, 1.07178, -0.89014, 0.71463, -4.68478, 5.611, -5.24803, 3.64711, -6.34839, -0.73527, -0.01259, -7.30965, -7.56195, 6.69263, -7.79449, 3.66819, -8.27226, -2.40356, 1.49812, -9.98654, -8.66737, 4.95668, -8.15704, 1.65724, -7.2142, -4.15167, 3.46031, -9.36584, -4.0067, 0.48698, -3.25443, -0.92718, -1.82774, -2.84777, 2.75845, -2.94655, -5.17319, -6.9464, -2.034, -8.30589, 3.9677, -7.5751, 8.24872, -2.64069, 8.42657, 2.002, -5.8371, -11.519, -1.24148, -12.82633, 7.5518, -10.44156, 12.78947, -1.78558, 4.03098, -10.33081, 9.85616, -5.08265, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.50935, 9.7275, -0.05598, 10.3409, 4.23605, 10.4854, 0.29195, 11.30316, -7.09698, 7.65528, -5.37296, 8.95002, -7.7084, 4.73219, -2.88019, 1.07446, -2.87729, 1.08156, -2.59372, 1.6496, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.01349, -0.74236, -0.00298, -0.74245, -0.57346, -0.56855, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.73239, 3.99894, -3.67713, 1.93826, -3.31642, -5.20765, -4.31546, -4.41521, -1.08829, -6.03279, 0.36765, 9.71325, 2.35529, 9.43057, -2.49424, 9.24221], "curve": 0.25, "c3": 0.75}, {"time": 0.6, "offset": 96, "vertices": [-1.83058, -10.25765, 5.41104, -8.9046, 4.27643, -1.29265, 3.19192, -2.26092, 3.88945, 0.41515, -4.09468, -11.47828, 2.59239, -11.30587, -26.23792, 27.9361, -33.80729, 14.61096, 13.01754, 36.04874, -7.92915, 23.70919, -9.85443, 22.97536, 17.54318, 17.81215, 0.76155, -6.02798, 1.25531, -5.94491, -5.02907, -3.40947, 0.64597, -6.72433, -5.98101, -3.1401, 2.09508, -7.92875, -6.54807, -4.9373, -3.44125, -7.44394, 4.25743, -8.76593, -6.5056, -7.25553, -2.30923, -9.46747, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.32869, -5.47972, -4.19251, -4.22763, 0.05298, -1.3122, 0.13535, -1.30626, -1.15812, -0.61919, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.24115, 6.19344, 1.01033, 6.0771, 6.2702, -3.82218, 4.30482, -5.18782, -2.81616, 5.3497, -1.66794, 5.81109, -1.22688, 5.86157, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.18204, -4.50256, 0.46484, -4.48219, -1.63675, -6.05932, -6.51909, -2.00088, -4.31733, 5.27918, -0.21411, 6.59199, 6.02143, 2.69148, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.53897, -5.24668, 1.86613, -5.13939, -4.05234, -3.67069, 2.32193, -9.53624, -7.95046, -5.75508, 6.06493, -16.54338, -13.02228, -11.86951, -5.87553, -16.6115], "curve": 0.25, "c3": 0.75}, {"time": 0.9, "offset": 20, "vertices": [7.55577, -2.09539, 7.51443, -2.23898, 5.88963, -2.19291, 6.04737, 1.71053, 0.77876, -3.7826, 0.57082, -3.79232, 2.70116, -2.72237, 3.28711, 2.02712, 6.75781, 0.14153, 5.28801, 0.05414, 4.23438, 3.16799, -2.44783, 6.30031, -2.25934, 6.37043, 6.75786, 0.14153, 5.28805, 0.05414, 4.2344, 3.16802, -2.25934, 6.37049, 14.31371, -1.95382, 11.17775, -2.13872, 10.28182, 4.87862, -2.64615, 14.20185, 15.86243, -4.17502, 12.36684, -4.37976, 12.56523, 3.77311, -1.02448, 16.37045, 6.77339, -0.60933, 5.29259, -0.69686, 4.68178, 2.56481, -1.55139, 6.62133, 15.81535, -1.92238, 12.35279, -2.1267, 11.22272, 5.58257, -3.61137, 15.51675, -3.14807, 15.61746, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4.06326, 3.59645, 3.2676, 3.54356, 0.54275, 4.78959, -4.86447, -20.44874, 8.6784, -18.9102, -4.62756, -33.60913, 16.5444, -29.46418, -29.17254, -17.31935, 13.85506, 4.61286, 11.99997, 8.31991, 12.23919, -7.96204, 3.01189, -19.6184, 8.40387, -17.98182, -13.46968, -14.57787, -4.24907, -4.71707, -5.97354, 2.15057, 3.45592, -13.08546, -10.80745, -8.14667, -8.03483, -10.89082, -2.58604, -15.33061, -15.17039, -3.40203, -13.56973, -7.58765, 2.22862, -7.05977, -5.6931, -4.73227, -4.10678, -6.15945, 6.70297, -13.74798, -10.1931, -11.40316, -6.51716, -13.83681, 3.16522, -12.16463, -10.0649, -7.5294, -7.49911, -10.08736, 3.23016, -8.0937, -6.27162, -6.05033, -4.28532, -7.58782, 0, 0, 0, 0, 1.83234, 12.22706, -1.1109, 12.31346, 10.97929, 5.68441, 11.94321, 17.56053, 7.4521, 19.88631, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4.67244, -0.71339, 3.89353, -0.77376, 3.87837, -0.10348, 3.11401, -0.15362, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5.08884, 15.16857, 1.35736, 15.94136, -5.37042, 5.56354, 4.77736, 5.06781, 6.49781, -2.50607, 7.54553, 7.70153, 9.98262, -4.0729, 0, 0, 0, 0, 0, 0, 0.74945, -3.71084, 0.60406, -3.70615, -0.63332, -5.21161, -0.7326, -5.19862, -0.54831, -5.20298, 0.10899, -5.19603, 0.00986, -5.19719, 0.03255, -5.19699, 0.88229, -6.66508, 0.62265, -6.67593, 4.44671, -5.0182, 0, 0, 4.12952, 8.06287, 2.10585, 8.81054, 0.52679, 1.25689, 0.21436, 1.34564, 1.32675, 0.3106, 0.49821, 7.14637, 6.80421, 2.241, -0.96902, 5.70647, 4.91636, 3.05517, 3.84089, 4.33046], "curve": 0.25, "c3": 0.75}, {"time": 1.2, "offset": 36, "vertices": [3.41191, 0.64233, 2.62935, 1.40684, 0.86175, 2.85485, 1.037, 3.3133, -1.62419, 3.06836, 6.27705, 0.13144, 5.0367, 1.56586, 2.44981, 4.6711, -1.985, 5.95622, 5.15973, -1.03376, 4.35683, 0.17569, 2.95346, 3.20784, -0.54114, 5.23425, 4.01839, -1.05768, 3.44101, -0.10905, 2.50806, 2.35849, -0.18015, 4.15126, 7.44234, -0.986, 6.18845, 0.74508, 3.84436, 4.90648, -1.263, 7.40025, 4.5414, 1.23694, 3.42731, 2.24435, 0.83263, 4.01129, 1.04317, 4.58971, -2.52669, 3.97098]}]}}}}}}