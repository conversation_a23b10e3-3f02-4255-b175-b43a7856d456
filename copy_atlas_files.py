#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Atlas文件复制脚本
根据json文件数量复制atlas文件并重命名

需求：
1. 寻找后缀atlas文件，以他为基础
2. 复制多个该文件，数量取决于当前.json文件的数量，忽略已存在和atlas同名的json
3. 并分别根据每个对应json，重命名对应的复制出来的文件
"""

import os
import shutil
import sys
from pathlib import Path


def find_atlas_files(directory="."):
    """查找目录中的.atlas文件"""
    atlas_files = []
    for file in os.listdir(directory):
        if file.endswith('.atlas'):
            atlas_files.append(file)
    return atlas_files


def find_json_files(directory="."):
    """查找目录中的.json文件"""
    json_files = []
    for file in os.listdir(directory):
        if file.endswith('.json'):
            json_files.append(file)
    return json_files


def get_base_name(filename):
    """获取文件的基础名称（不包含扩展名）"""
    return os.path.splitext(filename)[0]


def copy_atlas_for_json(atlas_file, json_files, directory=".", overwrite=False):
    """
    根据json文件复制atlas文件

    Args:
        atlas_file: atlas文件名
        json_files: json文件列表
        directory: 工作目录
        overwrite: 是否覆盖已存在的文件
    """
    atlas_base_name = get_base_name(atlas_file)
    atlas_path = os.path.join(directory, atlas_file)

    # 过滤掉与atlas同名的json文件
    filtered_json_files = [
        json_file for json_file in json_files
        if get_base_name(json_file) != atlas_base_name
    ]

    print(f"使用的atlas文件: {atlas_file}")
    print(f"找到json文件: {json_files}")
    print(f"过滤后的json文件: {filtered_json_files}")
    print(f"需要复制的数量: {len(filtered_json_files)}")
    print(f"覆盖模式: {'是' if overwrite else '否'}")
    print()

    copied_count = 0
    skipped_count = 0

    # 为每个json文件创建对应的atlas副本
    for json_file in filtered_json_files:
        json_base_name = get_base_name(json_file)
        new_atlas_name = f"{json_base_name}.atlas"
        new_atlas_path = os.path.join(directory, new_atlas_name)

        try:
            # 检查目标文件是否已存在
            if os.path.exists(new_atlas_path) and not overwrite:
                print(f"跳过: 文件 {new_atlas_name} 已存在")
                skipped_count += 1
                continue

            # 复制文件
            shutil.copy2(atlas_path, new_atlas_path)
            action = "覆盖" if os.path.exists(new_atlas_path) else "复制"
            print(f"成功{action}: {atlas_file} -> {new_atlas_name}")
            copied_count += 1

        except Exception as e:
            print(f"复制文件时出错: {atlas_file} -> {new_atlas_name}")
            print(f"错误信息: {e}")

    print()
    print(f"操作总结: 成功复制 {copied_count} 个文件，跳过 {skipped_count} 个文件")


def select_atlas_file(atlas_files):
    """让用户选择要使用的atlas文件"""
    if len(atlas_files) == 1:
        return atlas_files[0]

    print("找到多个atlas文件，请选择要使用的基础文件:")
    for i, atlas_file in enumerate(atlas_files, 1):
        print(f"{i}. {atlas_file}")

    while True:
        try:
            choice = input(f"请输入选择 (1-{len(atlas_files)}): ").strip()
            index = int(choice) - 1
            if 0 <= index < len(atlas_files):
                return atlas_files[index]
            else:
                print(f"请输入1到{len(atlas_files)}之间的数字")
        except ValueError:
            print("请输入有效的数字")
        except KeyboardInterrupt:
            print("\n操作已取消")
            sys.exit(0)


def ask_overwrite():
    """询问用户是否覆盖已存在的文件"""
    while True:
        try:
            choice = input("是否覆盖已存在的文件? (y/n): ").strip().lower()
            if choice in ['y', 'yes', '是', 'Y']:
                return True
            elif choice in ['n', 'no', '否', 'N']:
                return False
            else:
                print("请输入 y/yes/是 或 n/no/否")
        except KeyboardInterrupt:
            print("\n操作已取消")
            sys.exit(0)


def main():
    """主函数"""
    print("=== Atlas文件复制脚本 ===")
    print(f"Python版本: {sys.version}")
    print(f"工作目录: {os.getcwd()}")
    print()

    # 查找atlas文件
    atlas_files = find_atlas_files()
    if not atlas_files:
        print("错误: 未找到任何.atlas文件")
        return

    # 选择atlas文件
    atlas_file = select_atlas_file(atlas_files)
    print(f"选择的atlas文件: {atlas_file}")
    print()

    # 查找json文件
    json_files = find_json_files()
    if not json_files:
        print("错误: 未找到任何.json文件")
        return

    # 询问是否覆盖
    overwrite = ask_overwrite()
    print()

    # 执行复制操作
    copy_atlas_for_json(atlas_file, json_files, overwrite=overwrite)

    print()
    print("=== 操作完成 ===")


if __name__ == "__main__":
    main()
